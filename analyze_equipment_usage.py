#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机台使用情况分析脚本
"""

# 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import pymysql
import logging
from datetime import datetime

# 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('equipment_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('EquipmentAnalysis')

def analyze_equipment_usage():
    """分析机台使用情况"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            # 获取数据库连接
            connection = pymysql.connect(
                host='localhost',
                port=3306,
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            cursor = connection.cursor()
            
            print("=" * 80)
            print("🔍 机台使用情况分析报告")
            print("=" * 80)
            
            # 1. 统计调度结果
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_scheduled_batches,
                    COUNT(DISTINCT HANDLER_ID) as used_equipment_count
                FROM lotprioritydone 
                WHERE HANDLER_ID IS NOT NULL AND HANDLER_ID != ''
            """)
            result = cursor.fetchone()
            scheduled_batches = result[0]
            used_equipment = result[1]
            
            print(f"📊 调度结果统计:")
            print(f"   已排产批次数: {scheduled_batches}")
            print(f"   使用的机台数: {used_equipment}")
            
            # 2. 统计系统中的总机台数
            cursor.execute("SELECT COUNT(DISTINCT HANDLER_ID) FROM eqp_status")
            total_equipment = cursor.fetchone()[0]
            
            print(f"   系统总机台数: {total_equipment}")
            print(f"   机台利用率: {used_equipment}/{total_equipment} = {used_equipment/total_equipment*100:.1f}%")
            
            # 3. 分析未使用的机台
            cursor.execute("""
                SELECT DISTINCT es.HANDLER_ID, es.STATUS
                FROM eqp_status es
                WHERE es.HANDLER_ID NOT IN (
                    SELECT DISTINCT HANDLER_ID 
                    FROM lotprioritydone 
                    WHERE HANDLER_ID IS NOT NULL AND HANDLER_ID != ''
                )
                ORDER BY es.HANDLER_ID
            """)
            
            unused_equipment = cursor.fetchall()
            print(f"\n🚫 未使用的机台 ({len(unused_equipment)}个):")
            
            status_counts = {}
            for handler_id, status in unused_equipment:
                if status not in status_counts:
                    status_counts[status] = []
                status_counts[status].append(handler_id)
            
            for status, equipment_list in status_counts.items():
                print(f"   状态 '{status}': {len(equipment_list)}个机台")
                # 只显示前5个机台ID
                sample_ids = equipment_list[:5]
                print(f"      示例: {', '.join(sample_ids)}")
                if len(equipment_list) > 5:
                    print(f"      ... 还有{len(equipment_list)-5}个")
            
            # 4. 分析待排产批次的情况
            cursor.execute("SELECT COUNT(*) FROM et_wait_lot")
            total_waiting_batches = cursor.fetchone()[0]
            
            print(f"\n📋 待排产批次情况:")
            print(f"   待排产批次总数: {total_waiting_batches}")
            print(f"   已排产批次数: {scheduled_batches}")
            print(f"   未排产批次数: {total_waiting_batches - scheduled_batches}")
            
            # 5. 分析机台的批次分布
            cursor.execute("""
                SELECT 
                    HANDLER_ID, 
                    COUNT(*) as batch_count,
                    GROUP_CONCAT(DISTINCT PROD_ID ORDER BY PROD_ID) as prod_types
                FROM lotprioritydone 
                WHERE HANDLER_ID IS NOT NULL AND HANDLER_ID != ''
                GROUP BY HANDLER_ID 
                ORDER BY batch_count DESC
                LIMIT 10
            """)
            
            print(f"\n🏭 机台批次分布 (前10个最忙的机台):")
            for row in cursor.fetchall():
                handler_id, batch_count, prod_types = row
                print(f"   {handler_id}: {batch_count}个批次, 产品类型: {prod_types}")
            
            # 6. 分析调度失败的原因
            cursor.execute("""
                SELECT COUNT(*) FROM scheduling_failed_lots
            """)
            failed_batches = cursor.fetchone()[0]
            
            if failed_batches > 0:
                print(f"\n❌ 调度失败情况:")
                print(f"   调度失败的批次数: {failed_batches}")
                
                cursor.execute("""
                    SELECT failure_reason, COUNT(*) as count
                    FROM scheduling_failed_lots
                    GROUP BY failure_reason
                    ORDER BY count DESC
                """)
                
                print("   失败原因分布:")
                for reason, count in cursor.fetchall():
                    print(f"      {reason}: {count}个批次")
            
            # 7. 检查测试规格匹配情况
            cursor.execute("""
                SELECT 
                    COUNT(DISTINCT ewl.LOT_ID) as total_lots,
                    COUNT(DISTINCT CASE WHEN ets.PROD_ID IS NOT NULL THEN ewl.LOT_ID END) as lots_with_spec
                FROM et_wait_lot ewl
                LEFT JOIN et_ft_test_spec ets ON ewl.PROD_ID = ets.PROD_ID
            """)
            
            result = cursor.fetchone()
            total_lots, lots_with_spec = result
            
            print(f"\n🔬 测试规格匹配情况:")
            print(f"   总待排产批次: {total_lots}")
            print(f"   有测试规格的批次: {lots_with_spec}")
            print(f"   无测试规格的批次: {total_lots - lots_with_spec}")
            
            cursor.close()
            connection.close()
            
            print("=" * 80)
            print("分析完成! 详细日志保存在 equipment_analysis.log")
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_equipment_usage()
    print("🎉 分析: 完成" if success else "❌ 分析: 失败")

if __name__ == "__main__":
    main()