
// 前端缓存优化脚本
// 添加到 failed_lots.html 的 JavaScript 部分

// 🚀 缓存管理
let dataCache = new Map();
let cacheTimeout = 3 * 60 * 1000; // 3分钟缓存

// 优化的数据加载函数
async function loadFailedLotsOptimized() {
    try {
        const cacheKey = `failed_lots_${isCurrentOnly}_${Date.now()}`;
        const cachedData = dataCache.get(`failed_lots_${isCurrentOnly}`);
        
        // 检查缓存
        if (cachedData && (Date.now() - cachedData.timestamp < cacheTimeout)) {
            console.log('✅ 使用缓存数据');
            processFailedLotsData(cachedData.data);
            return;
        }
        
        showLoading();
        
        // 优先使用缓存版本的API
        const url = `/api/v2/production/get-failed-lots-cached?current_only=${isCurrentOnly}`;
        console.log(`🔍 请求失败批次数据（缓存版本）: ${url}`);
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('📊 失败批次API响应（缓存版本）:', result);
        
        if (result.success) {
            // 更新缓存
            dataCache.set(`failed_lots_${isCurrentOnly}`, {
                data: result,
                timestamp: Date.now()
            });
            
            processFailedLotsData(result);
            console.log(`✅ 成功加载 ${result.data.failed_lots.length} 条失败批次记录（缓存版本）`);
        } else {
            throw new Error(result.message || '获取失败批次数据失败');
        }
    } catch (error) {
        console.error('❌ 加载失败批次数据出错（尝试备用API）:', error);
        // 备用：使用原始API
        loadFailedLotsOriginal();
    } finally {
        hideLoading();
    }
}

// 处理失败批次数据的函数
function processFailedLotsData(result) {
    allFailedLots = result.data.failed_lots || [];
    
    // 更新统计信息
    if (result.data.summary) {
        updateStats(result.data.summary);
    }
    
    // 更新数据源信息
    if (result.debug_info) {
        updateDataSourceInfo(result.debug_info);
    }
    
    // 💡 在数据加载完成后生成筛选选项
    updateFilterOptionsFromData(allFailedLots);
    
    // 应用筛选和渲染
    filterLots();
}

// 备用的原始API加载函数
async function loadFailedLotsOriginal() {
    const url = `/api/v2/production/get-failed-lots-from-logs?current_only=${isCurrentOnly}`;
    // ... 原有的加载逻辑
}

// 缓存失效函数
async function invalidateCache() {
    try {
        dataCache.clear();
        console.log('🧹 本地缓存已清理');
        
        // 调用服务端缓存清理
        const response = await fetch('/api/v2/production/invalidate-failed-lots-cache', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('🧹 服务端缓存清理结果:', result);
        }
        
        // 重新加载数据
        loadFailedLotsOptimized();
        
    } catch (error) {
        console.error('⚠️ 缓存清理失败:', error);
        // 即使缓存清理失败，也要重新加载数据
        loadFailedLotsOptimized();
    }
}

// 修改刷新数据函数
function refreshData() {
    invalidateCache();
}

// 在页面加载时使用优化版本
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔥 失败批次页面初始化（缓存优化版本）...');
    initializeEventListeners();
    initializeSorting();
    // 使用缓存优化的加载函数
    loadFailedLotsOptimized();
});
