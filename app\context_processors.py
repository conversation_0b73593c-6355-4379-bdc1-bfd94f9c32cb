#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask上下文处理器 - 为模板提供全局数据
"""

from flask import current_app, g
from flask_login import current_user
from app.utils.menu_renderer import get_user_menu_html, get_user_menu_data
from app.config.menu_config import MENU_CONFIG_VERSION, MENU_ID_MAP
import time


def register_context_processors(app):
    """注册所有上下文处理器"""
    
    @app.context_processor
    def inject_menu_data():
        """注入菜单相关数据到所有模板"""
        try:
            return _inject_menu_data_impl()
        except Exception as e:
            from flask import current_app
            current_app.logger.error(f"菜单数据注入失败: {e}")
            from app.config.menu_config import MENU_CONFIG_VERSION
            return {
                'user_menu_html': '<div class="alert alert-warning text-center" style="margin: 20px;"><i class="fas fa-exclamation-triangle"></i><br><small>菜单配置加载失败，请刷新页面重试</small><br><button class="btn btn-sm btn-primary mt-2" onclick="location.reload()"><i class="fas fa-redo"></i> 重新加载</button></div>',
                'user_permissions': [],
                'menu_config_version': MENU_CONFIG_VERSION,
                'menu_error': str(e)
            }
    
    def _inject_menu_data_impl():
        """注入菜单相关数据到所有模板"""
        if not current_user.is_authenticated:
            return {
                'user_menu_html': '',
                'user_permissions': [],
                'menu_config_version': MENU_CONFIG_VERSION
            }
        
        # 使用缓存避免重复计算
        cache_key = f"menu_data_{current_user.username}_{current_user.role}"
        
        # 检查是否已经在当前请求中计算过
        if hasattr(g, cache_key):
            return getattr(g, cache_key)
        
        # 计算菜单数据
        menu_data = {
            'user_menu_html': get_user_menu_html(current_user),
            'user_permissions': current_user.get_permissions(),
            'user_menu_data': get_user_menu_data(current_user),
            'menu_config_version': MENU_CONFIG_VERSION,
            'menu_id_map': MENU_ID_MAP,
            'is_admin': current_user.role == 'admin'
        }
        
        # 缓存到请求上下文
        setattr(g, cache_key, menu_data)
        
        return menu_data
    
    @app.context_processor
    def inject_system_info():
        """注入系统信息"""
        # 🏷️ 从统一版本号管理中心获取版本信息
        try:
            from app.services import APP_VERSION
            app_version = APP_VERSION
        except ImportError:
            # 降级方案：从配置或设置默认值
            app_version = getattr(current_app.config, 'APP_VERSION', '2.3.6')
        
        return {
            'app_version': app_version,
            'app_name': 'APS 车规芯片终测智能调度平台',
            'current_timestamp': int(time.time())
        }
    
    @app.context_processor
    def inject_user_info():
        """注入用户信息"""
        if current_user.is_authenticated:
            return {
                'current_user_data': {
                    'username': current_user.username,
                    'role': current_user.role,
                    'email': getattr(current_user, 'email', ''),
                    'last_login': getattr(current_user, 'last_login', None)
                }
            }
        return {'current_user_data': None} 