#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析YX0125GA0419批次的LSTR工序匹配问题
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('analyze_yx0125ga0419.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('YX0125GA0419Analysis')

def analyze_yx0125ga0419_batch():
    """分析YX0125GA0419批次的LSTR工序匹配问题"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入排产服务和数据管理器
            from app.services.real_scheduling_service import RealSchedulingService
            from app.services.data_source_manager import DataSourceManager
            
            data_manager = DataSourceManager()
            scheduling_service = RealSchedulingService()
            
            # 目标批次ID
            target_lot_id = "YX0125GA0419"
            
            logger.info(f"🔍 开始分析批次: {target_lot_id}")
            
            # 1. 获取批次基本信息
            logger.info("📋 Step 1: 获取批次基本信息")
            wait_lots = data_manager.get_table_data('ET_WAIT_LOT')
            target_lot = None
            
            for lot in wait_lots.get('data', []):
                if lot.get('LOT_ID') == target_lot_id:
                    target_lot = lot
                    break
            
            if not target_lot:
                logger.error(f"❌ 未找到批次: {target_lot_id}")
                return False
                
            logger.info(f"✅ 找到目标批次:")
            logger.info(f"   LOT_ID: {target_lot.get('LOT_ID')}")
            logger.info(f"   DEVICE: {target_lot.get('DEVICE')}")
            logger.info(f"   STAGE: {target_lot.get('STAGE')}")
            logger.info(f"   PKG_PN: {target_lot.get('PKG_PN')}")
            logger.info(f"   GOOD_QTY: {target_lot.get('GOOD_QTY')}")
            
            # 2. 获取批次配置需求
            logger.info(f"\n📋 Step 2: 获取批次配置需求")
            preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
            lot_requirements = scheduling_service.get_lot_configuration_requirements_optimized(target_lot, preloaded_data)
            
            if lot_requirements:
                logger.info(f"✅ 批次配置需求:")
                for key, value in lot_requirements.items():
                    logger.info(f"   {key}: {value}")
            else:
                logger.error(f"❌ 未获取到批次配置需求")
                return False
            
            # 3. 查找可用的LSTR设备
            logger.info(f"\n📋 Step 3: 查找可用的LSTR设备")
            equipment_status = preloaded_data.get('equipment_status', [])
            lstr_equipment = []
            
            for equipment in equipment_status:
                handler_id = equipment.get('HANDLER_ID', '')
                eqp_class = equipment.get('EQP_CLASS', '')
                stage = equipment.get('STAGE', '')
                status = equipment.get('STATUS', '')
                handler_config = equipment.get('HANDLER_CONFIG', '')
                
                # 查找LSTR相关设备
                if ('LSTR' in stage.upper() or 'LSTR' in eqp_class.upper() or 
                    'TAPE' in stage.upper() or 'REEL' in stage.upper()):
                    lstr_equipment.append({
                        'HANDLER_ID': handler_id,
                        'EQP_CLASS': eqp_class,
                        'STAGE': stage,
                        'STATUS': status,
                        'HANDLER_CONFIG': handler_config,
                        'DEVICE': equipment.get('DEVICE', ''),
                        'KIT_PN': equipment.get('KIT_PN', ''),
                        'HB_PN': equipment.get('HB_PN', ''),
                        'TB_PN': equipment.get('TB_PN', '')
                    })
            
            logger.info(f"✅ 找到 {len(lstr_equipment)} 台LSTR相关设备:")
            for i, eqp in enumerate(lstr_equipment, 1):
                logger.info(f"   {i}. {eqp['HANDLER_ID']} - {eqp['STATUS']} - {eqp['STAGE']} - {eqp['HANDLER_CONFIG']}")
            
            # 4. 逐一测试设备匹配
            logger.info(f"\n📋 Step 4: 逐一测试设备匹配")
            for i, equipment in enumerate(lstr_equipment, 1):
                logger.info(f"\n🔧 测试设备 {i}: {equipment['HANDLER_ID']}")
                
                # 调用排产服务的设备匹配评分
                score, match_type, changeover_time = scheduling_service.calculate_equipment_match_score_optimized(
                    lot_requirements, equipment, preloaded_data
                )
                
                logger.info(f"   匹配结果: 评分={score}, 类型={match_type}, 改机时间={changeover_time}分钟")
                logger.info(f"   批次HANDLER_CONFIG: {lot_requirements.get('HANDLER_CONFIG')}")
                logger.info(f"   设备HANDLER_CONFIG: {equipment.get('HANDLER_CONFIG')}")
                logger.info(f"   批次DEVICE: {lot_requirements.get('DEVICE')}")
                logger.info(f"   设备DEVICE: {equipment.get('DEVICE')}")
                logger.info(f"   批次STAGE: {lot_requirements.get('STAGE')}")
                logger.info(f"   设备STAGE: {equipment.get('STAGE')}")
                
                if score > 0:
                    logger.info(f"   ✅ 匹配成功!")
                else:
                    logger.info(f"   ❌ 匹配失败!")
            
            # 5. 分析匹配失败的可能原因
            logger.info(f"\n📋 Step 5: 分析匹配失败的可能原因")
            
            # 检查HANDLER_CONFIG匹配情况
            req_handler_config = lot_requirements.get('HANDLER_CONFIG', '')
            logger.info(f"批次要求的HANDLER_CONFIG: '{req_handler_config}'")
            
            config_matches = []
            for equipment in lstr_equipment:
                eqp_handler_config = equipment.get('HANDLER_CONFIG', '')
                match = (req_handler_config.strip().upper() == eqp_handler_config.strip().upper()) if req_handler_config and eqp_handler_config else False
                config_matches.append({
                    'HANDLER_ID': equipment['HANDLER_ID'],
                    'CONFIG': eqp_handler_config,
                    'MATCH': match
                })
            
            logger.info("HANDLER_CONFIG匹配情况:")
            for item in config_matches:
                status = "✅ 匹配" if item['MATCH'] else "❌ 不匹配"
                logger.info(f"   {item['HANDLER_ID']}: '{item['CONFIG']}' - {status}")
            
            # 6. 检查测试规范配置
            logger.info(f"\n📋 Step 6: 检查测试规范配置")
            test_specs = preloaded_data.get('test_specs', [])
            
            target_device = target_lot.get('DEVICE')
            target_stage = target_lot.get('STAGE')
            
            matching_specs = []
            for spec in test_specs:
                if (spec.get('DEVICE') == target_device and 
                    spec.get('STAGE') == target_stage and
                    spec.get('APPROVAL_STATE') == 'Released'):
                    matching_specs.append(spec)
            
            logger.info(f"找到 {len(matching_specs)} 个匹配的测试规范:")
            for spec in matching_specs:
                logger.info(f"   DEVICE: {spec.get('DEVICE')}")
                logger.info(f"   STAGE: {spec.get('STAGE')}")
                logger.info(f"   KIT_PN: {spec.get('KIT_PN')}")
                logger.info(f"   HB_PN: {spec.get('HB_PN')}")
                logger.info(f"   TB_PN: {spec.get('TB_PN')}")
                logger.info(f"   HANDLER_CONFIG: {spec.get('HANDLER_CONFIG')}")
                logger.info(f"   EQP_CLASS: {spec.get('EQP_CLASS')}")
                logger.info("   ---")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_yx0125ga0419_batch()
    print("🎉 分析完成" if success else "❌ 分析失败")

if __name__ == "__main__":
    main()