#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APScheduler熔断机制辅助函数
防止任务重叠执行和连接池耗尽
"""

import time
import logging
from typing import Dict

logger = logging.getLogger(__name__)

def _increment_task_failure(task_id: str):
    """增加任务失败计数"""
    from app.services.background_scheduler_service import _task_failure_counts
    
    if task_id not in _task_failure_counts:
        _task_failure_counts[task_id] = {'count': 0, 'last_failure': 0, 'last_reset': time.time()}
    
    _task_failure_counts[task_id]['count'] += 1
    _task_failure_counts[task_id]['last_failure'] = time.time()
    
    logger.warning(f"⚠️ 任务 {task_id} 失败计数增加: {_task_failure_counts[task_id]['count']}")

def _reset_task_failure(task_id: str):
    """重置任务失败计数"""
    from app.services.background_scheduler_service import _task_failure_counts
    
    if task_id in _task_failure_counts:
        _task_failure_counts[task_id] = {'count': 0, 'last_failure': 0, 'last_reset': time.time()}
        logger.info(f"🔄 任务 {task_id} 失败计数已重置")

def check_circuit_breaker_status() -> Dict:
    """检查熔断器状态"""
    from app.services.background_scheduler_service import _task_failure_counts, _max_failures_per_task
    
    status = {
        'total_tasks': len(_task_failure_counts),
        'failed_tasks': 0,
        'circuit_breaker_active': [],
        'task_details': {}
    }
    
    for task_id, failure_info in _task_failure_counts.items():
        is_circuit_broken = failure_info['count'] >= _max_failures_per_task
        if is_circuit_broken:
            status['failed_tasks'] += 1
            status['circuit_breaker_active'].append(task_id)
        
        status['task_details'][task_id] = {
            'failure_count': failure_info['count'],
            'last_failure': failure_info['last_failure'],
            'circuit_breaker_active': is_circuit_broken
        }
    
    return status