#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复失败批次页面的缓存和数据连接问题
解决前端页面显示问题，优化数据加载和缓存机制

关键修复：
1. 集成统一缓存系统到失败批次API
2. 修复字符集转换问题
3. 优化前端数据刷新机制
4. 添加缓存失效处理
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_failed_lots_cache_issues.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('FixFailedLotsCacheIssues')

def create_optimized_api_version():
    """创建优化的失败批次API版本，集成统一缓存系统"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 读取原始API文件
            api_file_path = 'app/api_v2/production/done_lots_api.py'
            
            with open(api_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 🔧 修复1：移除字符集转换问题
            old_sql_pattern = """LEFT JOIN et_wait_lot ewl ON sfl.lot_id COLLATE utf8mb4_unicode_ci = ewl.LOT_ID COLLATE utf8mb4_unicode_ci"""
            new_sql_pattern = """LEFT JOIN et_wait_lot ewl ON sfl.lot_id = ewl.LOT_ID"""
            
            if old_sql_pattern in content:
                content = content.replace(old_sql_pattern, new_sql_pattern)
                logger.info("🔧 修复字符集转换问题：移除COLLATE语句")
            
            # 🔧 修复2：添加缓存系统集成导入
            cache_import = """
# 🚀 缓存系统集成
from app.utils.api_cache_adapter import get_cached_table_data
from app.utils.simple_cache import cache_result, cache_get, cache_set"""
            
            if "from app.utils.api_cache_adapter import get_cached_table_data" not in content:
                # 在文件顶部添加缓存导入
                import_position = content.find("from flask import")
                if import_position != -1:
                    content = content[:import_position] + cache_import + "\n" + content[import_position:]
                    logger.info("🔧 添加缓存系统导入")
            
            # 🔧 修复3：添加缓存版本的失败批次获取函数
            cached_function = '''
def get_failed_lots_from_logs_cached():
    """失败批次获取的缓存版本 - 使用统一缓存系统"""
    try:
        import time
        from app.utils.db_connection_pool import get_db_connection_context
        
        # 🔥 获取查询参数
        current_only = request.args.get('current_only', 'false').lower() == 'true'
        hours_limit = int(request.args.get('hours', '24'))  # 默认24小时内的失败记录
        
        logger.info(f"🔍 缓存版本：获取失败批次数据 (current_only={current_only}, hours_limit={hours_limit})")
        
        # 🚀 使用缓存键
        cache_key = f"failed_lots_{current_only}_{hours_limit}"
        
        def fetch_failed_lots_data():
            """实际获取失败批次数据的函数"""
            try:
                with get_db_connection_context() as conn:
                    cursor = conn.cursor()
                    
                    # 检查表是否存在
                    check_table_sql = """
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'scheduling_failed_lots'
                    """
                    cursor.execute(check_table_sql)
                    check_result = cursor.fetchone()
                    
                    if isinstance(check_result, dict):
                        table_exists = list(check_result.values())[0] > 0
                    else:
                        table_exists = check_result[0] > 0
                    
                    if not table_exists:
                        return {
                            'success': False,
                            'error': 'scheduling_failed_lots表不存在'
                        }
                    
                    # 🔧 优化的SQL查询（移除字符集转换）
                    if current_only:
                        query_sql = """
                        SELECT
                            sfl.lot_id,
                            sfl.device,
                            sfl.stage,
                            sfl.good_qty,
                            sfl.failure_reason,
                            sfl.failure_details,
                            sfl.suggestion,
                            sfl.session_id,
                            sfl.timestamp,
                            -- 从ET_WAIT_LOT表获取关键字段（移除字符集转换）
                            ewl.LOT_TYPE,
                            ewl.PKG_PN
                        FROM scheduling_failed_lots sfl
                        LEFT JOIN et_wait_lot ewl ON sfl.lot_id = ewl.LOT_ID
                        WHERE sfl.timestamp >= DATE_SUB(NOW(), INTERVAL %s HOUR)
                        ORDER BY sfl.timestamp DESC
                        LIMIT 1000
                        """
                        cursor.execute(query_sql, (hours_limit,))
                    else:
                        query_sql = """
                        SELECT
                            sfl.lot_id,
                            sfl.device,
                            sfl.stage,
                            sfl.good_qty,
                            sfl.failure_reason,
                            sfl.failure_details,
                            sfl.suggestion,
                            sfl.session_id,
                            sfl.timestamp,
                            -- 从ET_WAIT_LOT表获取关键字段（移除字符集转换）
                            ewl.LOT_TYPE,
                            ewl.PKG_PN
                        FROM scheduling_failed_lots sfl
                        LEFT JOIN et_wait_lot ewl ON sfl.lot_id = ewl.LOT_ID
                        ORDER BY sfl.timestamp DESC
                        LIMIT 1000
                        """
                        cursor.execute(query_sql)
                    
                    results = cursor.fetchall()
                    
                    if not results:
                        return {
                            'success': True,
                            'data': {
                                'failed_lots': [],
                                'total_count': 0,
                                'summary': {
                                    'total_failed': 0,
                                    'config_missing': 0,
                                    'equipment_incompatible': 0,
                                    'other_reasons': 0
                                }
                            },
                            'message': '暂无失败批次数据'
                        }
                    
                    # 处理查询结果
                    failed_lots = []
                    summary_stats = {
                        'total_failed': 0,
                        'config_missing': 0,
                        'equipment_incompatible': 0,
                        'other_reasons': 0
                    }
                    
                    for row in results:
                        lot_id = row['lot_id']
                        device = row['device'] or ''
                        stage = row['stage'] or ''
                        good_qty = row['good_qty'] or 0
                        failure_reason = row['failure_reason'] or '未知原因'
                        failure_details = row['failure_details'] or ''
                        suggestion = row.get('suggestion', '') or generate_suggestion(failure_reason, failure_details, device, stage)
                        session_id = row.get('session_id', '')
                        timestamp = row['timestamp']

                        # 获取ET_WAIT_LOT表的关键信息
                        lot_type = row.get('LOT_TYPE', '') or ''
                        pkg_pn = row.get('PKG_PN', '') or ''
                        
                        # 统计失败原因
                        summary_stats['total_failed'] += 1
                        if '配置' in failure_reason or 'config' in failure_reason.lower():
                            summary_stats['config_missing'] += 1
                        elif '设备' in failure_reason or '不兼容' in failure_reason:
                            summary_stats['equipment_incompatible'] += 1
                        else:
                            summary_stats['other_reasons'] += 1
                        
                        failure_info = {
                            'LOT_ID': lot_id,
                            'DEVICE': device,
                            'STAGE': stage,
                            'LOT_TYPE': lot_type,
                            'PKG_PN': pkg_pn,
                            'GOOD_QTY': good_qty,
                            'failure_reason': failure_reason,
                            'suggestion': suggestion,
                            'timestamp': timestamp.isoformat() if timestamp else datetime.now().isoformat(),
                            'session_id': session_id
                        }
                        failed_lots.append(failure_info)
                    
                    cursor.close()
                    
                    return {
                        'success': True,
                        'data': {
                            'failed_lots': failed_lots,
                            'total_count': len(failed_lots),
                            'summary': summary_stats
                        },
                        'debug_info': {
                            'data_source': 'database_cached',
                            'table_name': 'scheduling_failed_lots',
                            'total_records': len(failed_lots),
                            'cache_key': cache_key
                        }
                    }
                    
            except Exception as e:
                logger.error(f"❌ 缓存版本获取失败批次数据失败: {e}")
                return {
                    'success': False,
                    'error': str(e)
                }
        
        # 🚀 使用缓存系统
        try:
            # 设置缓存时间：3分钟
            cached_result = cache_get(cache_key)
            if cached_result is not None:
                logger.info(f"✅ 缓存命中: {cache_key}")
                return jsonify(cached_result)
            
            # 缓存未命中，获取新数据
            result = fetch_failed_lots_data()
            if result.get('success'):
                cache_set(cache_key, result, timeout=180)  # 3分钟缓存
                logger.info(f"✅ 缓存已更新: {cache_key}")
            
            return jsonify(result)
            
        except Exception as cache_error:
            logger.warning(f"⚠️ 缓存操作失败，使用直接查询: {cache_error}")
            return jsonify(fetch_failed_lots_data())
            
    except Exception as e:
        logger.error(f"❌ 缓存版本API失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': {
                'failed_lots': [],
                'total_count': 0,
                'summary': {
                    'total_failed': 0,
                    'config_missing': 0,
                    'equipment_incompatible': 0,
                    'other_reasons': 0
                }
            }
        })
'''
            
            # 🔧 修复4：添加缓存失效函数
            cache_invalidate_function = '''
def invalidate_failed_lots_cache():
    """失效失败批次相关的所有缓存"""
    try:
        from app.utils.simple_cache import cache_delete
        
        # 清理所有相关缓存键
        cache_keys = [
            'failed_lots_True_24',
            'failed_lots_False_24',
            'failed_lots_True_168',  # 7天
            'failed_lots_False_168'
        ]
        
        cleared_count = 0
        for key in cache_keys:
            try:
                cache_delete(key)
                cleared_count += 1
            except:
                pass
        
        logger.info(f"🧹 已清理 {cleared_count} 个失败批次缓存键")
        
        return jsonify({
            'success': True,
            'message': f'已清理 {cleared_count} 个缓存键',
            'cleared_keys': cleared_count
        })
        
    except Exception as e:
        logger.error(f"❌ 缓存清理失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })
'''
            
            # 添加新函数到文件中
            if "def get_failed_lots_from_logs_cached():" not in content:
                # 在文件末尾添加新函数
                content += cached_function
                content += cache_invalidate_function
                logger.info("🔧 添加缓存版本API函数")
            
            # 备份原文件
            backup_path = api_file_path + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"📄 原文件已备份到: {backup_path}")
            
            # 写入优化后的文件
            with open(api_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ API文件已优化，集成了统一缓存系统")
            return True
            
    except Exception as e:
        logger.error(f"❌ 创建优化API版本失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def update_api_routes():
    """更新API路由配置，添加缓存版本的端点"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            # 查找路由配置文件
            routes_files = [
                'app/api_v2/production/__init__.py',
                'app/api_v2/__init__.py'
            ]
            
            for route_file in routes_files:
                if os.path.exists(route_file):
                    with open(route_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 添加新的路由注册
                    new_routes = '''
# 🚀 缓存优化的失败批次API路由
from .done_lots_api import get_failed_lots_from_logs_cached, invalidate_failed_lots_cache

# 注册缓存版本的路由
bp.route('/get-failed-lots-cached')(get_failed_lots_from_logs_cached)
bp.route('/invalidate-failed-lots-cache', methods=['POST'])(invalidate_failed_lots_cache)
'''
                    
                    if "get_failed_lots_from_logs_cached" not in content:
                        content += new_routes
                        
                        with open(route_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        logger.info(f"✅ 更新路由配置: {route_file}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 更新API路由失败: {e}")
        return False

def create_frontend_cache_optimization():
    """创建前端缓存优化脚本"""
    try:
        frontend_optimization = '''
// 前端缓存优化脚本
// 添加到 failed_lots.html 的 JavaScript 部分

// 🚀 缓存管理
let dataCache = new Map();
let cacheTimeout = 3 * 60 * 1000; // 3分钟缓存

// 优化的数据加载函数
async function loadFailedLotsOptimized() {
    try {
        const cacheKey = `failed_lots_${isCurrentOnly}_${Date.now()}`;
        const cachedData = dataCache.get(`failed_lots_${isCurrentOnly}`);
        
        // 检查缓存
        if (cachedData && (Date.now() - cachedData.timestamp < cacheTimeout)) {
            console.log('✅ 使用缓存数据');
            processFailedLotsData(cachedData.data);
            return;
        }
        
        showLoading();
        
        // 优先使用缓存版本的API
        const url = `/api/v2/production/get-failed-lots-cached?current_only=${isCurrentOnly}`;
        console.log(`🔍 请求失败批次数据（缓存版本）: ${url}`);
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('📊 失败批次API响应（缓存版本）:', result);
        
        if (result.success) {
            // 更新缓存
            dataCache.set(`failed_lots_${isCurrentOnly}`, {
                data: result,
                timestamp: Date.now()
            });
            
            processFailedLotsData(result);
            console.log(`✅ 成功加载 ${result.data.failed_lots.length} 条失败批次记录（缓存版本）`);
        } else {
            throw new Error(result.message || '获取失败批次数据失败');
        }
    } catch (error) {
        console.error('❌ 加载失败批次数据出错（尝试备用API）:', error);
        // 备用：使用原始API
        loadFailedLotsOriginal();
    } finally {
        hideLoading();
    }
}

// 处理失败批次数据的函数
function processFailedLotsData(result) {
    allFailedLots = result.data.failed_lots || [];
    
    // 更新统计信息
    if (result.data.summary) {
        updateStats(result.data.summary);
    }
    
    // 更新数据源信息
    if (result.debug_info) {
        updateDataSourceInfo(result.debug_info);
    }
    
    // 💡 在数据加载完成后生成筛选选项
    updateFilterOptionsFromData(allFailedLots);
    
    // 应用筛选和渲染
    filterLots();
}

// 备用的原始API加载函数
async function loadFailedLotsOriginal() {
    const url = `/api/v2/production/get-failed-lots-from-logs?current_only=${isCurrentOnly}`;
    // ... 原有的加载逻辑
}

// 缓存失效函数
async function invalidateCache() {
    try {
        dataCache.clear();
        console.log('🧹 本地缓存已清理');
        
        // 调用服务端缓存清理
        const response = await fetch('/api/v2/production/invalidate-failed-lots-cache', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('🧹 服务端缓存清理结果:', result);
        }
        
        // 重新加载数据
        loadFailedLotsOptimized();
        
    } catch (error) {
        console.error('⚠️ 缓存清理失败:', error);
        // 即使缓存清理失败，也要重新加载数据
        loadFailedLotsOptimized();
    }
}

// 修改刷新数据函数
function refreshData() {
    invalidateCache();
}

// 在页面加载时使用优化版本
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔥 失败批次页面初始化（缓存优化版本）...');
    initializeEventListeners();
    initializeSorting();
    // 使用缓存优化的加载函数
    loadFailedLotsOptimized();
});
'''
        
        # 保存前端优化脚本
        with open('frontend_cache_optimization.js', 'w', encoding='utf-8') as f:
            f.write(frontend_optimization)
        
        logger.info("📄 前端缓存优化脚本已创建: frontend_cache_optimization.js")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建前端缓存优化失败: {e}")
        return False

def test_optimized_api():
    """测试优化后的API"""
    try:
        import requests
        
        logger.info("🧪 测试优化后的API...")
        
        # 测试缓存版本API
        url = "http://localhost:5000/api/v2/production/get-failed-lots-cached?current_only=false"
        
        start_time = datetime.now()
        response = requests.get(url)
        end_time = datetime.now()
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                failed_lots = result.get('data', {}).get('failed_lots', [])
                response_time = (end_time - start_time).total_seconds() * 1000
                
                logger.info(f"✅ 缓存版本API测试成功:")
                logger.info(f"   - 返回记录数: {len(failed_lots)}")
                logger.info(f"   - 响应时间: {response_time:.2f}ms")
                logger.info(f"   - 数据源: {result.get('debug_info', {}).get('data_source', 'unknown')}")
                
                return True
            else:
                logger.error(f"❌ API返回失败: {result}")
                return False
        else:
            logger.error(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ API测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始修复失败批次页面的缓存和数据连接问题...")
    
    # 步骤1: 创建优化的API版本
    logger.info("=" * 50)
    logger.info("步骤1: 创建优化的API版本")
    success1 = create_optimized_api_version()
    if not success1:
        logger.error("❌ 创建优化API版本失败")
        return False
    
    # 步骤2: 更新API路由
    logger.info("=" * 50)
    logger.info("步骤2: 更新API路由")
    success2 = update_api_routes()
    if not success2:
        logger.warning("⚠️ 更新API路由失败（可能需要手动配置）")
    
    # 步骤3: 创建前端缓存优化
    logger.info("=" * 50)
    logger.info("步骤3: 创建前端缓存优化")
    success3 = create_frontend_cache_optimization()
    if not success3:
        logger.error("❌ 创建前端缓存优化失败")
        return False
    
    # 步骤4: 测试优化后的API
    logger.info("=" * 50)
    logger.info("步骤4: 测试优化后的API")
    success4 = test_optimized_api()
    if not success4:
        logger.warning("⚠️ API测试失败（可能需要重启应用）")
    
    logger.info("=" * 50)
    logger.info("🎉 缓存和数据连接问题修复完成!")
    logger.info("")
    logger.info("修复内容:")
    logger.info("1. ✅ 移除了SQL查询中的字符集转换问题")
    logger.info("2. ✅ 集成了统一缓存系统到失败批次API")
    logger.info("3. ✅ 添加了缓存失效处理")
    logger.info("4. ✅ 创建了前端缓存优化脚本")
    logger.info("")
    logger.info("后续操作:")
    logger.info("1. 重启Flask应用以加载新的API版本")
    logger.info("2. 将frontend_cache_optimization.js的内容添加到failed_lots.html")
    logger.info("3. 测试前端页面的数据加载和缓存功能")
    
    return True

if __name__ == "__main__":
    success = main()
    print("🎉 缓存问题修复: 成功" if success else "❌ 缓存问题修复: 失败")