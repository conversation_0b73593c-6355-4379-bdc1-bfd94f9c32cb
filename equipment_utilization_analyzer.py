#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备利用率深度分析器 - 分析为什么只有32台设备被使用
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict, Counter
import pandas as pd
import json

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('equipment_utilization_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('EquipmentAnalyzer')

def analyze_equipment_utilization():
    """主分析函数"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入数据管理器
            from app.services.data_source_manager import DataSourceManager
            data_manager = DataSourceManager()
            
            # 分析结果存储
            analysis_results = {
                'timestamp': datetime.now().isoformat(),
                'equipment_analysis': {},
                'scheduling_analysis': {},
                'matching_analysis': {},
                'configuration_issues': {},
                'recommendations': []
            }
            
            # ==================== 第一步：设备状态分析 ====================
            logger.info("📊 开始设备状态分析...")
            
            # 获取所有设备状态数据
            equipment_result = data_manager.get_table_data('EQP_STATUS')
            if not equipment_result.get('success'):
                logger.error("❌ 无法获取设备状态数据")
                return False
            
            all_equipment = equipment_result.get('data', [])
            logger.info(f"📋 总共找到 {len(all_equipment)} 台设备")
            
            # 按状态分类设备
            equipment_by_status = defaultdict(list)
            equipment_by_type = defaultdict(list)
            equipment_by_class = defaultdict(list)
            
            for eqp in all_equipment:
                status = eqp.get('STATUS', '').strip()
                eqp_type = eqp.get('EQP_TYPE', '').strip()
                eqp_class = eqp.get('EQP_CLASS', '').strip()
                
                equipment_by_status[status].append(eqp)
                if eqp_type:
                    equipment_by_type[eqp_type].append(eqp)
                if eqp_class:
                    equipment_by_class[eqp_class].append(eqp)
            
            # 记录设备统计信息
            analysis_results['equipment_analysis'] = {
                'total_equipment': len(all_equipment),
                'status_distribution': {status: len(eqps) for status, eqps in equipment_by_status.items()},
                'type_distribution': {eqp_type: len(eqps) for eqp_type, eqps in equipment_by_type.items()},
                'class_distribution': {eqp_class: len(eqps) for eqp_class, eqps in equipment_by_class.items()}
            }
            
            logger.info(f"📊 设备状态分布: {analysis_results['equipment_analysis']['status_distribution']}")
            logger.info(f"📊 设备类型分布: {analysis_results['equipment_analysis']['type_distribution']}")
            
            # ==================== 第二步：排产结果分析 ====================
            logger.info("📊 开始排产结果分析...")
            
            # 获取已排产数据
            scheduled_result = data_manager.get_table_data('lotprioritydone')
            if scheduled_result.get('success'):
                scheduled_lots = scheduled_result.get('data', [])
                logger.info(f"📋 找到 {len(scheduled_lots)} 个已排产批次")
                
                # 统计已占用的设备
                used_equipment = set()
                device_usage_count = defaultdict(int)
                
                for lot in scheduled_lots:
                    handler_id = lot.get('HANDLER_ID', '').strip()
                    if handler_id:
                        used_equipment.add(handler_id)
                        device_usage_count[handler_id] += 1
                
                analysis_results['scheduling_analysis'] = {
                    'total_scheduled_lots': len(scheduled_lots),
                    'used_equipment_count': len(used_equipment),
                    'unused_equipment_count': len(all_equipment) - len(used_equipment),
                    'equipment_utilization_rate': len(used_equipment) / len(all_equipment) * 100 if all_equipment else 0,
                    'used_equipment_list': list(used_equipment),
                    'device_workload_distribution': dict(device_usage_count)
                }
                
                logger.info(f"📊 已使用设备: {len(used_equipment)} 台")
                logger.info(f"📊 未使用设备: {len(all_equipment) - len(used_equipment)} 台")
                logger.info(f"📊 设备利用率: {analysis_results['scheduling_analysis']['equipment_utilization_rate']:.1f}%")
                
                # 分析未使用的设备
                unused_equipment = []
                for eqp in all_equipment:
                    handler_id = eqp.get('HANDLER_ID', '').strip()
                    if handler_id not in used_equipment:
                        unused_equipment.append({
                            'HANDLER_ID': handler_id,
                            'STATUS': eqp.get('STATUS', ''),
                            'EQP_TYPE': eqp.get('EQP_TYPE', ''),
                            'EQP_CLASS': eqp.get('EQP_CLASS', ''),
                            'DEVICE': eqp.get('DEVICE', ''),
                            'STAGE': eqp.get('STAGE', ''),
                            'KIT_PN': eqp.get('KIT_PN', ''),
                            'HB_PN': eqp.get('HB_PN', ''),
                            'TB_PN': eqp.get('TB_PN', ''),
                            'HANDLER_CONFIG': eqp.get('HANDLER_CONFIG', ''),
                            'TESTER_CONFIG': eqp.get('TESTER_CONFIG', '')
                        })
                
                logger.info(f"📋 分析了 {len(unused_equipment)} 台未使用设备的配置信息")
                
            else:
                logger.warning("⚠️ 无法获取排产结果数据")
                analysis_results['scheduling_analysis'] = {
                    'error': '无法获取排产结果数据'
                }
                unused_equipment = all_equipment
            
            # ==================== 第三步：配置数据完整性分析 ====================
            logger.info("📊 开始配置数据完整性分析...")
            
            config_issues = {
                'missing_handler_config': [],
                'missing_kit_info': [],
                'missing_device_stage': [],
                'incomplete_tester_config': []
            }
            
            for eqp in all_equipment:
                handler_id = eqp.get('HANDLER_ID', '').strip()
                handler_config = eqp.get('HANDLER_CONFIG', '').strip()
                kit_pn = eqp.get('KIT_PN', '').strip()
                device = eqp.get('DEVICE', '').strip()
                stage = eqp.get('STAGE', '').strip()
                tester_config = eqp.get('TESTER_CONFIG', '').strip()
                
                if not handler_config:
                    config_issues['missing_handler_config'].append(handler_id)
                if not kit_pn:
                    config_issues['missing_kit_info'].append(handler_id)
                if not device or not stage:
                    config_issues['missing_device_stage'].append(handler_id)
                if not tester_config:
                    config_issues['incomplete_tester_config'].append(handler_id)
            
            analysis_results['configuration_issues'] = {
                'missing_handler_config_count': len(config_issues['missing_handler_config']),
                'missing_kit_info_count': len(config_issues['missing_kit_info']),
                'missing_device_stage_count': len(config_issues['missing_device_stage']),
                'incomplete_tester_config_count': len(config_issues['incomplete_tester_config']),
                'details': config_issues
            }
            
            logger.info(f"📊 配置问题统计:")
            logger.info(f"   - 缺少HANDLER_CONFIG: {len(config_issues['missing_handler_config'])} 台")
            logger.info(f"   - 缺少KIT信息: {len(config_issues['missing_kit_info'])} 台")
            logger.info(f"   - 缺少DEVICE/STAGE信息: {len(config_issues['missing_device_stage'])} 台")
            logger.info(f"   - 缺少TESTER_CONFIG: {len(config_issues['incomplete_tester_config'])} 台")
            
            # ==================== 第四步：待排产批次需求分析 ====================
            logger.info("📊 开始待排产批次需求分析...")
            
            wait_lots_result = data_manager.get_table_data('et_wait_lot')
            if wait_lots_result.get('success'):
                wait_lots = wait_lots_result.get('data', [])
                logger.info(f"📋 找到 {len(wait_lots)} 个待排产批次")
                
                # 分析批次需求分布
                device_requirements = defaultdict(int)
                stage_requirements = defaultdict(int)
                device_stage_combo = defaultdict(int)
                
                for lot in wait_lots:
                    device = lot.get('DEVICE', '').strip()
                    stage = lot.get('STAGE', '').strip()
                    
                    if device:
                        device_requirements[device] += 1
                    if stage:
                        stage_requirements[stage] += 1
                    if device and stage:
                        device_stage_combo[f"{device}-{stage}"] += 1
                
                analysis_results['lot_requirements'] = {
                    'total_wait_lots': len(wait_lots),
                    'device_requirements': dict(device_requirements),
                    'stage_requirements': dict(stage_requirements),
                    'device_stage_combinations': dict(device_stage_combo)
                }
                
                logger.info(f"📊 待排产批次的设备需求: {dict(device_requirements)}")
                logger.info(f"📊 待排产批次的工序需求: {dict(stage_requirements)}")
                
            else:
                logger.warning("⚠️ 无法获取待排产批次数据")
            
            # ==================== 第五步：设备匹配能力分析 ====================
            logger.info("📊 开始设备匹配能力分析...")
            
            # 获取测试规格数据以了解匹配规则
            test_spec_result = data_manager.get_table_data('et_ft_test_spec')
            if test_spec_result.get('success'):
                test_specs = test_spec_result.get('data', [])
                logger.info(f"📋 找到 {len(test_specs)} 条测试规格数据")
                
                # 分析有多少设备能处理特定的产品-工序组合
                matching_capability = {}
                
                for spec in test_specs:
                    device = spec.get('DEVICE', '').strip()
                    stage = spec.get('STAGE', '').strip()
                    handler_config = spec.get('HANDLER', '').strip()  # 注意这里字段名可能是HANDLER
                    
                    if device and stage:
                        combo_key = f"{device}-{stage}"
                        
                        # 统计能处理此组合的设备数量
                        capable_equipment = []
                        for eqp in all_equipment:
                            eqp_handler_config = eqp.get('HANDLER_CONFIG', '').strip()
                            eqp_device = eqp.get('DEVICE', '').strip()
                            eqp_stage = eqp.get('STAGE', '').strip()
                            
                            # 检查是否匹配（简化版匹配逻辑）
                            same_setup = (eqp_device == device and eqp_stage == stage)
                            handler_match = (eqp_handler_config == handler_config) if handler_config else False
                            
                            if same_setup or handler_match:
                                capable_equipment.append(eqp.get('HANDLER_ID', ''))
                        
                        matching_capability[combo_key] = {
                            'required_handler_config': handler_config,
                            'capable_equipment_count': len(capable_equipment),
                            'capable_equipment_list': capable_equipment
                        }
                
                analysis_results['matching_analysis'] = matching_capability
                logger.info(f"📊 分析了 {len(matching_capability)} 个产品-工序组合的设备匹配能力")
                
                # 找出匹配设备数量少的组合
                low_match_combos = {k: v for k, v in matching_capability.items() 
                                  if v['capable_equipment_count'] < 5}
                if low_match_combos:
                    logger.warning(f"⚠️ 发现 {len(low_match_combos)} 个组合的可匹配设备少于5台:")
                    for combo, info in low_match_combos.items():
                        logger.warning(f"   - {combo}: 仅 {info['capable_equipment_count']} 台设备能处理")
            
            # ==================== 第六步：生成分析报告和建议 ====================
            logger.info("📊 生成分析报告和建议...")
            
            recommendations = []
            
            # 基于配置问题的建议
            if config_issues['missing_handler_config']:
                recommendations.append({
                    'type': '配置数据修复',
                    'priority': '高',
                    'issue': f'{len(config_issues["missing_handler_config"])} 台设备缺少HANDLER_CONFIG配置',
                    'impact': '这些设备无法参与大改机匹配，大大降低了设备利用率',
                    'solution': '补全这些设备的HANDLER_CONFIG字段配置',
                    'equipment_list': config_issues['missing_handler_config'][:10]  # 只显示前10台
                })
            
            if config_issues['missing_kit_info']:
                recommendations.append({
                    'type': '配置数据修复',
                    'priority': '高',
                    'issue': f'{len(config_issues["missing_kit_info"])} 台设备缺少KIT_PN配置',
                    'impact': '这些设备无法参与同设置和小改机匹配',
                    'solution': '补全这些设备的KIT_PN字段配置',
                    'equipment_list': config_issues['missing_kit_info'][:10]
                })
            
            # 基于利用率的建议
            if 'scheduling_analysis' in analysis_results:
                utilization_rate = analysis_results['scheduling_analysis'].get('equipment_utilization_rate', 0)
                if utilization_rate < 50:
                    recommendations.append({
                        'type': '排产策略优化',
                        'priority': '中',
                        'issue': f'设备利用率过低 ({utilization_rate:.1f}%)',
                        'impact': '大量设备闲置，生产能力未充分发挥',
                        'solution': '考虑放宽设备匹配规则，允许更多的大改机匹配',
                        'details': f'当前仅使用了 {analysis_results["scheduling_analysis"]["used_equipment_count"]} / {analysis_results["equipment_analysis"]["total_equipment"]} 台设备'
                    })
            
            analysis_results['recommendations'] = recommendations
            
            # ==================== 保存分析结果 ====================
            # 保存详细的分析结果到JSON文件
            output_file = f'equipment_utilization_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📄 详细分析结果已保存到: {output_file}")
            
            # ==================== 输出核心发现 ====================
            print("\n" + "="*80)
            print("🔍 设备利用率深度分析结果")
            print("="*80)
            
            print(f"\n📊 设备总体情况:")
            print(f"   总设备数量: {analysis_results['equipment_analysis']['total_equipment']} 台")
            if 'scheduling_analysis' in analysis_results and 'used_equipment_count' in analysis_results['scheduling_analysis']:
                print(f"   已使用设备: {analysis_results['scheduling_analysis']['used_equipment_count']} 台")
                print(f"   未使用设备: {analysis_results['scheduling_analysis']['unused_equipment_count']} 台")
                print(f"   设备利用率: {analysis_results['scheduling_analysis']['equipment_utilization_rate']:.1f}%")
            
            print(f"\n⚠️ 配置问题统计:")
            for issue_type, issue_data in analysis_results['configuration_issues'].items():
                if issue_type.endswith('_count'):
                    issue_name = issue_type.replace('_count', '').replace('_', ' ').title()
                    print(f"   {issue_name}: {issue_data} 台设备")
            
            if recommendations:
                print(f"\n💡 核心建议:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"   {i}. [{rec['priority']}优先级] {rec['issue']}")
                    print(f"      解决方案: {rec['solution']}")
            
            print(f"\n📄 详细报告已保存至: {output_file}")
            print("="*80)
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_equipment_utilization()
    if success:
        print("\n🎉 设备利用率分析完成！")
    else:
        print("\n❌ 设备利用率分析失败！")

if __name__ == "__main__":
    main()