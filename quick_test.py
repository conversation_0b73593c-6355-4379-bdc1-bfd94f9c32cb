#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.real_scheduling_service import RealSchedulingService
    
    scheduler = RealSchedulingService()
    
    # 检查关键属性
    has_cache_timestamp = hasattr(scheduler, '_cache_timestamp')
    has_cache_timeout = hasattr(scheduler, '_cache_timeout')
    
    print("Cache timestamp:", has_cache_timestamp)
    print("Cache timeout:", has_cache_timeout)
    
    if has_cache_timestamp and has_cache_timeout:
        print("SUCCESS: Critical attributes restored")
    else:
        print("FAILED: Missing critical attributes")
        
except Exception as e:
    print(f"ERROR: {e}")