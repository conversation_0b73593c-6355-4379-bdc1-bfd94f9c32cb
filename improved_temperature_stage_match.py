#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的温度感知STAGE匹配逻辑
基于机台TEMPERATURE_RANGE物理能力的智能匹配
"""

import re
import logging
from typing import Tuple, Optional, Dict, Any

logger = logging.getLogger(__name__)

class TemperatureAwareStageMatch:
    """基于温度能力的智能STAGE匹配器"""
    
    def __init__(self):
        # 测试阶段的温度需求定义（修正版）
        # 注意：这里定义的是测试时需要达到的目标温度，而不是温度范围
        self.stage_temp_requirements = {
            # 低温测试系列（需要机台能达到-40°C左右）
            'Cold': {'target_temp': -40, 'tolerance': 15, 'description': '低温测试'},
            'COLD': {'target_temp': -40, 'tolerance': 15, 'description': '低温测试'},
            'cold': {'target_temp': -40, 'tolerance': 15, 'description': '低温测试'},
            'COLD-FT': {'target_temp': -40, 'tolerance': 15, 'description': '低温FT测试'},
            
            # 高温测试系列（需要机台能达到125°C左右）
            'Hot': {'target_temp': 125, 'tolerance': 25, 'description': '高温测试'},
            'HOT': {'target_temp': 125, 'tolerance': 25, 'description': '高温测试'},
            'hot': {'target_temp': 125, 'tolerance': 25, 'description': '高温测试'},
            'HOT-FT': {'target_temp': 125, 'tolerance': 25, 'description': '高温FT测试'},
            '125HOT1': {'target_temp': 125, 'tolerance': 10, 'description': '125度高温测试'},
            
            # 烘烤测试系列（需要机台能达到125°C左右）
            'BAKING': {'target_temp': 125, 'tolerance': 25, 'description': '烘烤测试'},
            'Baking': {'target_temp': 125, 'tolerance': 25, 'description': '烘烤测试'},
            'baking': {'target_temp': 125, 'tolerance': 25, 'description': '烘烤测试'},
            
            # 常温测试系列（需要机台能达到25°C，基本所有机台都能做）
            'ROOM': {'target_temp': 25, 'tolerance': 10, 'description': '常温测试'},
            'Room': {'target_temp': 25, 'tolerance': 10, 'description': '常温测试'},
            'ROOM-TTR': {'target_temp': 25, 'tolerance': 10, 'description': '常温TTR测试'},
            'ROOM-TEST': {'target_temp': 25, 'tolerance': 10, 'description': '常温测试'},
            'TRIM': {'target_temp': 25, 'tolerance': 15, 'description': '修整测试'},
            'Trim': {'target_temp': 25, 'tolerance': 15, 'description': '修整测试'},
            'LSTR': {'target_temp': 25, 'tolerance': 15, 'description': '激光修调'},
        }
    
    def parse_temperature_range(self, temp_range: str) -> Tuple[Optional[float], Optional[float]]:
        """
        解析TEMPERATURE_RANGE字符串，提取最小和最大温度
        
        Args:
            temp_range: 温度范围字符串，如 "-50-150", "25", "25-150"
            
        Returns:
            Tuple[min_temp, max_temp]: 最小温度和最大温度，解析失败返回 (None, None)
        """
        if not temp_range or not isinstance(temp_range, str):
            return (None, None)
        
        temp_range = temp_range.strip()
        
        try:
            # 1. 处理单一温度值（如 "25"）
            if temp_range.isdigit() or (temp_range.startswith('-') and temp_range[1:].isdigit()):
                temp = float(temp_range)
                return (temp, temp)
            
            # 2. 处理范围格式（如 "-50-150", "25-150"）
            # 匹配模式: 可能的负号 + 数字 + - + 数字
            pattern = r'^(-?\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)$'
            match = re.match(pattern, temp_range)
            
            if match:
                min_temp = float(match.group(1))
                max_temp = float(match.group(2))
                return (min_temp, max_temp)
            
            # 3. 处理其他可能的格式
            # 尝试分割并提取数字
            parts = re.findall(r'-?\d+(?:\.\d+)?', temp_range)
            if len(parts) == 2:
                return (float(parts[0]), float(parts[1]))
            elif len(parts) == 1:
                temp = float(parts[0])
                return (temp, temp)
                
            logger.warning(f"⚠️ 无法解析温度范围格式: {temp_range}")
            return (None, None)
            
        except Exception as e:
            logger.error(f"❌ 解析温度范围失败: {temp_range}, 错误: {e}")
            return (None, None)
    
    def get_stage_temp_requirement(self, stage: str) -> Optional[Dict[str, Any]]:
        """
        获取测试阶段的温度需求
        
        Args:
            stage: 测试阶段名称
            
        Returns:
            Dict: 温度需求信息，包含 min_temp, max_temp, description
        """
        if not stage:
            return None
            
        stage = stage.strip()
        
        # 1. 精确匹配
        if stage in self.stage_temp_requirements:
            return self.stage_temp_requirements[stage]
        
        # 2. 大小写不敏感匹配
        stage_upper = stage.upper()
        stage_lower = stage.lower()
        stage_title = stage.title()
        
        for candidate in [stage_upper, stage_lower, stage_title]:
            if candidate in self.stage_temp_requirements:
                return self.stage_temp_requirements[candidate]
        
        # 3. 模糊匹配（包含关系）
        stage_clean = stage.upper().strip()
        for stage_key, requirement in self.stage_temp_requirements.items():
            if stage_clean in stage_key.upper() or stage_key.upper() in stage_clean:
                logger.debug(f"🔍 STAGE模糊匹配: {stage} → {stage_key}")
                return requirement
        
        # 4. 默认为常温测试
        logger.debug(f"⚠️ 未知STAGE {stage}，默认为常温测试")
        return {'target_temp': 25, 'tolerance': 10, 'description': '默认常温测试'}
    
    def check_temperature_capability(self, equipment_temp_range: str, required_stage: str) -> bool:
        """
        检查设备温度能力是否满足测试阶段需求
        
        Args:
            equipment_temp_range: 设备温度范围，如 "-50-150"
            required_stage: 需要的测试阶段，如 "Cold", "Hot"
            
        Returns:
            bool: 是否满足温度要求
        """
        # 1. 解析设备温度能力
        eqp_min_temp, eqp_max_temp = self.parse_temperature_range(equipment_temp_range)
        if eqp_min_temp is None or eqp_max_temp is None:
            logger.debug(f"⚠️ 设备温度范围解析失败: {equipment_temp_range}")
            return False
        
        # 2. 获取测试阶段温度需求
        stage_requirement = self.get_stage_temp_requirement(required_stage)
        if not stage_requirement:
            logger.debug(f"⚠️ 无法确定STAGE温度需求: {required_stage}")
            return False
        
        # 3. 检查温度能力是否满足需求
        if 'target_temp' in stage_requirement:
            # 新格式：目标温度 + 容差
            target_temp = stage_requirement['target_temp']
            tolerance = stage_requirement.get('tolerance', 10)
            
            # 设备的温度范围必须包含目标温度
            capability_ok = (eqp_min_temp <= target_temp <= eqp_max_temp)
            
            if capability_ok:
                logger.info(f"✅ 温度能力匹配: 设备[{eqp_min_temp}~{eqp_max_temp}°C] 可执行 {required_stage}[目标{target_temp}°C±{tolerance}]")
            else:
                logger.debug(f"❌ 温度能力不足: 设备[{eqp_min_temp}~{eqp_max_temp}°C] 无法执行 {required_stage}[目标{target_temp}°C±{tolerance}]")
                
        else:
            # 旧格式兼容：温度范围
            required_min = stage_requirement.get('min_temp', 20)
            required_max = stage_requirement.get('max_temp', 30)
            
            # 设备必须能够达到测试所需的温度范围
            capability_ok = (eqp_min_temp <= required_min and eqp_max_temp >= required_max)
            
            if capability_ok:
                logger.info(f"✅ 温度能力匹配: 设备[{eqp_min_temp}~{eqp_max_temp}°C] 可执行 {required_stage}[{required_min}~{required_max}°C]")
            else:
                logger.debug(f"❌ 温度能力不足: 设备[{eqp_min_temp}~{eqp_max_temp}°C] 无法执行 {required_stage}[{required_min}~{required_max}°C]")
        
        return capability_ok
    
    def enhanced_stage_match(self, lot_stage: str, equipment_stage: str, 
                           equipment_temp_range: str, equipment_eqp_class: str = "", 
                           fallback_to_string_match: bool = True) -> bool:
        """
        增强的STAGE匹配逻辑：优先使用温度能力匹配，兜底使用字符串匹配
        
        Args:
            lot_stage: 批次需要的测试阶段
            equipment_stage: 设备当前配置的阶段
            equipment_temp_range: 设备温度能力范围
            equipment_eqp_class: 设备类型 (如 Turret, PnP, Gravity等)
            fallback_to_string_match: 是否在温度匹配失败时回退到字符串匹配
            
        Returns:
            bool: 是否匹配
        """
        if not lot_stage:
            return False
        
        # 1. EQP_CLASS约束检查（最高优先级）
        if not self._check_eqp_class_constraint(lot_stage, equipment_eqp_class):
            logger.debug(f"❌ EQP_CLASS约束不满足: {lot_stage} 需要特定设备类型，当前设备类型: {equipment_eqp_class}")
            return False
        
        # 2. 温度能力匹配（主要逻辑）
        if equipment_temp_range:
            temp_match = self.check_temperature_capability(equipment_temp_range, lot_stage)
            if temp_match:
                logger.info(f"✅ 温度+类型匹配成功: {lot_stage} 可在 {equipment_eqp_class} 设备上执行")
                return True
        
        # 2. 精确字符串匹配
        if equipment_stage and lot_stage.strip().upper() == equipment_stage.strip().upper():
            logger.debug(f"✅ 精确STAGE匹配: {lot_stage} == {equipment_stage}")
            return True
        
        # 3. 兜底：传统字符串匹配逻辑
        if fallback_to_string_match:
            return self._legacy_string_match(lot_stage, equipment_stage or "")
        
        return False
    
    def _legacy_string_match(self, lot_stage: str, equipment_stage: str) -> bool:
        """传统的字符串匹配逻辑（兜底方案）"""
        if not lot_stage or not equipment_stage:
            return False
        
        lot_stage_clean = lot_stage.upper().strip()
        eqp_stage_clean = equipment_stage.upper().strip()
        
        # 基础映射关系
        stage_aliases = {
            'COLD': ['COLD', 'Cold', 'cold', 'COLD-FT'],
            'HOT': ['HOT', 'Hot', 'hot', 'HOT-FT', '125HOT1'],
            'ROOM': ['ROOM', 'Room', 'ROOM-TTR', 'ROOM-TEST'],
            'TRIM': ['TRIM', 'Trim', 'trim', 'TRIM-FT'],
            'BAKING': ['BAKING', 'Baking', 'baking'],
        }
        
        # 检查别名匹配
        for canonical_stage, aliases in stage_aliases.items():
            if lot_stage_clean in [alias.upper() for alias in aliases]:
                if eqp_stage_clean in [alias.upper() for alias in aliases]:
                    logger.debug(f"✅ 别名匹配: {lot_stage} ≈ {equipment_stage}")
                    return True
        
        return False
    
    def _check_eqp_class_constraint(self, lot_stage: str, equipment_eqp_class: str) -> bool:
        """
        检查EQP_CLASS约束条件
        
        Args:
            lot_stage: 批次测试阶段
            equipment_eqp_class: 设备类型
            
        Returns:
            bool: 是否满足EQP_CLASS约束
        """
        if not lot_stage:
            return False
        
        stage_upper = lot_stage.upper().strip()
        eqp_class_upper = equipment_eqp_class.upper().strip() if equipment_eqp_class else ""
        
        # EQP_CLASS特殊约束规则
        eqp_class_constraints = {
            # ROOM-TTR只能在Turret和Gravity类型设备上运行
            'ROOM-TTR': ['TURRET', 'GRAVITY'],
            'Room-TTR': ['TURRET', 'GRAVITY'],
            'room-ttr': ['TURRET', 'GRAVITY'],
            
            # 可以根据需要添加更多约束
            # 'SOME_OTHER_STAGE': ['REQUIRED_EQP_CLASS'],
        }
        
        # 检查是否有EQP_CLASS约束
        if stage_upper in [key.upper() for key in eqp_class_constraints.keys()]:
            # 找到对应的约束规则
            required_classes = None
            for stage_key, classes in eqp_class_constraints.items():
                if stage_upper == stage_key.upper():
                    required_classes = [cls.upper() for cls in classes]
                    break
            
            if required_classes:
                if not eqp_class_upper:
                    logger.debug(f"⚠️ 设备类型信息缺失，无法验证 {lot_stage} 的EQP_CLASS约束")
                    return False
                
                if eqp_class_upper not in required_classes:
                    logger.debug(f"❌ EQP_CLASS约束失败: {lot_stage} 需要 {required_classes}，当前为 {eqp_class_upper}")
                    return False
                else:
                    logger.debug(f"✅ EQP_CLASS约束满足: {lot_stage} 在 {eqp_class_upper} 设备上可执行")
        
        # 没有约束或满足约束
        return True

def main():
    """测试温度感知匹配逻辑"""
    matcher = TemperatureAwareStageMatch()
    
    print("Temperature-Aware STAGE Matching Test")
    print("=" * 60)
    
    # 测试用例（更新包含EQP_CLASS约束）
    test_cases = [
        # (设备温度范围, 批次STAGE, 设备STAGE, 设备类型, 预期结果, 描述)
        ("-50-150", "Cold", "ROOM", "PnP", True, "全温PnP机台可以做低温测试"),
        ("25", "Cold", "ROOM", "PnP", False, "常温PnP机台无法做低温测试"),
        ("25-150", "Hot", "ROOM", "PnP", True, "中温PnP机台可以做高温测试"),
        ("25", "Hot", "ROOM", "PnP", False, "常温PnP机台无法做高温测试"),
        
        # ROOM-TTR的EQP_CLASS约束测试
        ("25", "ROOM-TTR", "ROOM", "Turret", True, "Turret机台可以做ROOM-TTR测试"),
        ("25", "ROOM-TTR", "ROOM", "Gravity", True, "Gravity机台可以做ROOM-TTR测试"),  
        ("25", "ROOM-TTR", "ROOM", "PnP", False, "PnP机台不能做ROOM-TTR测试"),
        ("-50-150", "ROOM-TTR", "ROOM", "PnP", False, "即使全温PnP机台也不能做ROOM-TTR测试"),
        
        ("25-150", "BAKING", "ROOM", "PnP", True, "中温PnP机台可以做烘烤测试"),
        ("25", "BAKING", "ROOM", "PnP", False, "常温PnP机台无法做烘烤测试"),
    ]
    
    for i, (temp_range, lot_stage, eqp_stage, eqp_class, expected, description) in enumerate(test_cases, 1):
        result = matcher.enhanced_stage_match(lot_stage, eqp_stage, temp_range, eqp_class)
        status = "PASS" if result == expected else "FAIL"
        print(f"{i:2d}. {status} | {temp_range:8s} + {lot_stage:10s} ({eqp_class:7s}) -> {result} | {description}")
        
        if result != expected:
            print(f"    Expected: {expected}, Actual: {result}")

if __name__ == "__main__":
    main()