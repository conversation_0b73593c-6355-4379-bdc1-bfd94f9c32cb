#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查HCHC-C-012-6800机台批次优先级排序问题
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
import pandas as pd

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('check_equipment_priority.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('EquipmentPriorityCheck')

def check_equipment_priority():
    """检查机台批次优先级排序"""
    try:
        # Flask应用创建
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app import db
            
            logger.info("✅ Flask应用上下文创建成功")
            
            # 先查看final_scheduling_result表结构
            logger.info("🔍 查看final_scheduling_result表结构...")
            table_structure = db.session.execute(db.text("DESCRIBE final_scheduling_result"))
            columns = table_structure.fetchall()
            
            print("\n=== final_scheduling_result表结构 ===")
            for col in columns:
                print(f"{col.Field}: {col.Type}")
            
            # 查询HCHC-C-012-6800机台的排产结果 - 使用实际字段名
            query = """
            SELECT 
                final_handler_id,
                lot_id,
                final_priority,
                comprehensive_score,
                create_time,
                created_at
            FROM final_scheduling_result 
            WHERE final_handler_id = 'HCHC-C-012-6800' 
            ORDER BY create_time
            LIMIT 30
            """
            
            result = db.session.execute(db.text(query))
            rows = result.fetchall()
            
            if rows:
                logger.info(f"📊 找到HCHC-C-012-6800机台的{len(rows)}条排产记录")
                
                print("\n=== HCHC-C-012-6800机台批次排产结果 ===")
                print(f"{'序号':<4} {'批次ID':<15} {'最终优先级':<12} {'综合评分':<12} {'创建时间':<20}")
                print("-" * 75)
                
                for i, row in enumerate(rows, 1):
                    print(f"{i:<4} {row.lot_id:<15} {row.final_priority or 'N/A':<12} "
                          f"{row.comprehensive_score or 'N/A':<12} {row.create_time or 'N/A'}")
                
                # 检查排序逻辑
                logger.info("\n🔍 分析排序逻辑...")
                
                # 按评分排序检查
                scored_lots = [(row.lot_id, row.final_priority, row.comprehensive_score) 
                              for row in rows if row.final_priority is not None]
                
                if scored_lots:
                    print(f"\n=== 按最终优先级排序分析 ===")
                    scored_lots.sort(key=lambda x: x[1])  # 按final_priority升序（数字小的优先级高）
                    
                    print("理论上应该的排序（按final_priority升序）:")
                    for i, (lot_id, final_priority, comprehensive_score) in enumerate(scored_lots[:10], 1):
                        print(f"{i}. {lot_id} - 最终优先级: {final_priority}, 综合评分: {comprehensive_score}")
                
            else:
                logger.warning("❌ 未找到HCHC-C-012-6800机台的排产记录")
            
            # 检查其他相关表
            logger.info("\n🔍 检查相关数据表...")
            
            # 先查看lotprioritydone表结构
            logger.info("🔍 查看lotprioritydone表结构...")
            table_structure2 = db.session.execute(db.text("DESCRIBE lotprioritydone"))
            columns2 = table_structure2.fetchall()
            
            print("\n=== lotprioritydone表结构 ===")
            for col in columns2:
                print(f"{col.Field}: {col.Type}")
            
            # 检查lotprioritydone表 - 使用正确字段名
            query2 = """
            SELECT 
                HANDLER_ID,
                LOT_ID, 
                priority_score,
                comprehensive_score,
                PRIORITY,
                created_at
            FROM lotprioritydone 
            WHERE HANDLER_ID = 'HCHC-C-012-6800' 
            ORDER BY priority_score DESC
            LIMIT 20
            """
            
            result2 = db.session.execute(db.text(query2))
            rows2 = result2.fetchall()
            
            if rows2:
                print(f"\n=== lotprioritydone表中HCHC-C-012-6800机台记录 ===")
                print(f"{'批次ID':<15} {'优先级评分':<12} {'综合评分':<12} {'优先级':<8} {'创建时间':<20}")
                print("-" * 75)
                
                for row in rows2:
                    print(f"{row.LOT_ID:<15} {row.priority_score or 'N/A':<12} "
                          f"{row.comprehensive_score or 'N/A':<12} {row.PRIORITY or 'N/A':<8} {row.created_at or 'N/A'}")
                
                # 分析优先级评分排序问题
                print(f"\n🔍 问题分析：")
                print("1. final_scheduling_result表显示批次按create_time排序，不是按优先级排序")
                print("2. lotprioritydone表的优先级评分数据：")
                
                # 按优先级评分排序显示
                scored_lots_2 = [(row.LOT_ID, row.priority_score, row.PRIORITY) 
                               for row in rows2 if row.priority_score is not None]
                if scored_lots_2:
                    scored_lots_2.sort(key=lambda x: x[1], reverse=True)
                    print("\n按优先级评分排序应该是:")
                    for i, (lot_id, priority_score, priority) in enumerate(scored_lots_2[:10], 1):
                        print(f"  {i}. {lot_id} - 评分: {priority_score}, 优先级: {priority}")
            else:
                print("❌ lotprioritydone表中未找到该机台记录")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = check_equipment_priority()
    print("🎉 检查完成: 成功" if success else "❌ 检查完成: 失败")

if __name__ == "__main__":
    main()