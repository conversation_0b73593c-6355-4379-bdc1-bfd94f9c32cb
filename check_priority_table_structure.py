#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查优先级配置表的字段结构
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('priority_table_check.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('PriorityTableChecker')

def check_priority_table_structure():
    """检查优先级配置表的字段结构"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            
            data_manager = DataSourceManager()
            
            print("="*80)
            print("🔍 优先级配置表字段结构检查")
            print("="*80)
            
            # 获取数据库连接
            try:
                from sqlalchemy import text
                from app import db
                
                with db.engine.connect() as conn:
                    # 检查devicepriorityconfig表
                    print(f"\n📊 检查 devicepriorityconfig 表...")
                    
                    # 检查表是否存在
                    result = conn.execute(text("SHOW TABLES LIKE 'devicepriorityconfig'"))
                    if result.fetchone():
                        print(f"   ✅ devicepriorityconfig 表存在")
                        
                        # 获取表结构
                        result = conn.execute(text("DESCRIBE devicepriorityconfig"))
                        columns = result.fetchall()
                        print(f"   表字段:")
                        for col in columns:
                            print(f"     - {col[0]} ({col[1]})")
                        
                        # 获取样本数据
                        result = conn.execute(text("SELECT * FROM devicepriorityconfig LIMIT 3"))
                        rows = result.fetchall()
                        print(f"   样本数据 ({len(rows)} 条):")
                        for i, row in enumerate(rows, 1):
                            print(f"     {i}. {dict(row._mapping)}")
                            
                    else:
                        print(f"   ❌ devicepriorityconfig 表不存在")
                    
                    # 检查lotpriorityconfig表
                    print(f"\n📊 检查 lotpriorityconfig 表...")
                    
                    # 检查表是否存在
                    result = conn.execute(text("SHOW TABLES LIKE 'lotpriorityconfig'"))
                    if result.fetchone():
                        print(f"   ✅ lotpriorityconfig 表存在")
                        
                        # 获取表结构
                        result = conn.execute(text("DESCRIBE lotpriorityconfig"))
                        columns = result.fetchall()
                        print(f"   表字段:")
                        for col in columns:
                            print(f"     - {col[0]} ({col[1]})")
                        
                        # 获取样本数据
                        result = conn.execute(text("SELECT * FROM lotpriorityconfig LIMIT 3"))
                        rows = result.fetchall()
                        print(f"   样本数据 ({len(rows)} 条):")
                        for i, row in enumerate(rows, 1):
                            print(f"     {i}. {dict(row._mapping)}")
                            
                    else:
                        print(f"   ❌ lotpriorityconfig 表不存在")
                    
                    # 检查所有包含priority的表
                    print(f"\n📊 搜索所有包含priority的表...")
                    result = conn.execute(text("SHOW TABLES"))
                    all_tables = [row[0] for row in result.fetchall()]
                    priority_tables = [table for table in all_tables if 'priority' in table.lower()]
                    print(f"   包含priority的表: {priority_tables}")
                    
                    # 检查每个priority表的字段
                    for table in priority_tables:
                        print(f"\n   🔍 检查表: {table}")
                        try:
                            result = conn.execute(text(f"DESCRIBE {table}"))
                            columns = result.fetchall()
                            field_names = [col[0] for col in columns]
                            print(f"     字段: {field_names}")
                            
                            # 检查是否有priority_value字段
                            if 'priority_value' in field_names:
                                print(f"     ✅ 有 priority_value 字段")
                            elif 'priority' in field_names:
                                print(f"     ⚠️ 有 priority 字段但无 priority_value")
                            else:
                                print(f"     ❌ 无 priority 相关字段")
                                
                        except Exception as e:
                            print(f"     查询失败: {e}")
            
            except Exception as e:
                print(f"   数据库连接失败: {e}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = check_priority_table_structure()
    if success:
        print("\n🎉 优先级表字段检查完成！")
    else:
        print("\n❌ 检查失败！")

if __name__ == "__main__":
    main()