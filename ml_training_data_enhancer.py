#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ML训练数据增强器 - 提供真实业务场景的训练数据
"""

import sys
import io
import os
import logging
import time
import math
import random
from datetime import datetime, timedelta

# 编码修复
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ml_training_enhancer.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('MLTrainingEnhancer')

def generate_training_data():
    """生成真实业务场景的训练数据"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.ml_auto_scaler import auto_scaler, SystemMetrics
            
            logger.info("🧠 开始生成ML训练数据...")
            
            # 生成6个月的历史数据
            base_time = time.time() - (180 * 24 * 3600)  # 6个月前
            training_samples = []
            
            # 1. 工作日高峰模式 (周一到周五 9-12, 14-18)
            logger.info("📈 生成工作日高峰模式数据...")
            for day in range(180):  # 6个月
                current_time = base_time + day * 86400
                day_of_week = (datetime.fromtimestamp(current_time).weekday() + 1) % 7
                
                for hour in range(24):
                    # 基础负载
                    if day_of_week < 5:  # 工作日
                        if 9 <= hour < 12 or 14 <= hour < 18:
                            # 工作高峰期
                            base_load = 35 + random.uniform(-5, 15)
                            cpu_usage = 45 + random.uniform(-10, 25)
                            memory_usage = 60 + random.uniform(-15, 20)
                        elif 20 <= hour < 23:
                            # 夜间批处理时间
                            base_load = 25 + random.uniform(-5, 10)
                            cpu_usage = 35 + random.uniform(-5, 15)
                            memory_usage = 45 + random.uniform(-10, 15)
                        else:
                            # 其他工作时间
                            base_load = 15 + random.uniform(-5, 8)
                            cpu_usage = 25 + random.uniform(-8, 12)
                            memory_usage = 35 + random.uniform(-10, 15)
                    else:  # 周末
                        base_load = 8 + random.uniform(-3, 5)
                        cpu_usage = 15 + random.uniform(-5, 10)
                        memory_usage = 25 + random.uniform(-8, 12)
                    
                    # 添加季节性变化
                    month = datetime.fromtimestamp(current_time).month
                    if month in [11, 12, 1, 2]:  # 年末年初高峰
                        base_load *= 1.2
                        cpu_usage *= 1.15
                    elif month in [7, 8]:  # 夏季假期
                        base_load *= 0.8
                        cpu_usage *= 0.85
                    
                    # 创建训练样本
                    metrics = SystemMetrics(
                        timestamp=current_time + hour * 3600,
                        connection_count=int(max(5, base_load)),
                        cpu_usage=max(10, min(95, cpu_usage)),
                        memory_usage=max(20, min(90, memory_usage)),
                        request_rate=base_load * 0.8,
                        response_time=15 + (base_load / 40) * 20,
                        error_rate=0.01 + (base_load / 100) * 0.02,
                        business_activity_level=base_load / 50
                    )
                    
                    auto_scaler.ml_predictor.add_metrics(metrics)
                    training_samples.append(metrics)
                    
                    if len(training_samples) % 100 == 0:
                        logger.info(f"  📊 已生成 {len(training_samples)} 个训练样本")
            
            # 2. 特殊事件模式 (突发高负载)
            logger.info("🚨 生成突发事件模式数据...")
            special_events = [
                # 月末结算高峰
                {'duration': 72, 'load_multiplier': 2.5, 'frequency': 12},  # 每月一次，持续3天
                # 系统升级后的高负载
                {'duration': 24, 'load_multiplier': 1.8, 'frequency': 6},   # 每两月一次，持续1天
                # 突发数据导入任务
                {'duration': 8, 'load_multiplier': 3.0, 'frequency': 24},   # 每月两次，持续8小时
            ]
            
            for event in special_events:
                for occurrence in range(event['frequency']):
                    # 随机选择发生时间
                    event_start = base_time + random.uniform(0, 180 * 24 * 3600)
                    
                    for hour in range(event['duration']):
                        event_time = event_start + hour * 3600
                        
                        # 正常负载基础上乘以倍数
                        base_load = 20 * event['load_multiplier']
                        cpu_usage = 40 * event['load_multiplier']
                        memory_usage = 50 * event['load_multiplier']
                        
                        # 添加随机波动
                        base_load += random.uniform(-5, 10)
                        cpu_usage += random.uniform(-10, 15)
                        memory_usage += random.uniform(-10, 20)
                        
                        metrics = SystemMetrics(
                            timestamp=event_time,
                            connection_count=int(max(10, min(80, base_load))),
                            cpu_usage=max(20, min(95, cpu_usage)),
                            memory_usage=max(30, min(90, memory_usage)),
                            request_rate=base_load * 0.9,
                            response_time=20 + (base_load / 40) * 30,
                            error_rate=0.02 + (base_load / 100) * 0.03,
                            business_activity_level=min(1.0, base_load / 50)
                        )
                        
                        auto_scaler.ml_predictor.add_metrics(metrics)
                        training_samples.append(metrics)
            
            # 3. 节假日低负载模式
            logger.info("🏖️ 生成节假日低负载模式数据...")
            holidays = [
                # 春节(7天), 国庆(7天), 劳动节(5天), 清明(3天), 端午(3天), 中秋(3天)
                (7, 0.3), (7, 0.3), (5, 0.4), (3, 0.5), (3, 0.5), (3, 0.5)
            ]
            
            for holiday_days, load_factor in holidays:
                holiday_start = base_time + random.uniform(0, 180 * 24 * 3600)
                
                for day in range(holiday_days):
                    for hour in range(24):
                        holiday_time = holiday_start + day * 86400 + hour * 3600
                        
                        base_load = 12 * load_factor
                        cpu_usage = 20 * load_factor
                        memory_usage = 30 * load_factor
                        
                        metrics = SystemMetrics(
                            timestamp=holiday_time,
                            connection_count=int(max(3, base_load)),
                            cpu_usage=max(5, cpu_usage),
                            memory_usage=max(15, memory_usage),
                            request_rate=base_load * 0.6,
                            response_time=10 + random.uniform(0, 5),
                            error_rate=0.005,
                            business_activity_level=load_factor * 0.5
                        )
                        
                        auto_scaler.ml_predictor.add_metrics(metrics)
                        training_samples.append(metrics)
            
            logger.info(f"✅ 训练数据生成完成: {len(training_samples)} 个样本")
            logger.info(f"🧠 ML预测器状态: {auto_scaler.ml_predictor.get_model_stats()}")
            
            # 测试预测准确性
            logger.info("🎯 测试预测准确性...")
            test_accuracies = []
            
            for i in range(20):
                # 创建测试场景
                test_time = time.time() + random.uniform(0, 24 * 3600)
                hour = datetime.fromtimestamp(test_time).hour
                
                # 预期负载（基于小时模式）
                if 9 <= hour < 12 or 14 <= hour < 18:
                    expected_load = 35
                elif 20 <= hour <= 23:
                    expected_load = 25
                else:
                    expected_load = 15
                    
                # 进行预测
                predicted_load, confidence = auto_scaler.ml_predictor.predict_load()
                
                # 计算准确性
                if expected_load > 0:
                    accuracy = 1 - abs(predicted_load - expected_load) / expected_load
                    test_accuracies.append(max(0, accuracy))
            
            avg_accuracy = sum(test_accuracies) / len(test_accuracies) if test_accuracies else 0
            logger.info(f"📊 平均预测准确性: {avg_accuracy:.2%}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 训练数据生成失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("🚀 启动ML训练数据增强器...")
    
    success = generate_training_data()
    
    if success:
        logger.info("🎉 ML训练数据增强完成")
    else:
        logger.error("❌ ML训练数据增强失败")

if __name__ == "__main__":
    main()