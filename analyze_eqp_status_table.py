#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析EQP_STATUS表的真实字段结构和数据
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('eqp_status_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('EqpStatusAnalyzer')

def analyze_eqp_status_table():
    """分析EQP_STATUS表的真实结构和数据"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            from app import db
            from sqlalchemy import text
            
            data_manager = DataSourceManager()
            
            print("="*80)
            print("🔍 分析EQP_STATUS表的真实字段结构和数据")
            print("="*80)
            
            # ==================== 第一步：检查表结构 ====================
            print(f"\n📊 第一步：检查EQP_STATUS表结构...")
            
            with db.engine.connect() as conn:
                # 获取表结构
                result = conn.execute(text("DESCRIBE eqp_status"))
                columns = result.fetchall()
                print(f"   EQP_STATUS表字段:")
                for col in columns:
                    print(f"     - {col[0]} ({col[1]})")
                
                # 获取记录总数
                result = conn.execute(text("SELECT COUNT(*) FROM eqp_status"))
                total_count = result.fetchone()[0]
                print(f"\n   总记录数: {total_count} 条")
                
                # 获取前10条样本数据
                result = conn.execute(text("SELECT * FROM eqp_status LIMIT 10"))
                sample_data = result.fetchall()
                print(f"\n   前10条样本数据:")
                for i, row in enumerate(sample_data, 1):
                    row_dict = dict(row._mapping)
                    print(f"     {i}. {row_dict}")
            
            # ==================== 第二步：通过DataSourceManager获取数据 ====================
            print(f"\n📊 第二步：通过DataSourceManager获取设备数据...")
            
            equipment_result = data_manager.get_table_data('EQP_STATUS')
            equipment_data = equipment_result.get('data', [])
            print(f"   DataSourceManager获取到: {len(equipment_data)} 条记录")
            
            if equipment_data:
                print(f"   第一条记录样本:")
                first_record = equipment_data[0]
                print(f"     字段: {list(first_record.keys())}")
                print(f"     数据: {first_record}")
                
                # 分析关键字段
                print(f"\n   关键字段分析:")
                
                # EQP_STATE字段分析
                eqp_states = defaultdict(int)
                eqp_ids = []
                devices = []
                handlers = []
                
                for eqp in equipment_data:
                    state = eqp.get('EQP_STATE', '')
                    eqp_states[state or '空'] += 1
                    
                    eqp_id = eqp.get('EQP_ID', '')
                    if eqp_id:
                        eqp_ids.append(eqp_id)
                    
                    device = eqp.get('DEVICE', '')
                    if device:
                        devices.append(device)
                        
                    handler = eqp.get('HANDLER_ID', '') or eqp.get('HANDLER_CONFIG', '')
                    if handler:
                        handlers.append(handler)
                
                print(f"     EQP_STATE分布:")
                for state, count in sorted(eqp_states.items(), key=lambda x: x[1], reverse=True):
                    print(f"       - '{state}': {count} 台")
                
                print(f"     有EQP_ID的设备: {len(eqp_ids)} 台")
                print(f"     有DEVICE的设备: {len(devices)} 台")  
                print(f"     有HANDLER信息的设备: {len(handlers)} 台")
                
                # 显示前5个EQP_ID
                if eqp_ids:
                    print(f"     EQP_ID样本: {eqp_ids[:5]}")
                if devices:
                    print(f"     DEVICE样本: {devices[:5]}")
                if handlers:
                    print(f"     HANDLER样本: {handlers[:5]}")
            
            # ==================== 第三步：分析排产中的设备使用情况 ====================
            print(f"\n📊 第三步：分析当前排产结果中的设备使用...")
            
            # 获取已排产的批次数据
            with db.engine.connect() as conn:
                result = conn.execute(text("SELECT COUNT(*) FROM lotprioritydone"))
                scheduled_count = result.fetchone()[0]
                print(f"   已排产批次数: {scheduled_count} 个")
                
                if scheduled_count > 0:
                    # 分析使用的设备
                    result = conn.execute(text("""
                        SELECT HANDLER_ID, COUNT(*) as batch_count 
                        FROM lotprioritydone 
                        WHERE HANDLER_ID IS NOT NULL AND HANDLER_ID != ''
                        GROUP BY HANDLER_ID 
                        ORDER BY batch_count DESC
                    """))
                    used_handlers = result.fetchall()
                    print(f"   实际使用的HANDLER_ID ({len(used_handlers)} 个):")
                    for handler, count in used_handlers[:10]:  # 显示使用最多的10个
                        print(f"     - {handler}: {count} 个批次")
                    
                    # 获取所有使用的HANDLER_ID
                    used_handler_set = {handler for handler, _ in used_handlers}
                    
                    # 对比可用设备中的HANDLER信息
                    available_handler_set = set()
                    for eqp in equipment_data:
                        handler = eqp.get('HANDLER_ID', '') or eqp.get('HANDLER_CONFIG', '')
                        if handler:
                            available_handler_set.add(handler)
                    
                    print(f"\n   设备使用分析:")
                    print(f"   - 排产中使用的HANDLER: {len(used_handler_set)} 个")
                    print(f"   - 设备表中可用HANDLER: {len(available_handler_set)} 个")
                    
                    # 找出未使用的HANDLER
                    unused_handlers = available_handler_set - used_handler_set
                    print(f"   - 未使用的HANDLER: {len(unused_handlers)} 个")
                    if unused_handlers:
                        print(f"     样本: {list(unused_handlers)[:10]}")
                    
                    # 找出排产中有但设备表中没有的HANDLER
                    missing_handlers = used_handler_set - available_handler_set
                    if missing_handlers:
                        print(f"   - 排产使用但设备表无的HANDLER: {len(missing_handlers)} 个")
                        print(f"     样本: {list(missing_handlers)[:10]}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_eqp_status_table()
    if success:
        print("\n🎉 EQP_STATUS表分析完成！")
    else:
        print("\n❌ 分析失败！")

if __name__ == "__main__":
    main()