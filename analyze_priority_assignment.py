#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析优先级分配问题
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)-8s | %(message)s')
logger = logging.getLogger('PriorityAnalysis')

def analyze_priority_assignment():
    """分析优先级分配问题"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app import db
            
            print("\n=== 当前优先级分配问题分析 ===")
            
            # 查询HCHC-C-012-6800机台的数据
            query = """
            SELECT 
                LOT_ID, 
                priority_score,
                comprehensive_score,
                PRIORITY,
                created_at
            FROM lotprioritydone 
            WHERE HANDLER_ID = 'HCHC-C-012-6800' 
            ORDER BY priority_score DESC
            """
            
            result = db.session.execute(db.text(query))
            rows = result.fetchall()
            
            if rows:
                print(f"📊 当前数据（按priority_score降序排列）:")
                print(f"{'批次ID':<15} {'优先级评分':<12} {'综合评分':<12} {'当前优先级':<10} {'期望优先级':<10}")
                print("-" * 75)
                
                expected_priority = 1
                for row in rows:
                    print(f"{row.LOT_ID:<15} {row.priority_score:<12} {row.comprehensive_score:<12} "
                          f"{row.PRIORITY:<10} {expected_priority:<10}")
                    expected_priority += 1
                
                print(f"\n🔍 问题识别:")
                print(f"- YX0125HB0400 评分最高(158.70)，当前优先级=4，应该是优先级=1")
                print(f"- YX0125GB0367 评分次高(71.70)，当前优先级=3，应该是优先级=2") 
                print(f"- YX0125GB0206 评分第三(71.40)，当前优先级=1，应该是优先级=3")
                
                print(f"\n💡 核心问题：优先级序号(PRIORITY字段)没有按评分高低重新分配")
                print(f"   需要修改排产算法中的优先级分配逻辑")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    success = analyze_priority_assignment()
    print("🎉 分析完成: 成功" if success else "❌ 分析完成: 失败")

if __name__ == "__main__":
    main()