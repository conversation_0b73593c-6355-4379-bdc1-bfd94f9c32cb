#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MySQL连接回收配置 - 分析连接管理参数
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('mysql_config_check.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('MySQLConfigCheck')

def check_mysql_timeout_config():
    """检查MySQL超时配置参数"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_connection, return_db_connection
            
            logger.info("🔍 检查MySQL连接回收相关配置...")
            
            conn = get_connection('aps')
            cursor = conn.cursor()
            
            # 检查超时相关参数
            timeout_queries = [
                "SHOW VARIABLES LIKE 'wait_timeout'",
                "SHOW VARIABLES LIKE 'interactive_timeout'", 
                "SHOW VARIABLES LIKE 'connect_timeout'",
                "SHOW VARIABLES LIKE 'net_read_timeout'",
                "SHOW VARIABLES LIKE 'net_write_timeout'"
            ]
            
            timeout_config = {}
            for query in timeout_queries:
                cursor.execute(query)
                result = cursor.fetchone()
                if result:
                    timeout_config[result[0]] = result[1]
            
            # 检查连接相关参数
            connection_queries = [
                "SHOW VARIABLES LIKE 'max_connections'",
                "SHOW VARIABLES LIKE 'max_user_connections'",
                "SHOW VARIABLES LIKE 'thread_cache_size'"
            ]
            
            connection_config = {}
            for query in connection_queries:
                cursor.execute(query)
                result = cursor.fetchone()
                if result:
                    connection_config[result[0]] = result[1]
            
            # 检查当前连接状态
            cursor.execute("SHOW STATUS LIKE '%connection%'")
            connection_status = {}
            results = cursor.fetchall()
            for result in results:
                connection_status[result[0]] = result[1]
            
            cursor.close()
            return_db_connection(conn, 'aps')
            
            # 输出分析结果
            logger.info("📊 MySQL超时配置:")
            for param, value in timeout_config.items():
                logger.info(f"  {param}: {value}秒")
            
            logger.info("📊 MySQL连接配置:")
            for param, value in connection_config.items():
                logger.info(f"  {param}: {value}")
            
            logger.info("📊 MySQL当前连接状态:")
            for param, value in connection_status.items():
                logger.info(f"  {param}: {value}")
            
            # 分析应用层vs数据库层配置差异
            app_pool_recycle = 3600  # 来自config.ini
            mysql_wait_timeout = int(timeout_config.get('wait_timeout', 28800))
            
            logger.info("🔍 配置差异分析:")
            logger.info(f"  应用层回收: {app_pool_recycle}秒 (1小时)")
            logger.info(f"  MySQL等待超时: {mysql_wait_timeout}秒 ({mysql_wait_timeout/3600:.1f}小时)")
            
            if mysql_wait_timeout > app_pool_recycle * 2:
                logger.warning(f"⚠️ 配置差异过大：MySQL超时({mysql_wait_timeout}s) >> 应用回收({app_pool_recycle}s)")
                logger.warning(f"   建议同步：将MySQL wait_timeout调整为{app_pool_recycle}s或应用pool_recycle调整为{mysql_wait_timeout}s")
            else:
                logger.info("✅ 应用层与数据库层超时配置基本匹配")
            
            return {
                'timeout_config': timeout_config,
                'connection_config': connection_config,
                'connection_status': connection_status,
                'config_mismatch': mysql_wait_timeout > app_pool_recycle * 2
            }
            
    except Exception as e:
        logger.error(f"❌ MySQL配置检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def check_current_connection_usage_patterns():
    """分析当前代码中的连接使用模式"""
    try:
        logger.info("🔍 分析real_scheduling_service.py中的连接使用模式...")
        
        # 读取排产服务代码
        with open('app/services/real_scheduling_service.py', 'r', encoding='utf-8') as f:
            scheduling_code = f.read()
        
        # 统计连接使用模式
        patterns = {
            'get_connection_calls': scheduling_code.count('get_connection'),
            'get_db_connection_calls': scheduling_code.count('get_db_connection'), 
            'batch_processing_usage': scheduling_code.count('batch_processing_connection'),
            'cursor_operations': scheduling_code.count('cursor()'),
            'loop_with_db_operations': 0  # 需要更复杂的分析
        }
        
        # 搜索循环中的数据库操作模式
        import re
        loop_patterns = re.findall(r'for\s+\w+\s+in\s+\w+.*?get_connection', scheduling_code, re.DOTALL)
        patterns['potential_loop_connections'] = len(loop_patterns)
        
        logger.info("📊 当前连接使用模式分析:")
        logger.info(f"  get_connection调用: {patterns['get_connection_calls']}")
        logger.info(f"  get_db_connection调用: {patterns['get_db_connection_calls']}")
        logger.info(f"  批量处理连接使用: {patterns['batch_processing_usage']}")
        logger.info(f"  游标操作: {patterns['cursor_operations']}")
        logger.info(f"  潜在循环连接: {patterns['potential_loop_connections']}")
        
        if patterns['batch_processing_usage'] == 0:
            logger.warning("⚠️ 发现问题：real_scheduling_service.py未使用batch_processing_connection")
            logger.warning("   这是268个批次重复连接的根本原因！")
        
        if patterns['potential_loop_connections'] > 0:
            logger.warning(f"⚠️ 发现 {patterns['potential_loop_connections']} 个潜在的循环连接模式")
            logger.warning("   这些应该改为batch_processing_connection")
        
        return patterns
        
    except Exception as e:
        logger.error(f"❌ 连接模式分析失败: {e}")
        return None

def main():
    """入口函数"""
    logger.info("🔍 MySQL连接回收配置检查启动")
    logger.info("🎯 目标: 分析用户提出的连接回收和持久连接策略")
    
    try:
        # 检查MySQL配置
        mysql_config = check_mysql_timeout_config()
        
        # 分析代码连接模式
        connection_patterns = check_current_connection_usage_patterns()
        
        # 生成分析报告
        analysis_report = {
            "timestamp": datetime.now().isoformat(),
            "analysis_type": "mysql_connection_lifecycle_analysis",
            "user_suggestion_evaluation": "用户分析完全正确：重复连接消耗是根本问题",
            "mysql_config": mysql_config,
            "connection_patterns": connection_patterns,
            "recommendations": [
                "1. 立即修改real_scheduling_service.py使用batch_processing_connection",
                "2. 同步MySQL wait_timeout与应用pool_recycle参数", 
                "3. 实现用户会话绑定连接管理",
                "4. 完善空闲连接自动断开机制"
            ]
        }
        
        import json
        with open('mysql_connection_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, indent=2, ensure_ascii=False)
        
        logger.info("📝 MySQL连接分析报告已生成: mysql_connection_analysis_report.json")
        logger.info("🎉 配置检查: 通过")
        print("🎉 检查: 通过")
        
    except Exception as e:
        logger.error(f"❌ MySQL配置检查失败: {e}")
        print("❌ 检查: 失败")

if __name__ == "__main__":
    main()