#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析排产算法，找出为什么44台设备没有被使用
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('scheduling_algorithm_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('SchedulingAlgorithmAnalyzer')

def analyze_scheduling_algorithm():
    """深度分析排产算法，找出设备未被使用的原因"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.real_scheduling_service import RealSchedulingService
            from app.services.data_source_manager import DataSourceManager
            from app import db
            from sqlalchemy import text
            
            scheduling_service = RealSchedulingService()
            data_manager = DataSourceManager()
            
            print("="*80)
            print("🔍 深度分析排产算法 - 为什么44台设备没有被使用")
            print("="*80)
            
            # ==================== 第一步：获取基础数据 ====================
            print(f"\n📊 第一步：获取基础数据...")
            
            preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
            
            wait_lots = preloaded_data.get('wait_lots', [])
            equipment_status = preloaded_data.get('equipment_status', [])
            
            print(f"   待排产批次: {len(wait_lots)} 个")
            print(f"   设备状态: {len(equipment_status)} 台")
            
            # ==================== 第二步：分析设备状态和过滤条件 ====================
            print(f"\n🔍 第二步：分析设备状态和过滤条件...")
            
            # 统计设备STATUS分布
            status_distribution = defaultdict(int)
            available_equipment = []
            
            for eqp in equipment_status:
                status = (eqp.get('STATUS') or '').strip()
                status_distribution[status or '空'] += 1
                
                # 检查设备可用性判断逻辑
                handler_id = (eqp.get('HANDLER_ID') or '').strip()
                device = (eqp.get('DEVICE') or '').strip()
                stage = (eqp.get('STAGE') or '').strip()
                
                if handler_id:  # 只要有HANDLER_ID就算可用设备
                    available_equipment.append({
                        'HANDLER_ID': handler_id,
                        'DEVICE': device,
                        'STAGE': stage,
                        'STATUS': status,
                        'EQP_CLASS': eqp.get('EQP_CLASS', ''),
                        'HANDLER_CONFIG': eqp.get('HANDLER_CONFIG', ''),
                        'KIT_PN': eqp.get('KIT_PN', '')
                    })
            
            print(f"   设备STATUS分布:")
            for status, count in sorted(status_distribution.items(), key=lambda x: x[1], reverse=True):
                print(f"     - '{status}': {count} 台")
            
            print(f"   有HANDLER_ID的设备: {len(available_equipment)} 台")
            
            # ==================== 第三步：分析已排产的设备使用情况 ====================
            print(f"\n📊 第三步：分析已排产的设备使用情况...")
            
            with db.engine.connect() as conn:
                # 获取已排产批次的设备使用
                result = conn.execute(text("""
                    SELECT HANDLER_ID, DEVICE, STAGE, COUNT(*) as batch_count 
                    FROM lotprioritydone 
                    WHERE HANDLER_ID IS NOT NULL AND HANDLER_ID != ''
                    GROUP BY HANDLER_ID, DEVICE, STAGE
                    ORDER BY batch_count DESC
                """))
                used_equipment = result.fetchall()
                
                used_handler_ids = set()
                for handler_id, device, stage, count in used_equipment:
                    used_handler_ids.add(handler_id)
                    
                print(f"   已排产使用的设备: {len(used_handler_ids)} 台")
                
                # 找出未使用的设备
                available_handler_ids = {eqp['HANDLER_ID'] for eqp in available_equipment}
                unused_handler_ids = available_handler_ids - used_handler_ids
                
                print(f"   未使用的设备: {len(unused_handler_ids)} 台")
                
                # 显示未使用设备的详细信息
                print(f"\n   未使用设备详细分析:")
                unused_equipment_details = []
                for eqp in available_equipment:
                    if eqp['HANDLER_ID'] in unused_handler_ids:
                        unused_equipment_details.append(eqp)
                
                # 按STATUS分组分析未使用设备
                unused_by_status = defaultdict(list)
                for eqp in unused_equipment_details:
                    status = eqp['STATUS'] or '空'
                    unused_by_status[status].append(eqp)
                
                for status, eqps in unused_by_status.items():
                    print(f"     STATUS='{status}': {len(eqps)} 台")
                    for i, eqp in enumerate(eqps[:3], 1):  # 显示前3台
                        print(f"       {i}. {eqp['HANDLER_ID']} | DEVICE:{eqp['DEVICE'][:20]}{'...' if len(eqp['DEVICE'])>20 else ''} | STAGE:{eqp['STAGE']}")
            
            # ==================== 第四步：分析排产算法中的设备筛选逻辑 ====================
            print(f"\n🔍 第四步：分析排产算法中的设备筛选逻辑...")
            
            # 随机测试几个待排产批次，看看设备筛选过程
            test_lots = wait_lots[:5] if len(wait_lots) >= 5 else wait_lots
            
            print(f"   测试 {len(test_lots)} 个批次的设备匹配过程:")
            
            for i, lot in enumerate(test_lots, 1):
                lot_id = lot.get('LOT_ID', '')
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                pkg_pn = (lot.get('PKG_PN') or '').strip()
                
                print(f"\n   批次 {i}: {lot_id}")
                print(f"   - 需要DEVICE: {device}")
                print(f"   - 需要STAGE: {stage}")
                print(f"   - PKG_PN: {pkg_pn}")
                
                # 手动执行设备匹配逻辑
                suitable_equipment = []
                
                for eqp in available_equipment:
                    eqp_device = (eqp['DEVICE'] or '').strip()
                    eqp_stage = (eqp['STAGE'] or '').strip()
                    
                    # 检查DEVICE匹配
                    device_match = False
                    if device and eqp_device:
                        device_match = (device == eqp_device)
                    
                    # 检查STAGE匹配
                    stage_match = False
                    if stage and eqp_stage:
                        stage_match = (stage == eqp_stage)
                    
                    # 如果设备有批次在运行，则不可用
                    lot_id_on_eqp = (eqp.get('LOT_ID') or '').strip() if isinstance(eqp, dict) else ''
                    
                    # 从equipment_status中获取LOT_ID信息
                    for orig_eqp in equipment_status:
                        if orig_eqp.get('HANDLER_ID') == eqp['HANDLER_ID']:
                            lot_id_on_eqp = (orig_eqp.get('LOT_ID') or '').strip()
                            break
                    
                    is_busy = bool(lot_id_on_eqp)
                    
                    if device_match and stage_match and not is_busy:
                        suitable_equipment.append({
                            'HANDLER_ID': eqp['HANDLER_ID'],
                            'STATUS': eqp['STATUS'],
                            'DEVICE_MATCH': device_match,
                            'STAGE_MATCH': stage_match,
                            'IS_BUSY': is_busy,
                            'LOT_ID_ON_EQP': lot_id_on_eqp
                        })
                
                print(f"   - 完全匹配的设备: {len(suitable_equipment)} 台")
                
                if suitable_equipment:
                    print(f"     样本设备:")
                    for j, eqp in enumerate(suitable_equipment[:3], 1):
                        print(f"       {j}. {eqp['HANDLER_ID']} (STATUS: {eqp['STATUS']})")
                else:
                    print(f"     ❌ 没有找到匹配的设备")
                    
                    # 分析为什么没有匹配
                    print(f"     原因分析:")
                    device_matches = sum(1 for eqp in available_equipment 
                                       if (eqp['DEVICE'] or '').strip() == device)
                    stage_matches = sum(1 for eqp in available_equipment 
                                      if (eqp['STAGE'] or '').strip() == stage)
                    both_matches = sum(1 for eqp in available_equipment 
                                     if (eqp['DEVICE'] or '').strip() == device 
                                     and (eqp['STAGE'] or '').strip() == stage)
                    
                    print(f"       - DEVICE匹配的设备: {device_matches} 台")
                    print(f"       - STAGE匹配的设备: {stage_matches} 台")
                    print(f"       - DEVICE+STAGE都匹配: {both_matches} 台")
                    
                    if both_matches > 0:
                        busy_count = 0
                        for eqp in available_equipment:
                            eqp_device = (eqp['DEVICE'] or '').strip()
                            eqp_stage = (eqp['STAGE'] or '').strip()
                            if eqp_device == device and eqp_stage == stage:
                                # 检查是否忙碌
                                for orig_eqp in equipment_status:
                                    if orig_eqp.get('HANDLER_ID') == eqp['HANDLER_ID']:
                                        lot_id_on_eqp = (orig_eqp.get('LOT_ID') or '').strip()
                                        if lot_id_on_eqp:
                                            busy_count += 1
                                        break
                        print(f"       - 匹配但忙碌的设备: {busy_count} 台")
            
            # ==================== 第五步：总结问题原因 ====================
            print(f"\n💡 第五步：问题原因总结...")
            
            print(f"   🎯 设备利用率低的可能原因:")
            print(f"   1. 设备配置不完整:")
            
            no_device_count = sum(1 for eqp in available_equipment if not eqp['DEVICE'])
            no_stage_count = sum(1 for eqp in available_equipment if not eqp['STAGE'])
            print(f"      - 缺少DEVICE字段: {no_device_count} 台")
            print(f"      - 缺少STAGE字段: {no_stage_count} 台")
            
            print(f"   2. 设备忙碌状态:")
            busy_count = sum(1 for eqp in equipment_status if (eqp.get('LOT_ID') or '').strip())
            print(f"      - 当前有批次在运行: {busy_count} 台")
            
            print(f"   3. 批次需求与设备配置不匹配:")
            # 统计批次需要的DEVICE-STAGE组合
            lot_requirements = set()
            for lot in wait_lots:
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                if device and stage:
                    lot_requirements.add((device, stage))
            
            # 统计设备能提供的DEVICE-STAGE组合
            equipment_capabilities = set()
            for eqp in available_equipment:
                device = (eqp['DEVICE'] or '').strip()
                stage = (eqp['STAGE'] or '').strip()
                if device and stage:
                    equipment_capabilities.add((device, stage))
            
            matched_combinations = lot_requirements & equipment_capabilities
            unmatched_lot_needs = lot_requirements - equipment_capabilities
            unused_capabilities = equipment_capabilities - lot_requirements
            
            print(f"      - 批次需要的DEVICE-STAGE组合: {len(lot_requirements)} 种")
            print(f"      - 设备能提供的组合: {len(equipment_capabilities)} 种")
            print(f"      - 能匹配的组合: {len(matched_combinations)} 种")
            print(f"      - 无设备可用的需求: {len(unmatched_lot_needs)} 种")
            print(f"      - 闲置的设备能力: {len(unused_capabilities)} 种")
            
            if unmatched_lot_needs:
                print(f"      无设备可用的需求示例:")
                for i, (device, stage) in enumerate(list(unmatched_lot_needs)[:5], 1):
                    print(f"        {i}. {device} - {stage}")
            
            if unused_capabilities:
                print(f"      闲置设备能力示例:")
                for i, (device, stage) in enumerate(list(unused_capabilities)[:5], 1):
                    print(f"        {i}. {device} - {stage}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_scheduling_algorithm()
    if success:
        print("\n🎉 排产算法分析完成！")
    else:
        print("\n❌ 分析失败！")

if __name__ == "__main__":
    main()