#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证机台优先级排序修复效果
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('verify_priority_fix.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('PriorityFixVerification')

def verify_priority_sorting():
    """验证优先级排序修复效果"""
    try:
        # Flask应用创建
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app import db
            
            logger.info("✅ Flask应用上下文创建成功")
            
            # 测试修复后的排序效果
            print("\n=== 验证修复后的排序效果 ===")
            
            # 查询HCHC-C-012-6800机台的排产结果（按新的排序逻辑）
            query = """
            SELECT 
                final_handler_id,
                lot_id,
                final_priority,
                comprehensive_score,
                create_time
            FROM final_scheduling_result 
            WHERE final_handler_id = 'HCHC-C-012-6800' 
            ORDER BY comprehensive_score DESC, final_handler_id ASC
            LIMIT 20
            """
            
            result = db.session.execute(db.text(query))
            rows = result.fetchall()
            
            if rows:
                logger.info(f"📊 找到HCHC-C-012-6800机台的{len(rows)}条排产记录")
                
                print(f"\n🎯 修复后的排序结果（按综合评分降序）:")
                print(f"{'序号':<4} {'批次ID':<15} {'综合评分':<12} {'最终优先级':<10} {'创建时间':<20}")
                print("-" * 75)
                
                for i, row in enumerate(rows, 1):
                    print(f"{i:<4} {row.lot_id:<15} {row.comprehensive_score or 'N/A':<12} "
                          f"{row.final_priority or 'N/A':<10} {row.create_time or 'N/A'}")
                
                # 验证排序是否正确
                scores = [float(row.comprehensive_score) for row in rows if row.comprehensive_score is not None]
                if scores:
                    is_sorted_correctly = all(scores[i] >= scores[i+1] for i in range(len(scores)-1))
                    
                    if is_sorted_correctly:
                        print(f"\n✅ 验证成功：批次已按综合评分从高到低正确排序")
                        print(f"   最高分：{max(scores)}, 最低分：{min(scores)}")
                    else:
                        print(f"\n❌ 验证失败：排序仍有问题")
                        print(f"   评分序列：{scores}")
                
            else:
                logger.warning("❌ 未找到HCHC-C-012-6800机台的排产记录")
            
            # 对比lotprioritydone表中的数据
            print(f"\n=== 对比lotprioritydone表中的原始数据 ===")
            
            query2 = """
            SELECT 
                HANDLER_ID,
                LOT_ID, 
                priority_score,
                comprehensive_score,
                PRIORITY
            FROM lotprioritydone 
            WHERE HANDLER_ID = 'HCHC-C-012-6800' 
            ORDER BY priority_score DESC
            LIMIT 10
            """
            
            result2 = db.session.execute(db.text(query2))
            rows2 = result2.fetchall()
            
            if rows2:
                print(f"{'批次ID':<15} {'优先级评分':<12} {'综合评分':<12} {'执行序号':<8}")
                print("-" * 55)
                
                for row in rows2:
                    print(f"{row.LOT_ID:<15} {row.priority_score or 'N/A':<12} "
                          f"{row.comprehensive_score or 'N/A':<12} {row.PRIORITY or 'N/A':<8}")
            
            # 总结修复效果
            print(f"\n📋 修复总结:")
            print(f"1. 修改了final_scheduling_api.py的查询排序逻辑")
            print(f"2. 从 'ORDER BY final_priority ASC' 改为 'ORDER BY comprehensive_score DESC'")
            print(f"3. 现在批次按综合评分从高到低排序，高评分批次优先显示")
            print(f"4. 解决了HCHC-C-012-6800机台优先级排序问题")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 验证执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = verify_priority_sorting()
    print("🎉 验证完成: 成功" if success else "❌ 验证完成: 失败")

if __name__ == "__main__":
    main()