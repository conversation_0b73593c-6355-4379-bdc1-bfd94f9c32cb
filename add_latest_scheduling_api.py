#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为failed-lots页面添加最新调度会话时间API
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('add_latest_scheduling_api.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('AddLatestSchedulingAPI')

def add_latest_scheduling_api():
    """添加获取最新调度会话时间的API"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 读取done_lots_api.py文件
            api_file_path = "app/api_v2/production/done_lots_api.py"
            
            with open(api_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查API是否已经存在
            if 'get-latest-scheduling-session' in content:
                logger.info("⚠️ 最新调度会话API已存在")
                return True
            
            # 添加新的API端点
            new_api_code = '''

@done_lots_bp.route(get_api_route('production/get-latest-scheduling-session'), methods=['GET'])
def get_latest_scheduling_session():
    """获取最新的调度会话时间，用于failed-lots页面的自动刷新监控"""
    try:
        from app.utils.db_connection_pool import get_db_connection_context
        
        logger.info("🔍 查询最新调度会话时间...")
        
        with get_db_connection_context() as conn:
            cursor = conn.cursor()
            
            # 查询最新的失败批次时间戳作为调度会话时间
            query_sql = """
            SELECT MAX(timestamp) as latest_session_time
            FROM scheduling_failed_lots
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 DAY)
            """
            
            cursor.execute(query_sql)
            result = cursor.fetchone()
            
            if isinstance(result, dict):
                latest_time = result.get('latest_session_time')
            else:
                latest_time = result[0] if result else None
            
            # 格式化时间
            if latest_time:
                if isinstance(latest_time, datetime):
                    latest_time_str = latest_time.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    latest_time_str = str(latest_time)
            else:
                latest_time_str = None
            
            return jsonify({
                'success': True,
                'data': {
                    'latest_session_time': latest_time_str,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                'message': '最新调度会话时间获取成功'
            })
            
    except Exception as e:
        logger.error(f"❌ 获取最新调度会话时间失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取最新调度会话时间失败'
        }), 500'''
            
            # 在文件末尾添加新的API代码（在最后一个函数之后）
            if content.strip().endswith('{% endblock %}'):
                # 如果文件以模板结束，在之前插入
                insert_pos = content.rfind('{% endblock %}')
                new_content = content[:insert_pos] + new_api_code + '\n' + content[insert_pos:]
            else:
                # 直接在文件末尾添加
                new_content = content + new_api_code
            
            # 写回文件
            with open(api_file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.info("✅ 成功添加最新调度会话时间API")
            return True
            
    except Exception as e:
        logger.error(f"❌ 添加API失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = add_latest_scheduling_api()
    print("🎉 添加: 成功" if success else "❌ 添加: 失败")

if __name__ == "__main__":
    main()