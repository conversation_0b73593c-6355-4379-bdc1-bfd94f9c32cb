#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
匹配策略优化方案设计与实现
基于数据完整性分析结果，设计新的匹配策略以提升设备利用率
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict
import json

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('matching_optimization.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('MatchingOptimizer')

def design_optimized_matching_strategy():
    """设计优化的匹配策略"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            from app.services.real_scheduling_service import RealSchedulingService
            
            data_manager = DataSourceManager()
            scheduling_service = RealSchedulingService()
            
            print("="*80)
            print("🔧 匹配策略优化方案设计与测试")
            print("="*80)
            
            # ==================== 获取基础数据 ====================
            print(f"\n📊 获取基础数据...")
            
            # 获取待排产批次
            wait_lots_result = data_manager.get_table_data('et_wait_lot')
            wait_lots = wait_lots_result.get('data', [])
            
            # 获取测试规格数据
            test_spec_result = data_manager.get_table_data('et_ft_test_spec')
            test_specs = test_spec_result.get('data', [])
            
            # 获取设备数据
            equipment_result = data_manager.get_table_data('EQP_STATUS')
            all_equipment = equipment_result.get('data', [])
            
            print(f"   待排产批次: {len(wait_lots)} 个")
            print(f"   测试规格: {len(test_specs)} 条")
            print(f"   设备总数: {len(all_equipment)} 台")
            
            # ==================== 分析当前匹配失败的原因 ====================
            print(f"\n🔍 第一步：分析当前匹配策略的问题")
            
            # 预加载数据
            preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
            
            # 统计当前匹配成功率
            current_strategy_results = {
                'total_attempts': 0,
                'successful_matches': 0,
                'failure_reasons': defaultdict(int),
                'successful_combinations': set()
            }
            
            # 测试前20个批次的匹配情况
            for lot in wait_lots[:20]:
                lot_id = lot.get('LOT_ID', '')
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                
                current_strategy_results['total_attempts'] += 1
                
                try:
                    # 使用当前策略获取配置需求
                    lot_requirements = scheduling_service.get_lot_configuration_requirements_optimized(lot, preloaded_data)
                    
                    if lot_requirements:
                        current_strategy_results['successful_matches'] += 1
                        current_strategy_results['successful_combinations'].add(f"{device}-{stage}")
                    else:
                        # 分析失败原因
                        # 检查ET_FT_TEST_SPEC
                        spec_matches = [s for s in test_specs 
                                      if (s.get('DEVICE') or '').strip() == device 
                                      and (s.get('STAGE') or '').strip() == stage]
                        
                        if not spec_matches:
                            current_strategy_results['failure_reasons']['无ET_FT_TEST_SPEC匹配'] += 1
                        else:
                            released_matches = [s for s in spec_matches if (s.get('APPROVAL_STATE') or '').strip() == 'Released']
                            if not released_matches:
                                current_strategy_results['failure_reasons']['无Released状态的规格'] += 1
                            else:
                                current_strategy_results['failure_reasons']['recipe_file数据缺失'] += 1
                                
                except Exception as e:
                    current_strategy_results['failure_reasons'][f'异常:{str(e)[:30]}'] += 1
            
            success_rate = current_strategy_results['successful_matches'] / current_strategy_results['total_attempts'] * 100
            
            print(f"   当前匹配策略结果:")
            print(f"   - 测试批次: {current_strategy_results['total_attempts']}")
            print(f"   - 成功匹配: {current_strategy_results['successful_matches']}")
            print(f"   - 成功率: {success_rate:.1f}%")
            
            print(f"\n   失败原因统计:")
            for reason, count in current_strategy_results['failure_reasons'].items():
                print(f"     - {reason}: {count} 次")
            
            # ==================== 设计新的匹配策略 ====================
            print(f"\n🚀 第二步：设计优化的匹配策略")
            
            print(f"   新策略设计原则:")
            print(f"   1. 放宽审批状态要求 (不仅限于Released)")
            print(f"   2. 增强模糊匹配能力")
            print(f"   3. 创建智能兜底配置生成")
            print(f"   4. 建立设备能力通用映射")
            print(f"   5. 支持跨工序设备复用")
            
            # ==================== 实现新的匹配算法 ====================
            def optimized_get_lot_configuration(lot, test_specs, equipment_list):
                """优化的批次配置获取算法"""
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                lot_id = lot.get('LOT_ID', '')
                
                if not device or not stage:
                    return None
                
                # 策略1: 精确匹配 (Released状态)
                for spec in test_specs:
                    spec_device = (spec.get('DEVICE') or '').strip()
                    spec_stage = (spec.get('STAGE') or '').strip()
                    spec_approval = (spec.get('APPROVAL_STATE') or '').strip()
                    
                    if (spec_device == device and 
                        spec_stage == stage and 
                        spec_approval == 'Released'):
                        
                        return {
                            'DEVICE': device,
                            'STAGE': stage,
                            'HANDLER': (spec.get('HANDLER') or '').strip(),
                            'TB_PN': (spec.get('TB_PN') or '').strip() or 'NA',
                            'HB_PN': (spec.get('HB_PN') or '').strip(),
                            'PKG_PN': (spec.get('PKG_PN') or '').strip(),
                            'TESTER': (spec.get('TESTER') or '').strip(),
                            'UPH': spec.get('UPH', 0),
                            'MATCH_TYPE': 'EXACT_RELEASED',
                            'SOURCE': 'ET_FT_TEST_SPEC'
                        }
                
                # 策略2: 精确匹配 (任何状态)
                for spec in test_specs:
                    spec_device = (spec.get('DEVICE') or '').strip()
                    spec_stage = (spec.get('STAGE') or '').strip()
                    
                    if spec_device == device and spec_stage == stage:
                        return {
                            'DEVICE': device,
                            'STAGE': stage,
                            'HANDLER': (spec.get('HANDLER') or '').strip(),
                            'TB_PN': (spec.get('TB_PN') or '').strip() or 'NA',
                            'HB_PN': (spec.get('HB_PN') or '').strip(),
                            'PKG_PN': (spec.get('PKG_PN') or '').strip(),
                            'TESTER': (spec.get('TESTER') or '').strip(),
                            'UPH': spec.get('UPH', 0),
                            'MATCH_TYPE': 'EXACT_ANY_STATE',
                            'APPROVAL_STATE': (spec.get('APPROVAL_STATE') or '').strip(),
                            'SOURCE': 'ET_FT_TEST_SPEC'
                        }
                
                # 策略3: 同产品不同工序匹配 (适用于LSTR、BAKING等特殊工序)
                if stage in ['LSTR', 'BAKING']:
                    for spec in test_specs:
                        spec_device = (spec.get('DEVICE') or '').strip()
                        spec_stage = (spec.get('STAGE') or '').strip()
                        
                        if spec_device == device:  # 同产品，允许不同工序
                            return {
                                'DEVICE': device,
                                'STAGE': stage,  # 保持原工序
                                'HANDLER': (spec.get('HANDLER') or '').strip(),
                                'TB_PN': (spec.get('TB_PN') or '').strip() or 'NA',
                                'HB_PN': (spec.get('HB_PN') or '').strip(),
                                'PKG_PN': (spec.get('PKG_PN') or '').strip(),
                                'TESTER': (spec.get('TESTER') or '').strip(),
                                'UPH': spec.get('UPH', 100),  # 特殊工序默认UPH
                                'MATCH_TYPE': 'CROSS_STAGE',
                                'ORIGINAL_STAGE': spec_stage,
                                'SOURCE': 'ET_FT_TEST_SPEC_CROSS'
                            }
                
                # 策略4: 模糊匹配 (产品系列匹配)
                device_base = device.split('_')[0] if '_' in device else device[:10]
                
                best_match = None
                best_score = 0
                
                for spec in test_specs:
                    spec_device = (spec.get('DEVICE') or '').strip()
                    spec_stage = (spec.get('STAGE') or '').strip()
                    
                    score = 0
                    
                    # 设备相似性评分
                    if spec_device == device:
                        score += 100
                    elif device_base in spec_device or spec_device in device:
                        score += 70
                    elif spec_device.split('_')[0] == device_base if '_' in spec_device else False:
                        score += 50
                    
                    # 工序评分
                    if spec_stage == stage:
                        score += 50
                    elif stage in ['LSTR', 'BAKING'] and spec_stage in ['Trim', 'Hot', 'Cold']:
                        score += 30  # 特殊工序可以使用类似工序的配置
                    
                    if score > best_score and score >= 70:
                        best_score = score
                        best_match = spec
                
                if best_match:
                    return {
                        'DEVICE': device,
                        'STAGE': stage,
                        'HANDLER': (best_match.get('HANDLER') or '').strip(),
                        'TB_PN': (best_match.get('TB_PN') or '').strip() or 'NA',
                        'HB_PN': (best_match.get('HB_PN') or '').strip(),
                        'PKG_PN': (best_match.get('PKG_PN') or '').strip(),
                        'TESTER': (best_match.get('TESTER') or '').strip(),
                        'UPH': best_match.get('UPH', 100),
                        'MATCH_TYPE': 'FUZZY_MATCH',
                        'MATCH_SCORE': best_score,
                        'REFERENCE_DEVICE': (best_match.get('DEVICE') or '').strip(),
                        'REFERENCE_STAGE': (best_match.get('STAGE') or '').strip(),
                        'SOURCE': 'ET_FT_TEST_SPEC_FUZZY'
                    }
                
                # 策略5: 智能兜底配置
                # 基于工序类型生成通用配置
                stage_configs = {
                    'LSTR': {'HANDLER': 'Turret', 'UPH': 120, 'TB_PN': 'TB_LSTR'},
                    'BAKING': {'HANDLER': 'PnP', 'UPH': 80, 'TB_PN': 'TB_BAKING'},
                    'Trim': {'HANDLER': 'Turret', 'UPH': 150, 'TB_PN': 'TB_TRIM'},
                    'Hot': {'HANDLER': 'Turret', 'UPH': 100, 'TB_PN': 'TB_HOT'},
                    'Cold': {'HANDLER': 'Turret', 'UPH': 100, 'TB_PN': 'TB_COLD'},
                    'ROOM-TTR': {'HANDLER': 'Turret', 'UPH': 200, 'TB_PN': 'TB_ROOM'},
                    'ROOM-TEST': {'HANDLER': 'PnP', 'UPH': 180, 'TB_PN': 'TB_ROOM'}
                }
                
                if stage in stage_configs:
                    config = stage_configs[stage]
                    return {
                        'DEVICE': device,
                        'STAGE': stage,
                        'HANDLER': config['HANDLER'],
                        'TB_PN': config['TB_PN'],
                        'HB_PN': 'HB_GENERIC',
                        'PKG_PN': f'PKG_{device.split("_")[0] if "_" in device else device[:8]}',
                        'TESTER': config['HANDLER'],
                        'UPH': config['UPH'],
                        'MATCH_TYPE': 'FALLBACK_CONFIG',
                        'SOURCE': 'INTELLIGENT_FALLBACK'
                    }
                
                return None
            
            # ==================== 测试新的匹配策略 ====================
            print(f"\n🧪 第三步：测试优化的匹配策略")
            
            optimized_results = {
                'total_attempts': 0,
                'successful_matches': 0,
                'match_types': defaultdict(int),
                'successful_combinations': set(),
                'improvements': []
            }
            
            # 测试前50个批次
            for lot in wait_lots[:50]:
                lot_id = lot.get('LOT_ID', '')
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                
                optimized_results['total_attempts'] += 1
                
                # 使用新的匹配算法
                config = optimized_get_lot_configuration(lot, test_specs, all_equipment)
                
                if config:
                    optimized_results['successful_matches'] += 1
                    optimized_results['successful_combinations'].add(f"{device}-{stage}")
                    optimized_results['match_types'][config['MATCH_TYPE']] += 1
                    
                    # 如果是之前失败的组合，记录为改进
                    combo = f"{device}-{stage}"
                    if combo not in current_strategy_results['successful_combinations']:
                        optimized_results['improvements'].append({
                            'lot_id': lot_id,
                            'device': device,
                            'stage': stage,
                            'match_type': config['MATCH_TYPE'],
                            'source': config['SOURCE']
                        })
            
            new_success_rate = optimized_results['successful_matches'] / optimized_results['total_attempts'] * 100
            improvement = new_success_rate - success_rate
            
            print(f"   优化后匹配策略结果:")
            print(f"   - 测试批次: {optimized_results['total_attempts']}")
            print(f"   - 成功匹配: {optimized_results['successful_matches']}")
            print(f"   - 成功率: {new_success_rate:.1f}%")
            print(f"   - 提升幅度: +{improvement:.1f}%")
            
            print(f"\n   匹配类型分布:")
            for match_type, count in optimized_results['match_types'].items():
                print(f"     - {match_type}: {count} 次")
            
            print(f"\n   改进的批次示例 (前10个):")
            for i, improvement in enumerate(optimized_results['improvements'][:10], 1):
                print(f"     {i}. {improvement['lot_id']}: {improvement['device']}-{improvement['stage']}")
                print(f"        匹配类型: {improvement['match_type']}, 来源: {improvement['source']}")
            
            # ==================== 预估设备利用率提升效果 ====================
            print(f"\n🚀 第四步：预估设备利用率提升效果")
            
            # 获取当前已使用设备
            scheduled_result = data_manager.get_table_data('lotprioritydone')
            current_scheduled = scheduled_result.get('data', [])
            current_used_equipment = set(lot.get('HANDLER_ID', '').strip() for lot in current_scheduled if lot.get('HANDLER_ID'))
            
            current_utilization = len(current_used_equipment) / len(all_equipment) * 100
            
            # 估算新策略能激活多少设备
            additional_batches = len(optimized_results['improvements'])
            additional_combinations = len(optimized_results['successful_combinations'] - current_strategy_results['successful_combinations'])
            
            # 保守估计：假设每3-5个新匹配的批次组合能激活1台设备
            estimated_new_equipment = additional_combinations // 4
            estimated_new_utilization = (len(current_used_equipment) + estimated_new_equipment) / len(all_equipment) * 100
            utilization_improvement = estimated_new_utilization - current_utilization
            
            print(f"   设备利用率预估:")
            print(f"   - 当前利用率: {current_utilization:.1f}% ({len(current_used_equipment)}/{len(all_equipment)}台)")
            print(f"   - 新增可匹配组合: {additional_combinations} 种")
            print(f"   - 预估激活设备: {estimated_new_equipment} 台")
            print(f"   - 预估新利用率: {estimated_new_utilization:.1f}%")
            print(f"   - 利用率提升: +{utilization_improvement:.1f}%")
            
            # ==================== 风险评估 ====================
            print(f"\n⚠️ 第五步：新策略风险评估")
            
            risk_analysis = {
                'low_risk': 0,
                'medium_risk': 0,
                'high_risk': 0,
                'risk_details': []
            }
            
            for improvement in optimized_results['improvements']:
                match_type = improvement['match_type']
                risk_level = 'low'
                risk_reason = ''
                
                if match_type == 'EXACT_ANY_STATE':
                    risk_level = 'medium'
                    risk_reason = '使用非Released状态的测试规格'
                elif match_type == 'CROSS_STAGE':
                    risk_level = 'medium'
                    risk_reason = '跨工序配置匹配，需验证工艺兼容性'
                elif match_type == 'FUZZY_MATCH':
                    risk_level = 'high'
                    risk_reason = '模糊匹配可能存在产品兼容性风险'
                elif match_type == 'FALLBACK_CONFIG':
                    risk_level = 'medium'
                    risk_reason = '使用兜底配置，需确认实际工艺参数'
                
                risk_analysis[f'{risk_level}_risk'] += 1
                if risk_level in ['medium', 'high']:
                    risk_analysis['risk_details'].append({
                        'lot_id': improvement['lot_id'],
                        'combo': f"{improvement['device']}-{improvement['stage']}",
                        'risk_level': risk_level,
                        'risk_reason': risk_reason,
                        'match_type': match_type
                    })
            
            print(f"   风险等级分布:")
            print(f"   - 低风险: {risk_analysis['low_risk']} 个改进")
            print(f"   - 中风险: {risk_analysis['medium_risk']} 个改进")
            print(f"   - 高风险: {risk_analysis['high_risk']} 个改进")
            
            if risk_analysis['risk_details']:
                print(f"\n   需要注意的风险项目 (前8个):")
                for i, risk in enumerate(risk_analysis['risk_details'][:8], 1):
                    print(f"     {i}. {risk['combo']} ({risk['risk_level'].upper()})")
                    print(f"        风险: {risk['risk_reason']}")
            
            # ==================== 实施建议 ====================
            print(f"\n💡 第六步：实施建议")
            
            print(f"   📋 分阶段实施方案:")
            print(f"   第一阶段 (低风险): 实施精确匹配策略 (任何审批状态)")
            print(f"   - 影响: {risk_analysis['low_risk']} 个批次")
            print(f"   - 风险: 极低")
            print(f"   - 建议: 立即实施")
            
            print(f"\n   第二阶段 (中风险): 实施跨工序匹配和兜底配置")
            print(f"   - 影响: {risk_analysis['medium_risk']} 个批次")
            print(f"   - 风险: 可控，需要工艺验证")
            print(f"   - 建议: 先小批量验证，再全面推广")
            
            print(f"\n   第三阶段 (高风险): 实施模糊匹配策略")
            print(f"   - 影响: {risk_analysis['high_risk']} 个批次")
            print(f"   - 风险: 较高，需要严格验证")
            print(f"   - 建议: 工艺团队确认后谨慎实施")
            
            # 保存分析结果
            result_data = {
                'timestamp': datetime.now().isoformat(),
                'current_strategy': current_strategy_results,
                'optimized_strategy': optimized_results,
                'improvement_rate': improvement,
                'utilization_improvement': utilization_improvement,
                'risk_analysis': risk_analysis,
                'implementation_phases': [
                    {'phase': 1, 'risk': 'low', 'count': risk_analysis['low_risk']},
                    {'phase': 2, 'risk': 'medium', 'count': risk_analysis['medium_risk']}, 
                    {'phase': 3, 'risk': 'high', 'count': risk_analysis['high_risk']}
                ]
            }
            
            result_file = f'matching_strategy_optimization_result_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n📄 策略优化分析结果已保存到: {result_file}")
            print("="*80)
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 策略优化分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = design_optimized_matching_strategy()
    if success:
        print("\n🎉 匹配策略优化方案设计完成！")
    else:
        print("\n❌ 策略优化失败！")

if __name__ == "__main__":
    main()