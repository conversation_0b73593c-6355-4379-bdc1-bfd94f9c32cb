#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控告警系统和自动报表生成器
实时监控多个监控脚本的输出，设置告警阈值并生成综合报表
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
import time
import json
import pymysql
from datetime import datetime, timedelta
from collections import defaultdict
import threading
import glob

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('monitoring_alert_system.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('MonitoringAlertSystem')

class MonitoringAlertSystem:
    """监控告警系统"""
    
    def __init__(self):
        self.alert_thresholds = {
            # 连接池告警阈值
            'mysql_connections_critical': 50,
            'mysql_connections_warning': 35,
            'connection_growth_rate_critical': 5.0,  # 每分钟连接增长超过5个
            'connection_growth_rate_warning': 3.0,
            
            # 回收效率告警阈值
            'recycling_efficiency_critical': 60,  # 低于60分为严重
            'recycling_efficiency_warning': 75,   # 低于75分为警告
            'new_connections_per_hour_critical': 200,  # 每小时新建连接超过200个
            'new_connections_per_hour_warning': 100,
            
            # 系统资源告警阈值
            'python_processes_critical': 15,
            'python_processes_warning': 10,
            'memory_growth_rate_critical': 50,  # MB/小时
            'memory_growth_rate_warning': 30,
            
            # 响应时间告警阈值
            'avg_response_time_critical': 2000,  # 2秒
            'avg_response_time_warning': 1000,   # 1秒
            
            # 连接池利用率告警阈值
            'pool_utilization_critical': 0.9,   # 90%
            'pool_utilization_warning': 0.8     # 80%
        }
        
        self.alert_history = []
        self.monitoring_summary = {}
        self.report_interval = 600  # 10分钟生成一次报表
        self.last_report_time = datetime.now()
        
        # 监控数据存储
        self.connection_data = []
        self.recycling_data = []
        self.system_metrics = []
        
        # MySQL连接
        self._mysql_connection = None
    
    def _get_mysql_connection(self):
        """获取MySQL连接"""
        if self._mysql_connection is None:
            try:
                self._mysql_connection = pymysql.connect(
                    host='localhost',
                    port=3306,
                    user='root',
                    password='WWWwww123!',
                    database='aps',
                    charset='utf8mb4'
                )
            except Exception as e:
                logger.error(f"MySQL连接失败: {e}")
                return None
        return self._mysql_connection
    
    def collect_monitoring_data(self):
        """收集所有监控脚本的数据"""
        current_time = datetime.now()
        collected_data = {
            'timestamp': current_time.isoformat(),
            'connection_specialist_data': None,
            'continuous_monitoring_data': None,
            'realtime_monitor_data': None,
            'direct_mysql_stats': None
        }
        
        try:
            # 1. 从连接回收专家报告获取数据
            specialist_files = glob.glob('connection_recycle_specialist_report_*.json')
            if specialist_files:
                latest_specialist = max(specialist_files, key=os.path.getctime)
                try:
                    with open(latest_specialist, 'r', encoding='utf-8') as f:
                        collected_data['connection_specialist_data'] = json.load(f)
                        logger.debug(f"已收集专家监控数据: {latest_specialist}")
                except Exception as e:
                    logger.error(f"读取专家监控数据失败: {e}")
            
            # 2. 从连续监控报告获取数据
            if os.path.exists('continuous_monitoring_report.json'):
                try:
                    with open('continuous_monitoring_report.json', 'r', encoding='utf-8') as f:
                        collected_data['continuous_monitoring_data'] = json.load(f)
                        logger.debug("已收集连续监控数据")
                except Exception as e:
                    logger.error(f"读取连续监控数据失败: {e}")
            
            # 3. 从实时监控获取数据
            realtime_files = glob.glob('realtime_monitor_check_*.json')
            if realtime_files:
                latest_realtime = max(realtime_files, key=os.path.getctime)
                try:
                    with open(latest_realtime, 'r', encoding='utf-8') as f:
                        collected_data['realtime_monitor_data'] = json.load(f)
                        logger.debug(f"已收集实时监控数据: {latest_realtime}")
                except Exception as e:
                    logger.error(f"读取实时监控数据失败: {e}")
            
            # 4. 直接获取MySQL状态
            collected_data['direct_mysql_stats'] = self.get_direct_mysql_stats()
            
            return collected_data
            
        except Exception as e:
            logger.error(f"收集监控数据失败: {e}")
            return collected_data
    
    def get_direct_mysql_stats(self):
        """直接获取MySQL统计信息"""
        try:
            conn = self._get_mysql_connection()
            if not conn:
                return None
            
            stats = {}
            with conn.cursor() as cursor:
                # 获取基础连接统计
                cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                result = cursor.fetchone()
                stats['active_connections'] = int(result[1]) if result else 0
                
                cursor.execute("SHOW STATUS LIKE 'Threads_running'")
                result = cursor.fetchone()
                stats['running_threads'] = int(result[1]) if result else 0
                
                cursor.execute("SHOW STATUS LIKE 'Connections'")
                result = cursor.fetchone()
                stats['total_connections'] = int(result[1]) if result else 0
                
                cursor.execute("SHOW STATUS LIKE 'Aborted_connects'")
                result = cursor.fetchone()
                stats['aborted_connects'] = int(result[1]) if result else 0
                
                # 获取变量配置
                cursor.execute("SHOW VARIABLES LIKE 'max_connections'")
                result = cursor.fetchone()
                stats['max_connections'] = int(result[1]) if result else 0
                
                cursor.execute("SHOW VARIABLES LIKE 'wait_timeout'")
                result = cursor.fetchone()
                stats['wait_timeout'] = int(result[1]) if result else 0
                
                stats['timestamp'] = datetime.now().isoformat()
                stats['utilization_rate'] = stats['active_connections'] / stats['max_connections'] if stats['max_connections'] > 0 else 0
                
            return stats
            
        except Exception as e:
            logger.error(f"获取MySQL直接统计失败: {e}")
            return None
    
    def analyze_alerts(self, monitoring_data):
        """分析告警条件"""
        alerts = []
        current_time = datetime.now()
        
        try:
            # 分析连接池状态
            mysql_stats = monitoring_data.get('direct_mysql_stats')
            if mysql_stats:
                active_connections = mysql_stats['active_connections']
                utilization = mysql_stats['utilization_rate']
                
                if active_connections >= self.alert_thresholds['mysql_connections_critical']:
                    alerts.append({
                        'level': 'CRITICAL',
                        'type': 'connection_count',
                        'message': f'MySQL连接数严重过高: {active_connections}',
                        'value': active_connections,
                        'threshold': self.alert_thresholds['mysql_connections_critical'],
                        'timestamp': current_time.isoformat()
                    })
                elif active_connections >= self.alert_thresholds['mysql_connections_warning']:
                    alerts.append({
                        'level': 'WARNING',
                        'type': 'connection_count',
                        'message': f'MySQL连接数较高: {active_connections}',
                        'value': active_connections,
                        'threshold': self.alert_thresholds['mysql_connections_warning'],
                        'timestamp': current_time.isoformat()
                    })
                
                if utilization >= self.alert_thresholds['pool_utilization_critical']:
                    alerts.append({
                        'level': 'CRITICAL',
                        'type': 'pool_utilization',
                        'message': f'连接池利用率过高: {utilization:.1%}',
                        'value': utilization,
                        'threshold': self.alert_thresholds['pool_utilization_critical'],
                        'timestamp': current_time.isoformat()
                    })
                elif utilization >= self.alert_thresholds['pool_utilization_warning']:
                    alerts.append({
                        'level': 'WARNING',
                        'type': 'pool_utilization',
                        'message': f'连接池利用率较高: {utilization:.1%}',
                        'value': utilization,
                        'threshold': self.alert_thresholds['pool_utilization_warning'],
                        'timestamp': current_time.isoformat()
                    })
            
            # 分析回收效率
            specialist_data = monitoring_data.get('connection_specialist_data')
            if specialist_data and specialist_data.get('recycling_analysis'):
                recycling_analysis = specialist_data['recycling_analysis']
                efficiency_score = recycling_analysis.get('efficiency_score', 0)
                
                if efficiency_score <= self.alert_thresholds['recycling_efficiency_critical']:
                    alerts.append({
                        'level': 'CRITICAL',
                        'type': 'recycling_efficiency',
                        'message': f'连接回收效率严重不足: {efficiency_score:.1f}/100',
                        'value': efficiency_score,
                        'threshold': self.alert_thresholds['recycling_efficiency_critical'],
                        'timestamp': current_time.isoformat()
                    })
                elif efficiency_score <= self.alert_thresholds['recycling_efficiency_warning']:
                    alerts.append({
                        'level': 'WARNING',
                        'type': 'recycling_efficiency',
                        'message': f'连接回收效率需要改善: {efficiency_score:.1f}/100',
                        'value': efficiency_score,
                        'threshold': self.alert_thresholds['recycling_efficiency_warning'],
                        'timestamp': current_time.isoformat()
                    })
                
                new_connections = recycling_analysis.get('recycling_indicators', {}).get('new_connections_created', 0)
                connections_per_hour = new_connections * 120  # 30秒间隔 * 120 = 1小时
                
                if connections_per_hour >= self.alert_thresholds['new_connections_per_hour_critical']:
                    alerts.append({
                        'level': 'CRITICAL',
                        'type': 'connection_creation_rate',
                        'message': f'新建连接频率过高: {connections_per_hour}/小时',
                        'value': connections_per_hour,
                        'threshold': self.alert_thresholds['new_connections_per_hour_critical'],
                        'timestamp': current_time.isoformat()
                    })
                elif connections_per_hour >= self.alert_thresholds['new_connections_per_hour_warning']:
                    alerts.append({
                        'level': 'WARNING',
                        'type': 'connection_creation_rate',
                        'message': f'新建连接频率较高: {connections_per_hour}/小时',
                        'value': connections_per_hour,
                        'threshold': self.alert_thresholds['new_connections_per_hour_warning'],
                        'timestamp': current_time.isoformat()
                    })
            
            # 记录告警历史
            if alerts:
                self.alert_history.extend(alerts)
                # 保持告警历史在合理范围内
                if len(self.alert_history) > 1000:
                    self.alert_history = self.alert_history[-500:]
            
            return alerts
            
        except Exception as e:
            logger.error(f"分析告警条件失败: {e}")
            return []
    
    def generate_comprehensive_report(self, monitoring_data, alerts):
        """生成综合监控报告"""
        try:
            current_time = datetime.now()
            
            # 统计告警信息
            alert_summary = defaultdict(int)
            recent_alerts = [alert for alert in alerts if 
                           (current_time - datetime.fromisoformat(alert['timestamp'])).total_seconds() < 3600]
            
            for alert in recent_alerts:
                alert_summary[alert['level']] += 1
            
            # 连接池状态总结
            mysql_stats = monitoring_data.get('direct_mysql_stats', {})
            connection_summary = {
                'active_connections': mysql_stats.get('active_connections', 0),
                'max_connections': mysql_stats.get('max_connections', 0),
                'utilization_rate': mysql_stats.get('utilization_rate', 0),
                'total_connections_created': mysql_stats.get('total_connections', 0),
                'aborted_connections': mysql_stats.get('aborted_connects', 0)
            }
            
            # 回收效率总结
            specialist_data = monitoring_data.get('connection_specialist_data', {})
            recycling_summary = {
                'efficiency_score': 0,
                'new_connections_created': 0,
                'average_connection_time': 0,
                'connection_reuse_rate': 0
            }
            
            if specialist_data and specialist_data.get('recycling_analysis'):
                recycling_analysis = specialist_data['recycling_analysis']
                recycling_summary.update({
                    'efficiency_score': recycling_analysis.get('efficiency_score', 0),
                    'new_connections_created': recycling_analysis.get('recycling_indicators', {}).get('new_connections_created', 0),
                    'connections_per_interval': recycling_analysis.get('recycling_indicators', {}).get('connections_per_interval', 0)
                })
            
            # 生成报告
            report = {
                'report_timestamp': current_time.isoformat(),
                'monitoring_duration_hours': (current_time - datetime.fromisoformat('2025-08-24T12:18:00')).total_seconds() / 3600,
                'alert_summary': dict(alert_summary),
                'connection_pool_status': connection_summary,
                'recycling_efficiency': recycling_summary,
                'recent_alerts': recent_alerts,
                'system_health_score': self.calculate_health_score(connection_summary, recycling_summary, alert_summary),
                'recommendations': self.generate_recommendations(connection_summary, recycling_summary, recent_alerts)
            }
            
            # 保存报告
            filename = f'comprehensive_monitoring_report_{current_time.strftime("%Y%m%d_%H%M%S")}.json'
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📊 综合监控报告已生成: {filename}")
            return report
            
        except Exception as e:
            logger.error(f"生成综合报告失败: {e}")
            return None
    
    def calculate_health_score(self, connection_summary, recycling_summary, alert_summary):
        """计算系统健康分数"""
        score = 100
        
        # 连接池利用率扣分
        utilization = connection_summary.get('utilization_rate', 0)
        if utilization > 0.9:
            score -= 30
        elif utilization > 0.8:
            score -= 15
        elif utilization > 0.7:
            score -= 5
        
        # 回收效率扣分
        efficiency = recycling_summary.get('efficiency_score', 100)
        if efficiency < 60:
            score -= 25
        elif efficiency < 75:
            score -= 15
        elif efficiency < 85:
            score -= 5
        
        # 告警扣分
        score -= alert_summary.get('CRITICAL', 0) * 10
        score -= alert_summary.get('WARNING', 0) * 3
        
        return max(0, min(100, score))
    
    def generate_recommendations(self, connection_summary, recycling_summary, recent_alerts):
        """生成优化建议"""
        recommendations = []
        
        utilization = connection_summary.get('utilization_rate', 0)
        efficiency = recycling_summary.get('efficiency_score', 100)
        
        if utilization > 0.8:
            recommendations.append("连接池利用率较高，建议增加连接池大小或优化连接使用")
        
        if efficiency < 75:
            recommendations.append("连接回收效率较低，建议检查连接池配置和应用连接管理")
        
        critical_alerts = [alert for alert in recent_alerts if alert['level'] == 'CRITICAL']
        if critical_alerts:
            recommendations.append(f"发现{len(critical_alerts)}个严重告警，需要立即处理")
        
        if connection_summary.get('aborted_connections', 0) > 10:
            recommendations.append("连接中断次数较多，检查网络稳定性和超时配置")
        
        if not recommendations:
            recommendations.append("系统运行状况良好，继续保持当前配置")
        
        return recommendations
    
    def run_monitoring_alert_system(self, duration_hours=4):
        """运行监控告警系统"""
        logger.info(f"🚀 启动监控告警系统 - 持续{duration_hours}小时")
        logger.info(f"📊 告警阈值配置: {json.dumps(self.alert_thresholds, indent=2, ensure_ascii=False)}")
        
        end_time = datetime.now() + timedelta(hours=duration_hours)
        check_count = 0
        
        try:
            while datetime.now() < end_time:
                check_count += 1
                current_time = datetime.now()
                
                # 收集监控数据
                monitoring_data = self.collect_monitoring_data()
                
                # 分析告警
                alerts = self.analyze_alerts(monitoring_data)
                
                # 输出告警信息
                if alerts:
                    logger.warning(f"🚨 发现 {len(alerts)} 个告警:")
                    for alert in alerts:
                        level_emoji = "🔴" if alert['level'] == 'CRITICAL' else "🟡"
                        logger.warning(f"  {level_emoji} {alert['level']}: {alert['message']}")
                else:
                    logger.info("✅ 无告警 - 系统运行正常")
                
                # 定期生成报告
                if (current_time - self.last_report_time).total_seconds() >= self.report_interval:
                    report = self.generate_comprehensive_report(monitoring_data, alerts)
                    if report:
                        health_score = report['system_health_score']
                        logger.info(f"📊 系统健康分数: {health_score}/100")
                        logger.info(f"💡 当前建议数量: {len(report.get('recommendations', []))}")
                    
                    self.last_report_time = current_time
                
                # 输出系统状态摘要
                mysql_stats = monitoring_data.get('direct_mysql_stats', {})
                if mysql_stats:
                    logger.info(f"📈 连接状态: {mysql_stats['active_connections']}/{mysql_stats['max_connections']} "
                              f"({mysql_stats['utilization_rate']:.1%})")
                
                elapsed = current_time - datetime.fromisoformat('2025-08-24T12:18:00')
                remaining = end_time - current_time
                logger.info(f"⏱️ 监控进度: 已运行 {elapsed}, 剩余 {remaining}")
                
                time.sleep(120)  # 2分钟检查间隔
                
        except KeyboardInterrupt:
            logger.info("🛑 监控告警系统被用户中断")
        except Exception as e:
            logger.error(f"❌ 监控告警系统失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
        
        # 生成最终总结报告
        final_report = self.generate_final_summary_report(check_count)
        
        # 清理连接
        self.cleanup_connections()
        
        return final_report
    
    def generate_final_summary_report(self, total_checks):
        """生成最终总结报告"""
        try:
            current_time = datetime.now()
            start_time = datetime.fromisoformat('2025-08-24T12:18:00')
            total_duration = (current_time - start_time).total_seconds() / 3600
            
            # 统计告警历史
            alert_stats = defaultdict(int)
            for alert in self.alert_history:
                alert_stats[alert['level']] += 1
                alert_stats[f"{alert['level']}_{alert['type']}"] += 1
            
            final_summary = {
                'monitoring_period': {
                    'start_time': start_time.isoformat(),
                    'end_time': current_time.isoformat(),
                    'total_duration_hours': total_duration,
                    'total_checks': total_checks
                },
                'alert_statistics': dict(alert_stats),
                'monitoring_effectiveness': {
                    'avg_checks_per_hour': total_checks / max(1, total_duration),
                    'alert_rate': len(self.alert_history) / max(1, total_checks),
                    'critical_alert_rate': alert_stats.get('CRITICAL', 0) / max(1, total_checks)
                },
                'final_recommendations': self.generate_final_recommendations(alert_stats, total_duration),
                'configuration_assessment': self.assess_current_configuration()
            }
            
            filename = f'monitoring_final_summary_{current_time.strftime("%Y%m%d_%H%M%S")}.json'
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(final_summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📑 最终总结报告已生成: {filename}")
            logger.info(f"📊 监控统计: 总检查{total_checks}次，告警{len(self.alert_history)}次")
            logger.info(f"⭐ 告警率: {final_summary['monitoring_effectiveness']['alert_rate']:.2%}")
            
            return filename
            
        except Exception as e:
            logger.error(f"生成最终总结报告失败: {e}")
            return None
    
    def generate_final_recommendations(self, alert_stats, total_duration):
        """生成最终优化建议"""
        recommendations = []
        
        critical_count = alert_stats.get('CRITICAL', 0)
        warning_count = alert_stats.get('WARNING', 0)
        
        if critical_count == 0 and warning_count <= 5:
            recommendations.append("✅ 系统运行稳定，当前连接池配置良好")
            recommendations.append("💡 可以考虑在高峰期适当调整连接池大小以应对负载波动")
        elif critical_count > 0:
            recommendations.append(f"🔴 发现{critical_count}次严重告警，需要重点优化连接池配置")
            recommendations.append("📈 建议增加连接池base size和max_overflow参数")
            recommendations.append("⏱️ 考虑调整连接回收时间以提高复用效率")
        elif warning_count > 10:
            recommendations.append(f"🟡 发现{warning_count}次警告，建议适度优化")
            recommendations.append("🔧 微调连接池参数，提高资源利用效率")
        
        if alert_stats.get('CRITICAL_connection_count', 0) > 0:
            recommendations.append("🏊 连接数经常过高，强烈建议增加连接池容量")
        
        if alert_stats.get('WARNING_recycling_efficiency', 0) > 0:
            recommendations.append("♻️ 连接回收效率有待提升，检查应用层连接管理")
        
        return recommendations
    
    def assess_current_configuration(self):
        """评估当前配置"""
        try:
            import configparser
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            
            current_config = {
                'critical_pool': {
                    'size': config.getint('DATABASE', 'critical_pool_size', fallback=5),
                    'max_overflow': config.getint('DATABASE', 'critical_max_overflow', fallback=5),
                    'recycle_time': config.getint('DATABASE', 'critical_pool_recycle', fallback=3600)
                },
                'interactive_pool': {
                    'size': config.getint('DATABASE', 'interactive_pool_size', fallback=10),
                    'max_overflow': config.getint('DATABASE', 'interactive_max_overflow', fallback=15),
                    'recycle_time': config.getint('DATABASE', 'interactive_pool_recycle', fallback=600)
                }
            }
            
            assessment = {
                'current_configuration': current_config,
                'configuration_rating': 'good',  # 基于监控结果评估
                'suggested_optimizations': []
            }
            
            # 根据告警历史评估配置
            critical_alerts = len([a for a in self.alert_history if a['level'] == 'CRITICAL'])
            if critical_alerts > 5:
                assessment['configuration_rating'] = 'needs_improvement'
                assessment['suggested_optimizations'].append("增加连接池大小")
                assessment['suggested_optimizations'].append("调整回收时间")
            elif critical_alerts == 0:
                assessment['configuration_rating'] = 'excellent'
                assessment['suggested_optimizations'].append("当前配置优秀，保持现状")
            
            return assessment
            
        except Exception as e:
            logger.error(f"配置评估失败: {e}")
            return {}
    
    def cleanup_connections(self):
        """清理数据库连接"""
        try:
            if self._mysql_connection:
                self._mysql_connection.close()
                self._mysql_connection = None
                logger.info("✅ MySQL连接已清理")
        except Exception as e:
            logger.error(f"连接清理失败: {e}")

def main():
    """告警系统入口"""
    alert_system = MonitoringAlertSystem()
    alert_system.run_monitoring_alert_system(4)  # 4小时监控

if __name__ == "__main__":
    main()