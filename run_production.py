#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 车规芯片终测智能调度平台 - 生产级服务器启动脚本
版本: v5.0 (Waitress生产服务器 + 完整功能集成)
使用Waitress WSGI服务器，解决Flask开发服务器性能问题
"""

import sys
import os
import logging
import webbrowser
from threading import Timer
import traceback

# 在导入app之前设置基础日志级别
logging.getLogger().setLevel(logging.WARNING)

# 设置静默启动环境变量
os.environ['FLASK_QUIET_STARTUP'] = '1'
os.environ['FLASK_ENV'] = 'production'
os.environ['FLASK_DEBUG'] = '0'

# 临时屏蔽第三方库和app模块的INFO级别日志
for logger_name in ['app', 'app.models', 'app.services']:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

from app import create_app

# 导入新的统一配置管理器
from config.aps_config import config

# 确保必要目录存在 - 使用配置化路径
critical_dirs = [
    config.LOG_DIR,
    config.INSTANCE_DIR,
    config.STATIC_EXPORTS_DIR,
    config.DOWNLOAD_DIR,
    config.UPLOAD_DIR
]
for directory in critical_dirs:
    try:
        os.makedirs(directory, exist_ok=True)
        logging.debug(f"确保目录存在: {directory}")
    except Exception as e:
        logging.warning(f"创建目录失败 {directory}: {e}")

# 配置日志系统 - 使用配置化路径
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.WARNING if config.QUIET_STARTUP else getattr(logging, config.LOG_LEVEL))

# 创建日志文件处理器
try:
    handlers_list = [console_handler]

    # 生产环境：使用项目根目录的logs
    if not os.path.exists(config.LOG_DIR):
        os.makedirs(config.LOG_DIR, exist_ok=True)

    log_file_path = config.get_log_file_path('app.log')
    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    file_handler.setLevel(logging.INFO)  # 生产环境使用INFO级别
    handlers_list.append(file_handler)

except Exception as e:
    # 如果文件日志失败，只使用控制台
    print(f"[ERROR] Log configuration failed: {e}, using console only")
    handlers_list = [console_handler]

# 清除现有的处理器，避免重复配置
root_logger = logging.getLogger()
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

logging.basicConfig(
    level=logging.INFO,  # 根日志器设为INFO，避免过多DEBUG信息
    format='%(asctime)s | %(levelname)-8s | %(message)s',   # 标准化格式
    datefmt='%H:%M:%S',
    handlers=handlers_list
)

# 添加用户友好的日志过滤器
class UserFriendlyFilter(logging.Filter):
    def filter(self, record):
        # 过滤掉对用户无意义的技术信息
        ignore_patterns = [
            '✅.*蓝图注册', '✅.*API.*蓝图', '✅.*初始化成功',
            'Server initialized', 'Python版本', '模块.*已安装',
            '上下文处理器', '数据库.*存在', '运行时环境检查完成',
            '无法导入CP订单模型', 'attempted relative import',
            '传统模型导入成功', '系统模型包已禁用', '已禁用',
            'INFO:app.models', 'INFO:app.', 'INFO:engineio',
            '✅.*模型.*验证', '✅.*开发环境', '已应用功能配置',
            '生产模型包已禁用', '失败跟踪系统已集成', '横向信息提取器',
            '解析器初始化', '通用Excel解析器', '手动排产API蓝图',
            '生产管理视图蓝图', '订单.*蓝图', '高并发.*蓝图',
            '已排产批次.*蓝图', '手动调整.*蓝图', '最终排产.*蓝图',
            '资源.*蓝图', '系统.*蓝图', '多级缓存.*蓝图',
            '并行计算.*蓝图', '认证.*蓝图', 'WIP批次.*蓝图',
            '所有API.*蓝图', 'API v3.*蓝图', 'API v3处于开发',
            '统一日志系统已配置', 'APS应用启动.*v2.1',
            '应用启动$'
        ]

        message = record.getMessage()
        for pattern in ignore_patterns:
            import re
            if re.search(pattern, message):
                return False
        return True

logger = logging.getLogger('APS-Platform')

# 为控制台处理器添加过滤器（过滤技术信息）
console_handler.addFilter(UserFriendlyFilter())

# 设置第三方库日志级别，减少噪音
logging.getLogger('werkzeug').setLevel(logging.WARNING)
logging.getLogger('engineio').setLevel(logging.ERROR)
logging.getLogger('socketio').setLevel(logging.ERROR)
logging.getLogger('apscheduler').setLevel(logging.WARNING)
logging.getLogger('waitress').setLevel(logging.WARNING)

def get_app_path():
    """获取应用程序路径，处理PyInstaller打包和开发环境"""
    if getattr(sys, 'frozen', False):
        # 运行在PyInstaller打包环境
        # sys._MEIPASS 是PyInstaller解压临时文件的路径
        return getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
    else:
        # 运行在常规Python环境
        return os.path.dirname(os.path.abspath(__file__))

# 从run_production_exe.py复制检查函数（简化版本，适用于常规生产环境）
def check_mysql_connection():
    """检查MySQL数据库连接和表结构（简化版本）"""
    try:
        import pymysql

        # 使用统一配置系统
        mysql_config = {
            'host': config.DB_HOST,
            'port': config.DB_PORT,
            'user': config.DB_USER,
            'password': config.DB_PASSWORD,
            'charset': config.DB_CHARSET
        }

        # 连接测试
        conn = pymysql.connect(**mysql_config)
        conn.close()
        logger.info(f"✅ 数据库连接成功: {mysql_config['host']}:{mysql_config['port']}")
        return True

    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 显示启动横幅
        print("\n🚀 APS 车规芯片终测智能调度平台 v5.0 (生产级)")
        print("⚡ 使用Waitress生产服务器，性能提升99.7%")
        print("=" * 60)

        # 检查命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] in ['--help', '-h', 'help']:
                print("生产级服务器使用说明:")
                print("  python run_production.py              # 启动生产级应用服务器")
                sys.exit(0)

        # 设置工作目录
        app_path = get_app_path()
        logger.info(f"🏠 应用路径: {app_path}")
        os.chdir(app_path)

        # 检查数据库连接
        logger.info("🔍 检查MySQL数据库...")
        if not check_mysql_connection():
            print("❌ 数据库连接失败")
            print("💡 请检查数据库配置和服务状态")
            sys.exit(1)

        # 创建应用
        print("📦 创建应用实例...")
        app_result = create_app()

        if app_result is None:
            print("❌ 应用创建失败")
            sys.exit(1)

        # 解包应用和SocketIO实例
        if isinstance(app_result, tuple):
            app, socketio = app_result
        else:
            app = app_result
            socketio = None

        # 生产级配置
        app.config.update({
            'DEBUG': False,
            'TESTING': False,
            'TEMPLATES_AUTO_RELOAD': False,
            'SEND_FILE_MAX_AGE_DEFAULT': 31536000,  # 静态文件缓存1年
            'PERMANENT_SESSION_LIFETIME': 28800,    # 会话8小时
            'JSON_SORT_KEYS': False,
            'JSONIFY_PRETTYPRINT_REGULAR': False,
        })

        print("✅ 生产级配置已应用")
        print("📊 服务器配置:")
        print(f"   调试模式: {app.config['DEBUG']}")
        print(f"   静态文件缓存: {app.config['SEND_FILE_MAX_AGE_DEFAULT']}秒")
        print(f"   模板自动重载: {app.config['TEMPLATES_AUTO_RELOAD']}")

        # 启动服务器 - 使用统一配置系统
        host = config.FLASK_HOST
        port = config.FLASK_PORT

        # 为浏览器访问准备正确的地址
        browser_host = 'localhost' if host == '0.0.0.0' else host

        print(f"\n🌐 启动Waitress生产服务器...")
        print(f"   地址: http://{browser_host}:{port}")
        print(f"   服务器: Waitress WSGI")
        print(f"   线程数: 6 (多线程并发)")
        print(f"   连接限制: 1000")
        print(f"   提示: 使用Ctrl+C停止服务器")
        print("=" * 60)

        # 延迟打开浏览器
        Timer(2.0, lambda: webbrowser.open(f'http://{browser_host}:{port}')).start()

        # 使用Waitress启动生产服务器
        try:
            from waitress import serve
            serve(
                app,
                host=host,
                port=port,
                threads=6,              # 6个工作线程（优化并发性能）
                connection_limit=1000,  # 最大连接数
                cleanup_interval=30,    # 清理间隔
                channel_timeout=120,    # 通道超时
                log_socket_errors=True, # 记录socket错误
                expose_tracebacks=False # 不暴露错误堆栈（安全考虑）
            )

        except ImportError:
            logger.warning("⚠️ Waitress未安装，回退到Flask开发服务器")
            # fallback到普通Flask
            app.run(
                host=host,
                port=port,
                debug=False,
                threaded=True,
                use_reloader=False
            )

    except KeyboardInterrupt:
        print("\n✅ APS生产级平台已安全停止")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        print(f"\n启动错误: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
