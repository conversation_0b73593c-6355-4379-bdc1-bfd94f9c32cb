#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析真实的排产数据
找出为什么200+批次只排到32台机器的真正原因
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('AnalyzeRealSchedulingData')

def analyze_wait_lot_data():
    """分析真实的待排产批次数据"""
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.data_source_manager import DataSourceManager
            data_manager = DataSourceManager()
            
            print("📊 分析真实的待排产批次数据")
            print("="*80)
            
            # 获取待排产批次
            wait_lots, source = data_manager.get_wait_lot_data()
            print(f"从 {source} 获取到 {len(wait_lots)} 个待排产批次")
            
            if not wait_lots:
                print("❌ 没有待排产批次数据")
                return False
            
            # 分析批次的关键字段
            print("\n📋 批次字段分布分析:")
            
            # 统计各个字段的填充情况
            field_stats = {}
            fields_to_analyze = ['DEVICE', 'STAGE', 'PKG_PN', 'LOT_TYPE']
            
            for field in fields_to_analyze:
                filled = 0
                empty = 0
                unique_values = set()
                
                for lot in wait_lots:
                    value = lot.get(field, '')
                    if value and value.strip():
                        filled += 1
                        unique_values.add(value.strip())
                    else:
                        empty += 1
                
                field_stats[field] = {
                    'filled': filled,
                    'empty': empty,
                    'unique_count': len(unique_values),
                    'sample_values': list(unique_values)[:5]
                }
            
            for field, stats in field_stats.items():
                print(f"\n{field}:")
                print(f"  有值: {stats['filled']}/{len(wait_lots)} ({100*stats['filled']/len(wait_lots):.1f}%)")
                print(f"  空值: {stats['empty']}/{len(wait_lots)} ({100*stats['empty']/len(wait_lots):.1f}%)")
                print(f"  唯一值数量: {stats['unique_count']}")
                print(f"  样本值: {stats['sample_values']}")
            
            # 分析具体的批次样本
            print(f"\n📦 前10个批次样本:")
            print("-" * 80)
            for i, lot in enumerate(wait_lots[:10]):
                print(f"{i+1:2d}. LOT_ID: {lot.get('LOT_ID', '')[:20]:20s} "
                      f"DEVICE: {lot.get('DEVICE', '')[:15]:15s} "
                      f"STAGE: {lot.get('STAGE', '')[:10]:10s} "
                      f"PKG_PN: {lot.get('PKG_PN', '')[:15]:15s}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析待排产批次失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def analyze_equipment_data():
    """分析真实的设备数据"""
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.data_source_manager import DataSourceManager
            data_manager = DataSourceManager()
            
            print("\n🔧 分析真实的设备数据")
            print("="*80)
            
            # 获取设备数据
            equipment_result = data_manager.get_table_data('EQP_STATUS')
            if not equipment_result.get('success'):
                print("❌ 无法获取设备数据")
                return False
            
            equipment_list = equipment_result.get('data', [])
            print(f"获取到 {len(equipment_list)} 台设备")
            
            if not equipment_list:
                print("❌ 没有设备数据")
                return False
            
            # 分析设备状态分布
            print("\n📊 设备状态分布:")
            status_count = {}
            for eqp in equipment_list:
                status = eqp.get('STATUS', '').strip()
                status_count[status] = status_count.get(status, 0) + 1
            
            for status, count in sorted(status_count.items()):
                print(f"  {status or '(空)':15s}: {count:3d} 台 ({100*count/len(equipment_list):5.1f}%)")
            
            # 分析设备配置字段
            print("\n🔧 设备配置字段分布:")
            config_fields = ['DEVICE', 'STAGE', 'KIT_PN', 'HB_PN', 'TB_PN', 'HANDLER_CONFIG', 'EQP_CLASS', 'TEMPERATURE_RANGE']
            
            for field in config_fields:
                filled = 0
                empty = 0
                unique_values = set()
                
                for eqp in equipment_list:
                    value = eqp.get(field, '')
                    if value and str(value).strip():
                        filled += 1
                        unique_values.add(str(value).strip())
                    else:
                        empty += 1
                
                print(f"  {field:20s}: 有值 {filled:3d} ({100*filled/len(equipment_list):5.1f}%) "
                      f"空值 {empty:3d} ({100*empty/len(equipment_list):5.1f}%) "
                      f"唯一值 {len(unique_values):2d}")
            
            # 找出没有配置信息的设备
            no_config_equipment = []
            for eqp in equipment_list:
                has_config = any([
                    eqp.get('KIT_PN', ''),
                    eqp.get('HB_PN', ''),
                    eqp.get('TB_PN', ''),
                    eqp.get('HANDLER_CONFIG', ''),
                    eqp.get('DEVICE', ''),
                    eqp.get('STAGE', '')
                ])
                if not has_config:
                    no_config_equipment.append(eqp)
            
            print(f"\n⚠️ 完全没有配置信息的设备: {len(no_config_equipment)} 台")
            if no_config_equipment:
                print("   前10台:")
                for i, eqp in enumerate(no_config_equipment[:10]):
                    print(f"   {i+1:2d}. {eqp.get('HANDLER_ID', 'Unknown'):20s} "
                          f"EQP_CLASS: {eqp.get('EQP_CLASS', ''):10s} "
                          f"TEMP_RANGE: {eqp.get('TEMPERATURE_RANGE', ''):10s}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析设备数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def analyze_test_spec_data():
    """分析测试规格数据"""
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.data_source_manager import DataSourceManager
            data_manager = DataSourceManager()
            
            print("\n🧪 分析测试规格数据")
            print("="*80)
            
            # 获取测试规格数据
            spec_result = data_manager.get_table_data('ET_FT_TEST_SPEC')
            if not spec_result.get('success'):
                print("❌ 无法获取测试规格数据")
                return False
            
            spec_list = spec_result.get('data', [])
            print(f"获取到 {len(spec_list)} 条测试规格")
            
            if not spec_list:
                print("❌ 没有测试规格数据")
                return False
            
            # 分析DEVICE-STAGE组合
            device_stage_combinations = set()
            for spec in spec_list:
                device = spec.get('DEVICE', '').strip()
                stage = spec.get('STAGE', '').strip()
                if device and stage:
                    device_stage_combinations.add((device, stage))
            
            print(f"\n📋 DEVICE-STAGE组合数量: {len(device_stage_combinations)}")
            print("   前20个组合:")
            for i, (device, stage) in enumerate(sorted(device_stage_combinations)[:20]):
                print(f"   {i+1:2d}. {device:20s} - {stage}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析测试规格失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def simulate_real_matching():
    """模拟真实批次和设备的匹配过程"""
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.real_scheduling_service import RealSchedulingService
            from app.services.data_source_manager import DataSourceManager
            
            scheduler = RealSchedulingService()
            data_manager = DataSourceManager()
            
            print("\n🎯 模拟真实批次和设备匹配")
            print("="*80)
            
            # 获取真实数据
            wait_lots, _ = data_manager.get_wait_lot_data()
            equipment_result = data_manager.get_table_data('EQP_STATUS')
            equipment_list = equipment_result.get('data', []) if equipment_result.get('success') else []
            
            if not wait_lots or not equipment_list:
                print("❌ 缺少批次或设备数据")
                return False
            
            print(f"分析 {len(wait_lots)} 个批次 × {len(equipment_list)} 台设备")
            
            # 取前10个批次进行详细分析
            sample_lots = wait_lots[:10]
            sample_equipment = equipment_list[:20]  # 取前20台设备
            
            print(f"\n🔍 详细分析前 {len(sample_lots)} 个批次:")
            
            total_matches = 0
            for i, lot in enumerate(sample_lots):
                lot_id = lot.get('LOT_ID', f'LOT_{i}')
                print(f"\n📦 批次 {i+1}: {lot_id}")
                
                # 获取批次配置需求
                preloaded_data = {}  # 简化版，实际应该预加载
                lot_requirements = scheduler.get_lot_configuration_requirements_optimized(lot, preloaded_data)
                
                if not lot_requirements:
                    print("   ❌ 无法获取配置需求")
                    continue
                
                print(f"   DEVICE: {lot_requirements.get('DEVICE', '')}")
                print(f"   STAGE: {lot_requirements.get('STAGE', '')}")
                print(f"   配置: KIT_PN={lot_requirements.get('KIT_PN', '')[:10]}, "
                      f"HB_PN={lot_requirements.get('HB_PN', '')[:10]}, "
                      f"TB_PN={lot_requirements.get('TB_PN', '')[:10]}")
                
                # 测试与设备的匹配
                lot_matches = 0
                match_details = []
                
                for eqp in sample_equipment:
                    score, match_type, changeover_time = scheduler.calculate_equipment_match_score_optimized(
                        lot_requirements, eqp, preloaded_data
                    )
                    
                    if score > 0:
                        lot_matches += 1
                        total_matches += 1
                        match_details.append({
                            'handler_id': eqp.get('HANDLER_ID', ''),
                            'score': score,
                            'match_type': match_type,
                            'changeover_time': changeover_time
                        })
                
                print(f"   ✅ 匹配设备: {lot_matches}/{len(sample_equipment)}")
                
                # 显示前5个最佳匹配
                match_details.sort(key=lambda x: x['score'], reverse=True)
                for j, match in enumerate(match_details[:5]):
                    print(f"     {j+1}. {match['handler_id']:20s} {match['score']:3d}分 {match['match_type']}")
            
            match_rate = (total_matches / (len(sample_lots) * len(sample_equipment))) * 100
            print(f"\n📊 匹配统计:")
            print(f"   总组合: {len(sample_lots)} × {len(sample_equipment)} = {len(sample_lots) * len(sample_equipment)}")
            print(f"   成功匹配: {total_matches} ({match_rate:.1f}%)")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 模拟匹配失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主分析函数"""
    print("🔍 真实排产数据深度分析")
    print("目标：找出200+批次只排32台机器的真正原因")
    print("="*80)
    
    # 分析1：待排产批次数据
    success1 = analyze_wait_lot_data()
    
    # 分析2：设备数据
    success2 = analyze_equipment_data()
    
    # 分析3：测试规格数据  
    success3 = analyze_test_spec_data()
    
    # 分析4：模拟真实匹配
    success4 = simulate_real_matching()
    
    print("\n" + "="*80)
    print("🎯 分析总结:")
    
    if success1 and success2 and success3 and success4:
        print("✅ 真实数据分析完成")
        print("   请检查上面的详细分析结果，重点关注：")
        print("   1. 批次字段填充情况（特别是DEVICE和STAGE）")
        print("   2. 设备配置字段分布（KIT_PN/HB_PN/TB_PN/HANDLER_CONFIG）")
        print("   3. 完全没有配置的设备数量")
        print("   4. 实际匹配成功率")
        print("   这些数据将揭示真正的瓶颈所在")
    else:
        print("❌ 部分分析失败，请检查数据库连接和表结构")

if __name__ == "__main__":
    main()