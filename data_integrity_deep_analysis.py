#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据完整性深度分析 - 找出为什么配置匹配全部失败
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict
import json

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('data_integrity_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('DataIntegrityAnalyzer')

def analyze_data_integrity():
    """深度分析数据完整性问题"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            
            data_manager = DataSourceManager()
            
            print("="*80)
            print("🔍 数据完整性深度分析")
            print("="*80)
            
            # ==================== 获取基础数据 ====================
            print(f"\n📊 获取基础数据...")
            
            # 获取待排产批次
            wait_lots_result = data_manager.get_table_data('et_wait_lot')
            wait_lots = wait_lots_result.get('data', [])
            
            # 获取测试规格数据
            test_spec_result = data_manager.get_table_data('et_ft_test_spec')
            test_specs = test_spec_result.get('data', [])
            
            # 获取配方文件数据
            recipe_result = data_manager.get_table_data('recipe_file')
            recipe_files = recipe_result.get('data', [])
            
            print(f"   待排产批次: {len(wait_lots)} 个")
            print(f"   测试规格: {len(test_specs)} 条")
            print(f"   配方文件: {len(recipe_files)} 条")
            
            # ==================== 分析待排产批次的数据质量 ====================
            print(f"\n🔍 第一步：分析待排产批次的数据质量")
            
            device_stage_combinations = set()
            batch_data_quality = {
                'complete_batches': 0,
                'missing_device': 0,
                'missing_stage': 0,
                'missing_both': 0,
                'device_types': defaultdict(int),
                'stage_types': defaultdict(int),
                'device_stage_pairs': defaultdict(int)
            }
            
            for lot in wait_lots:
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                lot_id = lot.get('LOT_ID', '')
                
                if device and stage:
                    batch_data_quality['complete_batches'] += 1
                    device_stage_combinations.add((device, stage))
                    batch_data_quality['device_types'][device] += 1
                    batch_data_quality['stage_types'][stage] += 1
                    batch_data_quality['device_stage_pairs'][f"{device}-{stage}"] += 1
                elif device and not stage:
                    batch_data_quality['missing_stage'] += 1
                elif not device and stage:
                    batch_data_quality['missing_device'] += 1
                else:
                    batch_data_quality['missing_both'] += 1
            
            print(f"   数据完整性统计:")
            print(f"   - 完整批次 (有DEVICE+STAGE): {batch_data_quality['complete_batches']}")
            print(f"   - 缺少STAGE: {batch_data_quality['missing_stage']}")
            print(f"   - 缺少DEVICE: {batch_data_quality['missing_device']}")
            print(f"   - 两者都缺少: {batch_data_quality['missing_both']}")
            print(f"   - 不同DEVICE类型: {len(batch_data_quality['device_types'])}")
            print(f"   - 不同STAGE类型: {len(batch_data_quality['stage_types'])}")
            print(f"   - 不同DEVICE-STAGE组合: {len(device_stage_combinations)}")
            
            print(f"\n   Top 10 DEVICE类型:")
            for i, (device, count) in enumerate(sorted(batch_data_quality['device_types'].items(), key=lambda x: x[1], reverse=True)[:10], 1):
                print(f"     {i:2d}. {device:<30} : {count} 个批次")
            
            print(f"\n   Top 10 STAGE类型:")
            for i, (stage, count) in enumerate(sorted(batch_data_quality['stage_types'].items(), key=lambda x: x[1], reverse=True)[:10], 1):
                print(f"     {i:2d}. {stage:<20} : {count} 个批次")
            
            print(f"\n   Top 10 DEVICE-STAGE组合:")
            for i, (pair, count) in enumerate(sorted(batch_data_quality['device_stage_pairs'].items(), key=lambda x: x[1], reverse=True)[:10], 1):
                print(f"     {i:2d}. {pair:<40} : {count} 个批次")
            
            # ==================== 分析ET_FT_TEST_SPEC表的数据覆盖率 ====================
            print(f"\n🔍 第二步：分析ET_FT_TEST_SPEC表的数据覆盖率")
            
            test_spec_combinations = set()
            test_spec_quality = {
                'total_specs': len(test_specs),
                'released_specs': 0,
                'device_types': defaultdict(int),
                'stage_types': defaultdict(int),
                'approval_states': defaultdict(int),
                'device_stage_pairs': defaultdict(int)
            }
            
            for spec in test_specs:
                device = (spec.get('DEVICE') or '').strip()
                stage = (spec.get('STAGE') or '').strip()
                approval = (spec.get('APPROVAL_STATE') or '').strip()
                
                if device and stage:
                    test_spec_combinations.add((device, stage))
                    test_spec_quality['device_types'][device] += 1
                    test_spec_quality['stage_types'][stage] += 1
                    test_spec_quality['device_stage_pairs'][f"{device}-{stage}"] += 1
                
                test_spec_quality['approval_states'][approval] += 1
                if approval == 'Released':
                    test_spec_quality['released_specs'] += 1
            
            print(f"   ET_FT_TEST_SPEC数据统计:")
            print(f"   - 总规格数: {test_spec_quality['total_specs']}")
            print(f"   - Released状态: {test_spec_quality['released_specs']}")
            print(f"   - 不同DEVICE类型: {len(test_spec_quality['device_types'])}")
            print(f"   - 不同STAGE类型: {len(test_spec_quality['stage_types'])}")
            print(f"   - 不同DEVICE-STAGE组合: {len(test_spec_combinations)}")
            
            print(f"\n   审批状态分布:")
            for state, count in sorted(test_spec_quality['approval_states'].items(), key=lambda x: x[1], reverse=True):
                print(f"     - {state or '空':<15}: {count} 条")
            
            # ==================== 关键：匹配覆盖率分析 ====================
            print(f"\n🔍 第三步：匹配覆盖率分析")
            
            # 待排产批次需要的DEVICE-STAGE组合
            wait_combinations = device_stage_combinations
            # ET_FT_TEST_SPEC中Released状态的组合
            released_spec_combinations = set()
            for spec in test_specs:
                if (spec.get('APPROVAL_STATE') or '').strip() == 'Released':
                    device = (spec.get('DEVICE') or '').strip()
                    stage = (spec.get('STAGE') or '').strip()
                    if device and stage:
                        released_spec_combinations.add((device, stage))
            
            # 计算匹配覆盖率
            covered_combinations = wait_combinations & released_spec_combinations
            uncovered_combinations = wait_combinations - released_spec_combinations
            
            print(f"   匹配覆盖率分析:")
            print(f"   - 待排产需要的组合: {len(wait_combinations)} 种")
            print(f"   - ET_FT_TEST_SPEC覆盖: {len(covered_combinations)} 种")
            print(f"   - 覆盖率: {len(covered_combinations)/len(wait_combinations)*100:.1f}%")
            print(f"   - 未覆盖的组合: {len(uncovered_combinations)} 种")
            
            if uncovered_combinations:
                print(f"\n   🚨 未覆盖的DEVICE-STAGE组合 (前15个):")
                for i, (device, stage) in enumerate(list(uncovered_combinations)[:15], 1):
                    # 统计有多少批次受影响
                    affected_count = sum(1 for lot in wait_lots 
                                       if (lot.get('DEVICE') or '').strip() == device 
                                       and (lot.get('STAGE') or '').strip() == stage)
                    print(f"     {i:2d}. {device}-{stage} (影响 {affected_count} 个批次)")
            
            # ==================== 分析recipe_file表的覆盖率 ====================
            print(f"\n🔍 第四步：分析recipe_file表的覆盖率")
            
            recipe_combinations = set()
            recipe_quality = {
                'total_recipes': len(recipe_files),
                'released_recipes': 0,
                'device_types': defaultdict(int),
                'stage_types': defaultdict(int),
                'approval_states': defaultdict(int),
                'device_stage_pairs': defaultdict(int)
            }
            
            for recipe in recipe_files:
                device = (recipe.get('DEVICE') or '').strip()
                stage = (recipe.get('STAGE') or '').strip()
                approval = (recipe.get('APPROVAL_STATE') or '').strip()
                
                if device and stage:
                    recipe_combinations.add((device, stage))
                    recipe_quality['device_types'][device] += 1
                    recipe_quality['stage_types'][stage] += 1
                    recipe_quality['device_stage_pairs'][f"{device}-{stage}"] += 1
                
                recipe_quality['approval_states'][approval] += 1
                if approval == 'Released':
                    recipe_quality['released_recipes'] += 1
            
            # recipe_file的Released状态组合
            released_recipe_combinations = set()
            for recipe in recipe_files:
                if (recipe.get('APPROVAL_STATE') or '').strip() == 'Released':
                    device = (recipe.get('DEVICE') or '').strip()
                    stage = (recipe.get('STAGE') or '').strip()
                    if device and stage:
                        released_recipe_combinations.add((device, stage))
            
            # recipe_file覆盖率
            recipe_covered = wait_combinations & released_recipe_combinations
            recipe_uncovered = wait_combinations - released_recipe_combinations
            
            print(f"   recipe_file数据统计:")
            print(f"   - 总配方数: {recipe_quality['total_recipes']}")
            print(f"   - Released状态: {recipe_quality['released_recipes']}")
            print(f"   - 不同DEVICE-STAGE组合: {len(recipe_combinations)}")
            print(f"   - 对待排产的覆盖率: {len(recipe_covered)/len(wait_combinations)*100:.1f}%")
            print(f"   - 未覆盖的组合: {len(recipe_uncovered)} 种")
            
            print(f"\n   审批状态分布:")
            for state, count in sorted(recipe_quality['approval_states'].items(), key=lambda x: x[1], reverse=True):
                print(f"     - {state or '空':<15}: {count} 条")
            
            # ==================== 综合分析：两个表的联合覆盖率 ====================
            print(f"\n🔍 第五步：两个表的联合覆盖率分析")
            
            # 任一表覆盖的组合
            either_covered = released_spec_combinations | released_recipe_combinations
            either_coverage = wait_combinations & either_covered
            still_uncovered = wait_combinations - either_covered
            
            print(f"   联合覆盖率分析:")
            print(f"   - ET_FT_TEST_SPEC独有覆盖: {len(released_spec_combinations - released_recipe_combinations)} 种")
            print(f"   - recipe_file独有覆盖: {len(released_recipe_combinations - released_spec_combinations)} 种")
            print(f"   - 两表重叠覆盖: {len(released_spec_combinations & released_recipe_combinations)} 种")
            print(f"   - 联合总覆盖率: {len(either_coverage)/len(wait_combinations)*100:.1f}%")
            print(f"   - 仍未覆盖: {len(still_uncovered)} 种")
            
            if still_uncovered:
                print(f"\n   🚨 两个表都无法覆盖的DEVICE-STAGE组合:")
                for i, (device, stage) in enumerate(list(still_uncovered)[:10], 1):
                    affected_count = sum(1 for lot in wait_lots 
                                       if (lot.get('DEVICE') or '').strip() == device 
                                       and (lot.get('STAGE') or '').strip() == stage)
                    print(f"     {i:2d}. {device}-{stage} (影响 {affected_count} 个批次)")
            
            # ==================== 分析具体批次为什么失败 ====================
            print(f"\n🔍 第六步：分析具体批次匹配失败原因")
            
            # 选择几个有代表性的批次进行详细分析
            sample_lots = []
            seen_combinations = set()
            for lot in wait_lots:
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                if device and stage and (device, stage) not in seen_combinations:
                    sample_lots.append(lot)
                    seen_combinations.add((device, stage))
                    if len(sample_lots) >= 5:
                        break
            
            print(f"   分析 {len(sample_lots)} 个代表性批次的匹配情况:")
            
            for i, lot in enumerate(sample_lots, 1):
                lot_id = lot.get('LOT_ID', '')
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                
                print(f"\n   批次 {i}: {lot_id} ({device}-{stage})")
                
                # 在ET_FT_TEST_SPEC中查找匹配
                spec_matches = []
                for spec in test_specs:
                    spec_device = (spec.get('DEVICE') or '').strip()
                    spec_stage = (spec.get('STAGE') or '').strip()
                    spec_approval = (spec.get('APPROVAL_STATE') or '').strip()
                    
                    if spec_device == device and spec_stage == stage:
                        spec_matches.append({
                            'approval': spec_approval,
                            'handler': (spec.get('HANDLER') or '').strip(),
                            'tb_pn': (spec.get('TB_PN') or '').strip(),
                            'hb_pn': (spec.get('HB_PN') or '').strip()
                        })
                
                print(f"     ET_FT_TEST_SPEC匹配: {len(spec_matches)} 条")
                if spec_matches:
                    released_matches = [m for m in spec_matches if m['approval'] == 'Released']
                    print(f"       其中Released状态: {len(released_matches)} 条")
                    for j, match in enumerate(spec_matches[:3], 1):
                        print(f"         {j}. APPROVAL={match['approval']}, HANDLER={match['handler']}")
                else:
                    print(f"       ❌ 无匹配")
                
                # 在recipe_file中查找匹配
                recipe_matches = []
                for recipe in recipe_files:
                    recipe_device = (recipe.get('DEVICE') or '').strip()
                    recipe_stage = (recipe.get('STAGE') or '').strip()
                    recipe_approval = (recipe.get('APPROVAL_STATE') or '').strip()
                    
                    if recipe_device == device and recipe_stage == stage:
                        recipe_matches.append({
                            'approval': recipe_approval,
                            'handler_config': (recipe.get('HANDLER_CONFIG') or '').strip(),
                            'kit_pn': (recipe.get('KIT_PN') or '').strip()
                        })
                
                print(f"     recipe_file匹配: {len(recipe_matches)} 条")
                if recipe_matches:
                    released_recipe_matches = [m for m in recipe_matches if m['approval'] == 'Released']
                    print(f"       其中Released状态: {len(released_recipe_matches)} 条")
                    for j, match in enumerate(recipe_matches[:3], 1):
                        print(f"         {j}. APPROVAL={match['approval']}, HANDLER_CONFIG={match['handler_config']}")
                else:
                    print(f"       ❌ 无匹配")
            
            # ==================== 保存详细分析结果 ====================
            analysis_result = {
                'timestamp': datetime.now().isoformat(),
                'batch_data_quality': batch_data_quality,
                'test_spec_quality': test_spec_quality,
                'recipe_quality': recipe_quality,
                'coverage_analysis': {
                    'wait_combinations_count': len(wait_combinations),
                    'spec_coverage_count': len(covered_combinations),
                    'spec_coverage_rate': len(covered_combinations)/len(wait_combinations)*100 if wait_combinations else 0,
                    'recipe_coverage_count': len(recipe_covered),
                    'recipe_coverage_rate': len(recipe_covered)/len(wait_combinations)*100 if wait_combinations else 0,
                    'joint_coverage_count': len(either_coverage),
                    'joint_coverage_rate': len(either_coverage)/len(wait_combinations)*100 if wait_combinations else 0,
                    'uncovered_combinations': list(still_uncovered)
                }
            }
            
            result_file = f'data_integrity_analysis_result_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n📄 详细分析结果已保存到: {result_file}")
            print("="*80)
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_data_integrity()
    if success:
        print("\n🎉 数据完整性深度分析完成！")
    else:
        print("\n❌ 分析失败！")

if __name__ == "__main__":
    main()