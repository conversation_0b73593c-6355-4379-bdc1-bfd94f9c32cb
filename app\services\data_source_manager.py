"""
数据源管理器 - 支持MySQL和Excel数据源的智能切换
当MySQL数据有问题时，自动切换到Excel数据源
"""

import logging
import os
import sys
import time
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy import text
from app import db
from app.utils.db_helper import get_mysql_connection
from config.aps_config import config
import psutil

# 🎯 引入简化缓存系统 - 适配项目规模
from app.utils.simple_cache import get_simple_cache, cache_result, cache_get, cache_set

# 🔥 Phase 7: 引入强化缓存同步机制
from app.utils.enhanced_cache_sync import (
    get_enhanced_cache_sync, on_data_change_event, CacheEventType
)

logger = logging.getLogger(__name__)

# 数据库连接池导入
# 已迁移到Flask-SQLAlchemy，不再需要自定义连接池
import pymysql

# 统一配置读取函数（使用缓存）
def _get_database_config():
    """获取数据库配置，使用新的统一配置系统"""
    return {
        'host': config.DB_HOST,
        'port': config.DB_PORT,
        'user': config.DB_USER,
        'password': config.DB_PASSWORD,
        'database': config.DB_NAME,
        'charset': config.DB_CHARSET
    }

# 在类的开头添加唯一字段映射配置
UNIQUE_FIELD_MAPPING = {
    'eqp_status': {
        'primary_key': 'id',  # 主键
        'business_key': 'HANDLER_ID',  # 业务主键
        'display_key': 'EQP_ID',  # 显示主键
        'datetime_fields': ['created_at', 'updated_at', 'UPDATE_TIME']  # 日期时间字段
    },
    'et_ft_test_spec': {
        'primary_key': 'id',
        'business_key': 'TEST_SPEC_ID',
        'display_key': 'TEST_SPEC_ID',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'et_uph_eqp': {
        'primary_key': 'id',
        'business_key': 'DEVICE',  # 组合业务键需要额外处理
        'display_key': 'DEVICE',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'ET_UPH_EQP': {  # 大写版本映射
        'primary_key': 'id',
        'business_key': 'DEVICE',
        'display_key': 'DEVICE',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'ct': {
        'primary_key': 'id',
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'CT': {  # 大写版本映射
        'primary_key': 'id',
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'TCC_INV': {
        'primary_key': 'id',
        'business_key': 'id',  # 没有明显业务键，使用ID
        'display_key': 'id',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'et_wait_lot': {
        'primary_key': 'id',  # 实际主键是id，不是LOT_ID
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at', 'CREATE_TIME']
    },
    'lotprioritydone': {
        'primary_key': 'id',
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at', 'timestamp']
    },
    'devicepriorityconfig': {
        'primary_key': 'id',
        'business_key': 'device',
        'display_key': 'device',
        'datetime_fields': ['created_at', 'updated_at', 'from_time', 'end_time', 'refresh_time']
    },
    'lotpriorityconfig': {
        'primary_key': 'id',
        'business_key': 'device',
        'display_key': 'device',
        'datetime_fields': ['created_at', 'updated_at', 'refresh_time']
    }
}

class DataSourceManager:
    """数据源管理器 - 智能切换MySQL和Excel数据源"""
    
    # 性能优化配置
    PERFORMANCE_OPTIMIZATION = {
        'max_query_timeout': 30,  # 最大查询超时时间（秒）
        'batch_size': 5000,       # 批量处理大小
        'memory_threshold': 0.8,  # 内存使用阈值
        'enable_chunked_loading': True,  # 启用分块加载
    }
    
    def _get_db_connection_pooled(self):
        """🔧 统一连接池获取方法 - 使用统一连接管理器"""
        # 🔥 NEW: 使用统一连接管理器替代直接连接池访问
        from app.utils.unified_connection_manager import get_unified_write_connection
        return get_unified_write_connection()
    
    def _get_batch_connection_for_preload(self):
        """🔥 数据预加载专用批量连接 - 使用统一连接管理器"""
        # 🔥 NEW: 使用统一连接管理器的批量连接
        from app.utils.unified_connection_manager import get_unified_batch_connection
        return get_unified_batch_connection('aps', 'data_preload_batch')
    
    def get_all_data_with_single_connection(self) -> Dict:
        """
        🔥 单连接数据预加载 - 用户建议的持久连接策略实现
        
        使用单个持久连接完成所有数据查询，解决268个批次重复连接问题
        替代多次get_db_connection_context()调用
        
        Returns:
            Dict: 包含所有预加载数据的字典
        """
        logger.info("🔥 开始单连接批量数据预加载...")
        start_time = time.time()
        
        all_data = {}
        
        try:
            # 🔥 用户建议实现：一直连着数据库处理所有查询
            with self._get_batch_connection_for_preload() as conn:
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                
                logger.info("🔥 单个持久连接已建立，开始批量查询...")
                
                # 1. ET_WAIT_LOT数据
                cursor.execute("SELECT * FROM et_wait_lot WHERE id IS NOT NULL ORDER BY id")
                wait_lots = cursor.fetchall()
                all_data['wait_lots'] = wait_lots
                logger.info(f"  ✅ ET_WAIT_LOT: {len(wait_lots)}条记录")
                
                # 2. 设备状态数据  
                cursor.execute("SELECT * FROM eqp_status WHERE id IS NOT NULL ORDER BY id")
                equipment_status = cursor.fetchall()
                all_data['equipment_status'] = equipment_status
                logger.info(f"  ✅ EQP_STATUS: {len(equipment_status)}条记录")
                
                # 3. 测试规格数据
                cursor.execute("SELECT * FROM et_ft_test_spec WHERE id IS NOT NULL ORDER BY id")
                test_specs = cursor.fetchall()
                all_data['test_specs'] = test_specs
                logger.info(f"  ✅ ET_FT_TEST_SPEC: {len(test_specs)}条记录")
                
                # 4. UPH设备数据
                cursor.execute("SELECT * FROM et_uph_eqp WHERE id IS NOT NULL ORDER BY id")
                uph_data = cursor.fetchall()
                all_data['uph_data'] = uph_data
                logger.info(f"  ✅ ET_UPH_EQP: {len(uph_data)}条记录")
                
                # 5. Recipe文件数据
                cursor.execute("""
                    SELECT DEVICE, STAGE, PKG_PN, CHIP_ID, RECIPE_FILE_NAME, RECIPE_VER, EQP_TYPE, HANDLER_CONFIG
                    FROM et_recipe_file
                    WHERE DEVICE IS NOT NULL AND DEVICE != ''
                    ORDER BY DEVICE, STAGE
                """)
                recipe_files = cursor.fetchall()
                all_data['recipe_files'] = recipe_files
                logger.info(f"  ✅ ET_RECIPE_FILE: {len(recipe_files)}条记录")
                
                # 6. 设备优先级配置
                cursor.execute("SELECT * FROM devicepriorityconfig ORDER BY id")
                device_priority_rows = cursor.fetchall()
                device_priority = {}
                for row in device_priority_rows:
                    device_priority[row['device']] = row['priority']
                all_data['device_priority'] = device_priority
                logger.info(f"  ✅ DEVICE_PRIORITY: {len(device_priority)}个设备配置")
                
                # 7. 批次优先级配置
                cursor.execute("SELECT * FROM lotpriorityconfig ORDER BY id")
                lot_priority_rows = cursor.fetchall()
                lot_priority = {}
                for row in lot_priority_rows:
                    lot_priority[row['lot_id']] = row['priority']
                all_data['lot_priority'] = lot_priority
                logger.info(f"  ✅ LOT_PRIORITY: {len(lot_priority)}个批次配置")
                
                # 8. 工序映射配置
                cursor.execute("""
                    SELECT * FROM stage_mapping_config 
                    WHERE is_active = true
                    ORDER BY target_stage, priority, id
                """)
                stage_mappings = cursor.fetchall()
                all_data['stage_mappings'] = stage_mappings
                logger.info(f"  ✅ STAGE_MAPPING: {len(stage_mappings)}条映射")
                
                cursor.close()
            
            load_time = time.time() - start_time
            
            logger.info(f"✅ 单连接批量数据预加载完成，耗时: {load_time:.2f}s")
            logger.info(f"🔥 用户建议验证：所有查询使用1个持久连接，避免重复连接消耗")
            
            # 组织数据以便scheduling service使用
            organized_data = {
                'wait_lots': all_data['wait_lots'],
                'equipment_status': all_data['equipment_status'], 
                'test_specs': all_data['test_specs'],
                'uph_data': all_data['uph_data'],
                'recipe_files': all_data['recipe_files'],
                'device_priority': all_data['device_priority'],
                'lot_priority': all_data['lot_priority'],
                'stage_mappings': all_data['stage_mappings'],
                'priority_configs': {
                    'device': all_data['device_priority'],
                    'lot': all_data['lot_priority']
                }
            }
            
            return organized_data
            
        except Exception as e:
            logger.error(f"❌ 单连接数据预加载失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    # 🎯 简化缓存机制 - 移除复杂的依赖关系管理
    # 简化缓存系统已内置按数据类型的TTL管理，无需手动管理依赖关系
    
    # 数据类型元数据配置
    _data_metadata = {
        'wait_lot_data': {'category': 'business', 'volatility': 'high', 'importance': 'critical'},
        'equipment_status_data': {'category': 'business', 'volatility': 'high', 'importance': 'critical'},
        'test_spec_data': {'category': 'configuration', 'volatility': 'low', 'importance': 'critical'},
        'uph_data': {'category': 'configuration', 'volatility': 'medium', 'importance': 'important'},
        'recipe_file_data': {'category': 'configuration', 'volatility': 'low', 'importance': 'important'},
        'device_priority_config': {'category': 'configuration', 'volatility': 'low', 'importance': 'important'},
        'lot_priority_config': {'category': 'configuration', 'volatility': 'low', 'importance': 'important'},
        'stage_mapping_config': {'category': 'configuration', 'volatility': 'low', 'importance': 'normal'},
        'lotprioritydone_data': {'category': 'business', 'volatility': 'medium', 'importance': 'normal'}
    }
    
    def _normalize_datetime_field(self, value):
        """智能日期时间字段标准化"""
        if value is None or value == '' or value == 'None':
            return None
            
        # 如果已经是datetime对象，直接格式化
        if hasattr(value, 'strftime'):
            return value.strftime('%Y-%m-%d %H:%M:%S')
            
        # 转换字符串格式
        try:
            from datetime import datetime
            import re
            
            # 处理GMT格式: 'Tue, 17 Jun 2025 17:17:22 GMT'
            if isinstance(value, str) and 'GMT' in value:
                # 移除星期和GMT，提取核心日期时间
                clean_value = re.sub(r'^[A-Za-z]{3},\s*', '', value)  # 移除 "Tue, "
                clean_value = re.sub(r'\s*GMT\s*$', '', clean_value)   # 移除 " GMT"
                # 转换月份名称为数字
                month_map = {
                    'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
                    'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
                    'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
                }
                for month_name, month_num in month_map.items():
                    clean_value = clean_value.replace(month_name, month_num)
                
                # 重新排列为标准格式 "17 06 2025 17:17:22" -> "2025-06-17 17:17:22"
                parts = clean_value.split()
                if len(parts) >= 4:
                    day, month, year, time = parts[0], parts[1], parts[2], parts[3]
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)} {time}"
            
            # 处理ISO格式或其他标准格式
            if isinstance(value, str):
                # 特殊处理：只有时间部分的情况（如 '08:00:00'）
                if re.match(r'^\d{1,2}:\d{2}:\d{2}$', value):
                    # 将时间部分与当前日期结合
                    current_date = datetime.now().strftime('%Y-%m-%d')
                    return f"{current_date} {value}"
                
                # 尝试多种日期格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d %H:%M:%S.%f',
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%dT%H:%M:%S.%f',
                    '%Y-%m-%dT%H:%M:%SZ',
                    '%Y-%m-%d',
                    '%m/%d/%Y',
                    '%m/%d/%Y %H:%M:%S',
                    '%H:%M:%S'  # 添加纯时间格式支持
                ]
                
                for fmt in formats:
                    try:
                        dt = datetime.strptime(value, fmt)
                        # 如果是纯时间格式，与当前日期结合
                        if fmt == '%H:%M:%S':
                            current_date = datetime.now().strftime('%Y-%m-%d')
                            return f"{current_date} {value}"
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue
                        
                # 如果所有格式都失败，返回当前时间
                logger.warning(f"无法解析日期格式: {value}，使用当前时间")
                return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
        except Exception as e:
            logger.warning(f"日期转换失败: {value}, 错误: {e}")
            from datetime import datetime
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
        return str(value)
    
    def _preprocess_record_data(self, table_name: str, data: Dict) -> Dict:
        """记录数据预处理 - 统一处理日期字段和数据类型"""
        processed_data = data.copy()
        
        # 获取表配置
        field_config = UNIQUE_FIELD_MAPPING.get(table_name, {})
        datetime_fields = field_config.get('datetime_fields', [])
        
        # 处理日期时间字段
        for field in datetime_fields:
            if field in processed_data:
                processed_data[field] = self._normalize_datetime_field(processed_data[field])
        
        # 处理优先级字段的数据类型转换
        if 'priority' in processed_data:
            priority_value = processed_data['priority']
            if isinstance(priority_value, str):
                # 字符串优先级映射为数字
                priority_mapping = {
                    'high': 1,
                    'medium': 2,
                    'normal': 2,
                    'low': 3,
                    'urgent': 0,
                    'critical': 0
                }
                processed_data['priority'] = priority_mapping.get(priority_value.lower(), 2)  # 默认medium
            elif isinstance(priority_value, (int, float)):
                processed_data['priority'] = int(priority_value)
        
        # 特殊处理：自动添加created_at和updated_at
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if 'created_at' not in processed_data or not processed_data.get('created_at'):
            processed_data['created_at'] = current_time
            
        if 'updated_at' not in processed_data or not processed_data.get('updated_at'):
            processed_data['updated_at'] = current_time
        
        # 移除空值字段（避免数据库约束错误）
        filtered_data = {}
        for key, value in processed_data.items():
            if value is not None and value != '':
                filtered_data[key] = value
                
        return filtered_data
    
    def _apply_filters(self, data: List[Dict], filters: List[Dict]) -> List[Dict]:
        """应用筛选条件到数据集"""
        if not filters or not data:
            return data
        
        filtered_data = []
        
        for record in data:
            match_all_filters = True
            
            for filter_condition in filters:
                field = filter_condition.get('field', '')
                operator = filter_condition.get('operator', 'contains')
                value = filter_condition.get('value', '')
                
                if not field:
                    continue
                
                # 获取记录中的字段值
                record_value = record.get(field, '')
                
                # 转换为字符串进行比较（处理None值）
                if record_value is None:
                    record_value = ''
                else:
                    record_value = str(record_value).lower()
                
                filter_value = str(value).lower()
                
                # 应用不同的操作符
                field_matches = False
                try:
                    if operator == 'contains':
                        field_matches = filter_value in record_value
                    elif operator == 'equals':
                        field_matches = record_value == filter_value
                    elif operator == 'starts_with':
                        field_matches = record_value.startswith(filter_value)
                    elif operator == 'ends_with':
                        field_matches = record_value.endswith(filter_value)
                    elif operator == 'not_equals':
                        field_matches = record_value != filter_value
                    elif operator == 'is_empty':
                        field_matches = record_value == '' or record_value is None
                    elif operator == 'is_not_empty':
                        field_matches = record_value != '' and record_value is not None
                    elif operator == 'greater_than':
                        # 尝试数值比较
                        try:
                            field_matches = float(record_value) > float(filter_value)
                        except (ValueError, TypeError):
                            field_matches = record_value > filter_value
                    elif operator == 'less_than':
                        # 尝试数值比较
                        try:
                            field_matches = float(record_value) < float(filter_value)
                        except (ValueError, TypeError):
                            field_matches = record_value < filter_value
                    else:
                        # 默认使用包含操作
                        field_matches = filter_value in record_value
                        
                except Exception as e:
                    logger.warning(f"筛选条件应用失败: {field} {operator} {value}, 错误: {e}")
                    field_matches = False
                
                # 如果任何一个筛选条件不匹配，则该记录不符合要求
                if not field_matches:
                    match_all_filters = False
                    break
            
            # 只有所有筛选条件都匹配才保留该记录
            if match_all_filters:
                filtered_data.append(record)
        
        logger.info(f"筛选条件: {filters}")
        logger.info(f"筛选结果: 从 {len(data)} 条记录筛选到 {len(filtered_data)} 条记录")
        
        return filtered_data
    
    def _get_cached_data(self, data_type: str, fetch_func):
        """
        🎯 简化缓存获取方法 - 使用简单高效的LRU+TTL缓存
        
        Args:
            data_type: 数据类型标识
            fetch_func: 数据获取函数
            
        Returns:
            数据内容
        """
        try:
            # 数据类型到缓存分类的映射
            category_mapping = {
                'equipment_status_data': 'equipment_status',
                'wait_lot_data': 'wait_lots',
                'ct_data': 'system_config',
                'lotprioritydone_data': 'wait_lots',
                'test_spec_data': 'test_specs',
                'uph_data': 'uph_data',
                'recipe_file_data': 'recipe_data',
                'device_priority_config': 'system_config',
                'lot_priority_config': 'system_config',
                'stage_mapping_config': 'system_config'
            }
            
            category = category_mapping.get(data_type, 'default')
            cache_key = f"dsm_{data_type}"
            
            # 1. 尝试从简化缓存获取数据
            cached_data = cache_get(cache_key, category)
            if cached_data is not None:
                logger.debug(f"🎯 简化缓存命中: {data_type}")
                return cached_data
            
            # 2. 缓存未命中，获取新数据
            logger.debug(f"📥 获取新数据: {data_type}")
            fresh_data = fetch_func()
            
            # 3. 存储到简化缓存
            if cache_set(cache_key, fresh_data, category):
                logger.debug(f"🎯 简化缓存更新: {data_type} -> {category}")
            
            return fresh_data
            
        except Exception as e:
            logger.error(f"❌ 简化缓存获取失败 - {data_type}: {e}")
            # 异常时直接获取数据，不使用缓存
            return fetch_func()

    # 🎯 简化：移除复杂的依赖版本管理，简化缓存系统已内置处理
    
    def clear_cache(self, data_type: str = None):
        """
        🎯 清理简化缓存
        
        Args:
            data_type: 要清理的数据类型，None表示清理全部缓存
        """
        try:
            cache = get_simple_cache()
            if data_type:
                # 清理特定数据类型的缓存
                category_mapping = {
                    'equipment_status_data': 'equipment_status',
                    'wait_lot_data': 'wait_lots',
                    'test_spec_data': 'test_specs',
                    'uph_data': 'uph_data',
                    'recipe_file_data': 'recipe_data'
                }
                category = category_mapping.get(data_type, 'default')
                cache.clear_category(category)
                logger.info(f"🧹 已清理简化缓存分类: {category}")
            else:
                # 清理全部缓存
                cache.clear_all()
                logger.info("🧹 已清理全部简化缓存")
                
        except Exception as e:
            logger.error(f"❌ 清理简化缓存失败: {e}")
    
    def invalidate_cache_on_data_change(self, table_name: str):
        """
        🎯 数据变更时失效相关的确定性缓存
        
        Args:
            table_name: 发生变更的数据库表名
        """
        # 数据库表名到数据类型的映射 - 🔥 扩展至所有调度关键表
        table_to_data_type_mappings = {
            # 🎯 核心调度业务表
            'et_wait_lot': ['wait_lot_data'],
            'eqp_status': ['equipment_status_data'],
            'et_ft_test_spec': ['test_spec_data'],
            'et_uph_eqp': ['uph_data'],
            'et_recipe_file': ['recipe_file_data'],
            'lotprioritydone': ['lotprioritydone_data'],
            
            # 🔧 调度配置表
            'devicepriorityconfig': ['device_priority_config'],
            'lotpriorityconfig': ['lot_priority_config'],
            'stage_mapping_config': ['stage_mapping_config'],
            
            # 🔄 兼容大写表名
            'ET_WAIT_LOT': ['wait_lot_data'],
            'EQP_STATUS': ['equipment_status_data'],
            'ET_FT_TEST_SPEC': ['test_spec_data'],
            'ET_UPH_EQP': ['uph_data'],
            'ET_RECIPE_FILE': ['recipe_file_data'],
            'LOTPRIORITYDONE': ['lotprioritydone_data'],
            'DEVICEPRIORITYCONFIG': ['device_priority_config'],
            'LOTPRIORITYCONFIG': ['lot_priority_config'],
            'STAGE_MAPPING_CONFIG': ['stage_mapping_config'],
        }
        
        affected_data_types = table_to_data_type_mappings.get(table_name, [])
        
        # 清理直接相关的数据类型
        for data_type in affected_data_types:
            self.clear_cache(data_type)
        
        # 🎯 简化：移除复杂的依赖关系管理
        # 简化缓存系统无需手动管理依赖关系
        dependent_types = []
        
        for dep_type in dependent_types:
            self.clear_cache(dep_type)
        
        all_affected = affected_data_types + dependent_types
        if all_affected:
            logger.info(f"🔄 表 {table_name} 变更，已失效相关确定性缓存: {all_affected}")
    
    def on_data_modified(self, table_name: str, operation: str = 'UPDATE', record_id: str = None):
        """
        🔥 数据修改监听器 - 自动失效相关缓存
        
        Args:
            table_name: 修改的表名
            operation: 操作类型 (INSERT, UPDATE, DELETE)
            record_id: 记录ID (可选)
        """
        try:
            logger.info(f"📢 检测到数据变更: 表={table_name}, 操作={operation}, ID={record_id}")
            
            # 立即失效相关缓存
            self.invalidate_cache_on_data_change(table_name)
            
            # 🔥 特别处理：关键调度表数据变更影响调度算法
            critical_scheduling_tables = [
                'et_wait_lot', 'ET_WAIT_LOT',           # 待排产批次
                'eqp_status', 'EQP_STATUS',             # 设备状态
                'et_ft_test_spec', 'ET_FT_TEST_SPEC',   # 测试规范
                'et_uph_eqp', 'ET_UPH_EQP',             # 产能数据
                'devicepriorityconfig', 'DEVICEPRIORITYCONFIG',  # 设备优先级
                'lotpriorityconfig', 'LOTPRIORITYCONFIG'          # 批次优先级
            ]
            
            if table_name in critical_scheduling_tables:
                logger.warning(f"⚠️ 关键调度数据变更 ({table_name})，建议重新执行调度以确保数据准确性")
                
                # 强制刷新所有调度相关缓存
                try:
                    critical_caches = [
                        'wait_lot_data', 'equipment_status_data', 'test_spec_data',
                        'uph_data', 'recipe_file_data', 'device_priority_config',
                        'lot_priority_config', 'stage_mapping_config'
                    ]
                    
                    cleared_count = 0
                    for cache_type in critical_caches:
                        try:
                            self.clear_cache(cache_type)
                            cleared_count += 1
                        except Exception as e:
                            logger.warning(f"⚠️ 清理缓存 {cache_type} 失败: {e}")
                    
                    logger.info(f"🧹 已强制刷新 {cleared_count}/{len(critical_caches)} 个调度相关缓存")
                except Exception as cache_error:
                    logger.error(f"❌ 批量强制刷新缓存失败: {cache_error}")
            
        except Exception as e:
            logger.error(f"❌ 数据修改监听器异常: {e}")
    
    def register_data_change_listener(self):
        """
        🔧 注册数据变更监听器 - 集成到Flask应用中
        """
        try:
            from flask import current_app, g
            
            # 将监听器注册到Flask g对象中，供其他模块调用
            g.data_change_listener = self.on_data_modified
            logger.info("✅ 数据变更监听器已注册到Flask应用")
            
        except Exception as e:
            logger.error(f"❌ 注册数据变更监听器失败: {e}")
    
    def get_cache_status(self):
        """
        🎯 获取确定性缓存状态信息
        
        Returns:
            Dict: 缓存状态详细信息
        """
        try:
            # 获取简化缓存统计信息
            cache = get_simple_cache()
            cache_stats = cache.get_stats()
            category_info = cache.get_category_info()
            
            # 扩展统计信息
            status_info = {
                'cache_type': 'simple_cache',
                'cache_statistics': cache_stats,
                'data_type_summary': {},
                'dependency_info': '已简化，无需依赖关系管理',
                'metadata_config': self._data_metadata
            }
            
            # 🎯 简化：移除依赖关系管理，使用简化统计信息
            # 复杂的版本信息管理已简化为基本缓存统计
            
            return status_info
            
        except Exception as e:
            logger.error(f"❌ 获取确定性缓存状态失败: {e}")
            return {
                'cache_type': 'deterministic',
                'error': str(e),
                'fallback_info': 'Cache status unavailable'
            }
    
    def validate_data_consistency_for_scheduling(self, required_data_types: List[str] = None, 
                                                consistency_level: str = 'moderate'):
        """
        🔍 排产前数据一致性验证
        
        Args:
            required_data_types: 需要验证的数据类型列表
            consistency_level: 一致性验证级别
            
        Returns:
            Dict: 一致性验证报告
        """
        if required_data_types is None:
            required_data_types = [
                'wait_lot_data',
                'equipment_status_data',
                'test_spec_data',
                'uph_data',
                'device_priority_config',
                'lot_priority_config'
            ]
        
        try:
            # 简化数据一致性检查 - 移除复杂的验证逻辑
            cache = get_simple_cache()
            cache_stats = cache.get_stats()
            
            # 简化的一致性评分：基于缓存命中率
            consistency_score = cache_stats.get('hit_rate_percent', 0) / 100.0
            is_consistent = consistency_score > 0.6  # 60%以上认为一致
            
            logger.info(f"🔍 简化数据一致性检查完成 - 评分: {consistency_score:.2f}, 一致: {is_consistent}")
            
            return {
                'validation_success': True,
                'consistency_report': {
                    'overall_score': consistency_score,
                    'is_consistent': is_consistent,
                    'validation_time': 0.001,  # 简化验证无需时间
                    'issues_count': 0,
                    'critical_issues': [],
                    'recommendation': '使用简化缓存系统，无需复杂验证',
                    'data_summary': cache_stats
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 排产数据一致性验证失败: {e}")
            return {
                'validation_success': False,
                'error': str(e),
                'recommendation': '数据一致性验证失败，建议检查系统状态后重试'
            }
    
    def create_data_snapshot_for_scheduling(self, data_types: List[str] = None) -> Dict[str, Any]:
        """
        📸 为排产创建数据快照，确保数据一致性
        
        Args:
            data_types: 要包含在快照中的数据类型
            
        Returns:
            Dict: 快照信息和数据
        """
        if data_types is None:
            data_types = ['wait_lot_data', 'equipment_status_data', 'test_spec_data', 'uph_data']
        
        try:
            # 简化快照创建 - 获取当前缓存状态
            cache = get_simple_cache()
            cache_stats = cache.get_stats()
            category_info = cache.get_category_info()
            
            snapshot_id = f"simple_snapshot_{int(time.time())}"
            
            logger.info(f"📸 简化数据快照创建成功: {snapshot_id}")
            
            return {
                'snapshot_created': True,
                'snapshot_id': snapshot_id,
                'consistency_score': cache_stats.get('hit_rate_percent', 0) / 100.0,
                'conflicts': [],  # 简化系统无冲突
                'data_types': data_types,
                'snapshot_data': {'cache_stats': cache_stats, 'category_info': category_info},
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"❌ 创建排产数据快照失败: {e}")
            return {
                'snapshot_created': False,
                'error': str(e)
            }
    
    def auto_fix_consistency_issues(self, validation_report: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔧 自动修复数据一致性问题
        
        Args:
            validation_report: 一致性验证报告
            
        Returns:
            Dict: 修复结果
        """
        try:
            if not validation_report.get('validation_success'):
                return {'fix_success': False, 'message': '验证报告无效'}
            
            # 模拟报告对象（实际使用中需要从验证器创建）
            from app.utils.data_consistency_validator import ConsistencyReport, InconsistencyIssue, InconsistencyType
            
            # 这里简化处理，实际应该传递完整的报告对象
            # 目前只支持清理缓存的自动修复
            fix_actions = ['清理相关缓存', '重新创建数据快照']
            
            # 清理所有缓存，触发重新加载
            self.clear_cache()
            
            logger.info("🔧 已执行自动修复：清理全部确定性缓存")
            
            return {
                'fix_success': True,
                'actions_taken': fix_actions,
                'message': '已清理缓存，下次数据访问将重新加载最新数据'
            }
            
        except Exception as e:
            logger.error(f"❌ 自动修复一致性问题失败: {e}")
            return {
                'fix_success': False,
                'error': str(e)
            }
    def __init__(self):
        self.current_source = 'mysql'  # 只使用MySQL
        self._mysql_available = None  # 延迟检查，避免应用上下文问题
        
        # 🔥 Phase 7: 初始化强化缓存同步机制
        try:
            self.enhanced_cache_sync = get_enhanced_cache_sync()
            logger.info("🔥 强化缓存同步机制已集成到DataSourceManager")
        except Exception as e:
            logger.warning(f"⚠️ 强化缓存同步集成失败: {e}")
            self.enhanced_cache_sync = None
    

    
    def _get_mysql_table_data(self, table_name: str, limit: int = None) -> Dict[str, Dict]:
        """通用MySQL表数据获取方法 - 处理任何ID范围和唯一字段 (Flask-SQLAlchemy版本)"""
        try:
            from sqlalchemy import text
            
            # 获取表的唯一字段配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id', 
                'display_key': 'id'
            })
            
            # 构建查询SQL
            if limit and limit > 0:
                sql = text(f"""
                    SELECT * FROM {table_name} 
                    WHERE id IS NOT NULL
                    ORDER BY id
                    LIMIT :limit
                """)
                result = db.session.execute(sql, {'limit': limit})
            else:
                sql = text(f"""
                    SELECT * FROM {table_name} 
                    WHERE id IS NOT NULL
                    ORDER BY id
                """)
                result = db.session.execute(sql)
            
            table_data = {}
            for row in result.mappings():
                # 使用ID作为主键确保唯一性（无论ID从何开始）
                primary_key = str(row.get(field_config['primary_key'], ''))
                
                # 保留所有原始数据 - 转换为字典
                table_data[primary_key] = dict(row)
                
                # 根据表类型添加标准化字段
                if table_name == 'eqp_status':
                    table_data[primary_key].update({
                            'EQP_ID': str(row.get('DEVICE', '') or row.get('HANDLER_ID', '') or primary_key),
                            'EQP_NAME': str(row.get('HANDLER_TYPE', '') or row.get('DEVICE', '') or '未知设备'),
                            'EQP_STATUS': str(row.get('STATUS', 'UNKNOWN')),
                            'TESTER_ID': str(row.get('TESTER_ID', '')),
                            'HANDLER_ID': str(row.get('HANDLER_ID', '')),
                            'FAC_ID': str(row.get('FAC_ID', '')),
                            'UPDATE_TIME': row.get('updated_at', row.get('UPDATE_TIME')),
                            'available': str(row.get('STATUS', '')).upper() in ['RUN', 'IDLE', 'SETUP', 'READY', 'ACTIVE', 'ONLINE']
                        })
            
            logger.info(f"从MySQL获取到 {len(table_data)} 条{table_name}数据")
            return table_data
            
        except Exception as e:
            logger.error(f"MySQL获取{table_name}数据失败: {e}")
            return {}
        
    @property
    def mysql_available(self):
        """延迟检查MySQL可用性"""
        if self._mysql_available is None:
            self._check_mysql_availability()
        return self._mysql_available
    
    # ✅ Excel相关属性已移除 - 全面转向MySQL
    
    def _check_mysql_availability(self):
        """检查MySQL可用性 - 修复版（避免事务冲突）"""
        max_retries = 3
        retry_delay = 0.5  # 秒
        
        for attempt in range(max_retries):
            try:
                from sqlalchemy import text
                # 🔧 修复：避免事务冲突，使用简单查询
                result = db.session.execute(text("SELECT 1"))
                result.fetchone()
                
                self._mysql_available = True
                if attempt > 0:
                    logger.info(f"✅ MySQL连接恢复成功 (重试{attempt}次)")
                else:
                    logger.debug("✅ MySQL数据源可用")
                return
                
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"❌ MySQL连接尝试{attempt+1}失败，{retry_delay}秒后重试: {e}")
                    import time
                    time.sleep(retry_delay)
                else:
                    self._mysql_available = False
                    logger.error(f"❌ MySQL连接最终失败: {e}")
                    # 🔧 修复：事务冲突时尝试回滚
                    try:
                        db.session.rollback()
                        logger.info("🔄 已回滚事务，下次调用将重新尝试")
                    except Exception as rollback_error:
                        logger.warning(f"⚠️ 事务回滚失败: {rollback_error}")
    

    
    def _check_data_sources(self):
        """检查数据源可用性 - 只检查MySQL"""
        self._check_mysql_availability()
        # ✅ 移除Excel检查 - 全面转向MySQL
        logger.info(f"数据源状态检查完成: MySQL={self.mysql_available}")
    
    def get_wait_lot_data(self, force_refresh: bool = False) -> Tuple[List[Dict], str]:
        """
        🎯 获取待排产批次数据 - 仅MySQL源，使用确定性缓存
        
        Args:
            force_refresh: 是否强制刷新缓存
        
        Returns:
            Tuple[List[Dict], str]: (数据列表, 数据源名称)
        """
        data_type = "wait_lot_data"
        
        # 🔥 强制刷新缓存
        if force_refresh:
            self.clear_cache(data_type)
            logger.info(f"🧹 已强制刷新缓存: {data_type}")
        
        # 只使用MySQL数据源
        if self.mysql_available:
            try:
                data = self._get_cached_data(data_type, self._get_wait_lot_from_mysql)
                
                # 🔍 数据一致性验证 - 使用统一连接管理器
                if data and not force_refresh:
                    try:
                        # 🔥 NEW: 使用统一连接管理器进行一致性检查
                        from app.utils.unified_connection_manager import get_unified_read_connection
                        with get_unified_read_connection() as conn:
                            if hasattr(conn, 'execute'):  # Flask-SQLAlchemy连接
                                from sqlalchemy import text
                                count_result = conn.execute(text(
                                    "SELECT COUNT(*) as count FROM et_wait_lot WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL AND LOT_ID != ''"
                                ))
                                actual_count = count_result.fetchone().count
                            else:  # 自定义连接池
                                cursor = conn.cursor()
                                cursor.execute(
                                    "SELECT COUNT(*) as count FROM et_wait_lot WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL AND LOT_ID != ''"
                                )
                                actual_count = cursor.fetchone()[0]
                        cached_count = len(data)
                        
                        # 数量不匹配时自动刷新
                        if actual_count != cached_count:
                            logger.warning(f"⚠️ 数据不一致检测: 缓存={cached_count}条, 数据库={actual_count}条, 自动刷新缓存")
                            self.clear_cache(data_type)
                            data = self._get_cached_data(data_type, self._get_wait_lot_from_mysql)
                            logger.info(f"🔄 缓存已刷新，重新获取到 {len(data)} 条数据")
                        
                    except Exception as check_error:
                        logger.warning(f"⚠️ 数据一致性检查失败: {check_error}")
                
                if data:
                    logger.info(f"📊 从MySQL获取到 {len(data)} 条待排产批次数据（确定性缓存）")
                    return data, "MySQL"
                else:
                    logger.warning("MySQL连接正常但未获取到数据")
                    return [], "MySQL"
            except Exception as e:
                logger.error(f"MySQL获取待排产数据失败: {e}")
                return [], "Error"
        else:
            logger.error("❌ MySQL数据源不可用，无备用数据源")
            return [], "Error"
    
    def get_test_spec_data(self) -> Tuple[Dict[str, Dict], str]:
        """
        🎯 获取测试规范数据 - 使用确定性缓存，仅MySQL源
        
        Returns:
            Tuple[Dict[str, Dict], str]: (测试规范字典, 数据源名称)
        """
        data_type = "test_spec_data"
        
        # 🔧 强制使用MySQL，确保数据一致性
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取测试规范数据")
            self._check_mysql_availability()
            if not self.mysql_available:
                raise RuntimeError("MySQL数据源连接失败，排产系统无法正常工作")
        
        try:
            data = self._get_cached_data(data_type, self._get_test_spec_from_mysql)
            if data:
                logger.info(f"🔬 从MySQL获取到 {len(data)} 条测试规范数据（确定性缓存）")
                return data, "MySQL"
            else:
                logger.error("❌ MySQL测试规范数据获取为空")
                return {}, "None"
        except Exception as e:
            logger.error(f"❌ MySQL获取测试规范数据失败: {e}")
            raise
    
    def get_recipe_file_data(self) -> Tuple[Dict[str, Dict], str]:
        """
        🎯 获取工艺配方数据 - 仅MySQL源，使用确定性缓存
        
        Returns:
            Tuple[Dict[str, Dict], str]: (配方数据字典, 数据源名称)
        """
        data_type = "recipe_file_data"
        
        # 只使用MySQL数据源
        if self.mysql_available:
            try:
                data = self._get_cached_data(data_type, self._get_recipe_file_from_mysql)
                if data:
                    logger.info(f"📄 从MySQL获取到 {len(data)} 条工艺配方数据（确定性缓存）")
                    return data, "MySQL"
                else:
                    logger.warning("MySQL连接正常但未获取到工艺配方数据")
                    return {}, "MySQL"
            except Exception as e:
                logger.error(f"MySQL获取工艺配方数据失败: {e}")
                return {}, "Error"
        else:
            logger.error("❌ MySQL数据源不可用，无法获取工艺配方数据")
            return {}, "Error"
    
    def get_equipment_status_data(self, force_refresh: bool = False) -> Tuple[Dict[str, Dict], str]:
        """
        🎯 获取设备状态数据 - 使用确定性缓存，仅MySQL源
        
        Args:
            force_refresh: 是否强制刷新缓存
        
        Returns:
            Tuple[Dict[str, Dict], str]: (设备状态字典, 数据源名称)
        """
        data_type = "equipment_status_data"
        
        # 🔥 强制刷新缓存
        if force_refresh:
            self.clear_cache(data_type)
            logger.info(f"🧹 已强制刷新缓存: {data_type}")
        
        # 🔧 强制使用MySQL，确保数据一致性
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取设备状态数据")
            # 🔧 尝试重新连接MySQL
            self._check_mysql_availability()
            if not self.mysql_available:
                raise RuntimeError("MySQL数据源连接失败，排产系统无法正常工作")
        
        try:
            data = self._get_cached_data(data_type, self._get_equipment_status_from_mysql)
            
            # 🔍 设备状态数据一致性验证 - 使用统一连接管理器
            if data and not force_refresh:
                try:
                    # 🔥 NEW: 使用统一连接管理器进行一致性检查
                    from app.utils.unified_connection_manager import get_unified_read_connection
                    with get_unified_read_connection() as conn:
                        if hasattr(conn, 'execute'):  # Flask-SQLAlchemy连接
                            from sqlalchemy import text
                            count_result = conn.execute(text("SELECT COUNT(*) as count FROM eqp_status"))
                            actual_count = count_result.fetchone().count
                        else:  # 自定义连接池
                            cursor = conn.cursor()
                            cursor.execute("SELECT COUNT(*) as count FROM eqp_status")
                            actual_count = cursor.fetchone()[0]
                    cached_count = len(data)
                    
                    if actual_count != cached_count:
                        logger.warning(f"⚠️ 设备状态数据不一致: 缓存={cached_count}条, 数据库={actual_count}条, 自动刷新缓存")
                        self.clear_cache(data_type)
                        data = self._get_cached_data(data_type, self._get_equipment_status_from_mysql)
                        logger.info(f"🔄 缓存已刷新，重新获取到 {len(data)} 条设备数据")
                        
                except Exception as check_error:
                    logger.warning(f"⚠️ 设备状态一致性检查失败: {check_error}")
            
            if data:
                logger.info(f"🏭 从MySQL获取到 {len(data)} 条设备状态数据（确定性缓存）")
                return data, "MySQL"
            else:
                logger.error("❌ MySQL数据获取为空")
                return {}, "None"
        except Exception as e:
            logger.error(f"❌ MySQL获取设备状态数据失败: {e}")
            raise
    
    def get_uph_data(self) -> Tuple[Dict[str, Dict], str]:
        """
        🎯 获取UPH数据 - 使用确定性缓存，仅MySQL源
        
        Returns:
            Tuple[Dict[str, Dict], str]: (UPH数据字典, 数据源名称)
        """
        data_type = "uph_data"
        
        # 🔧 强制使用MySQL，确保数据一致性
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取UPH数据")
            self._check_mysql_availability()
            if not self.mysql_available:
                raise RuntimeError("MySQL数据源连接失败，排产系统无法正常工作")
        
        try:
            data = self._get_cached_data(data_type, self._get_uph_from_mysql)
            if data:
                logger.info(f"⚡ 从MySQL获取到 {len(data)} 条UPH数据（确定性缓存）")
                return data, "MySQL"
            else:
                logger.error("❌ MySQL UPH数据获取为空")
                return {}, "None"
        except Exception as e:
            logger.error(f"❌ MySQL获取UPH数据失赅: {e}")
            raise
    
    def get_priority_configs(self) -> Tuple[Dict[str, Dict], str]:
        """获取优先级配置数据 - 仅MySQL源"""
        # 只使用MySQL数据源
        if self.mysql_available:
            try:
                data = self._get_priority_configs_from_mysql()
                device_count = len(data.get('device', {}))
                lot_count = len(data.get('lot', {}))
                if device_count > 0 or lot_count > 0:
                    logger.info(f"🎯 从MySQL获取到优先级配置 - 设备: {device_count}, 批次: {lot_count}")
                    return data, "MySQL"
                else:
                    logger.warning("MySQL连接正常但未获取到优先级配置数据")
                    return {'device': {}, 'lot': {}}, "MySQL"
            except Exception as e:
                logger.error(f"MySQL获取优先级配置失败: {e}")
                return {'device': {}, 'lot': {}}, "Error"
        else:
            logger.error("❌ MySQL数据源不可用，无法获取优先级配置")
            return {'device': {}, 'lot': {}}, "Error"
    
    def get_lotprioritydone_data(self) -> Tuple[List[Dict], str]:
        """
        🎯 获取已排产批次数据 - 使用确定性缓存，仅MySQL源
        
        Returns:
            Tuple[List[Dict], str]: (已排产数据列表, 数据源名称)
        """
        data_type = "lotprioritydone_data"
        
        # 🔧 强制使用MySQL，确保数据一致性
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取已排产数据")
            self._check_mysql_availability()
            if not self.mysql_available:
                raise RuntimeError("MySQL数据源连接失败，无法获取已排产数据")
        
        try:
            data = self._get_cached_data(data_type, self._get_lotprioritydone_from_mysql)
            if data:
                logger.info(f"✅ 从MySQL获取到 {len(data)} 条已排产数据（确定性缓存）")
                return data, "MySQL"
            else:
                logger.info("MySQL已排产数据获取为空")
                return [], "MySQL"
        except Exception as e:
            logger.error(f"❌ MySQL获取已排产数据失败: {e}")
            raise
    
    def get_ct_data(self) -> Tuple[List[Dict], str]:
        """
        🎯 获取CT生产历史数据 - 使用确定性缓存，仅MySQL源
        
        Returns:
            Tuple[List[Dict], str]: (CT历史数据列表, 数据源名称)
        """
        data_type = "ct_data"
        
        # 🔧 强制使用MySQL，确保数据一致性
        if not self.mysql_available:
            logger.error("❌ MySQL数据源不可用，无法获取CT数据")
            self._check_mysql_availability()
            if not self.mysql_available:
                raise RuntimeError("MySQL数据源连接失败，无法获取CT数据")
        
        try:
            data = self._get_cached_data(data_type, self._get_ct_from_mysql)
            if data:
                logger.info(f"📊 从MySQL获取到 {len(data)} 条CT历史数据（确定性缓存）")
                return data, "MySQL"
            else:
                logger.info("MySQL CT数据获取为空")
                return [], "MySQL"
        except Exception as e:
            logger.error(f"❌ MySQL获取CT数据失败: {e}")
            raise
    
    # MySQL数据获取方法
    def _get_wait_lot_from_mysql(self) -> List[Dict]:
        """🔥 从MySQL获取待排产批次数据 - 使用统一连接管理器"""
        try:
            # 🔥 NEW: 使用统一连接管理器替代Flask-SQLAlchemy
            from app.utils.unified_connection_manager import get_unified_read_connection
            import pymysql
            
            with get_unified_read_connection() as conn:
                if hasattr(conn, 'execute'):  # Flask-SQLAlchemy连接
                    from sqlalchemy import text
                    result = conn.execute(text("""
                        SELECT LOT_ID, LOT_TYPE, PROD_ID, PO_ID, DEVICE, STAGE, STEP, GOOD_QTY, PKG_PN, CHIP_ID, CREATE_TIME,
                               FAC_ID, FLOW_ID, FLOW_VER, WIP_STATE, PROC_STATE, HOLD_STATE
                        FROM et_wait_lot
                        WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL AND LOT_ID != ''
                        ORDER BY CREATE_TIME DESC
                    """))
                    wait_lots = [dict(row) for row in result.mappings()]
                    
                else:  # 自定义连接池
                    cursor = conn.cursor(pymysql.cursors.DictCursor)
                    cursor.execute("""
                        SELECT LOT_ID, LOT_TYPE, PROD_ID, PO_ID, DEVICE, STAGE, STEP, GOOD_QTY, PKG_PN, CHIP_ID, CREATE_TIME,
                               FAC_ID, FLOW_ID, FLOW_VER, WIP_STATE, PROC_STATE, HOLD_STATE
                        FROM et_wait_lot
                        WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL AND LOT_ID != ''
                        ORDER BY CREATE_TIME DESC
                    """)
                    wait_lots = cursor.fetchall()
                
                # 数据清洗
                for lot in wait_lots:
                    try:
                        qty_val = lot.get('GOOD_QTY')
                        if qty_val is not None:
                            lot['GOOD_QTY'] = int(float(qty_val))
                        else:
                            lot['GOOD_QTY'] = 0
                    except (ValueError, TypeError):
                        lot['GOOD_QTY'] = 0
                    lot['CREATE_TIME'] = self._normalize_datetime_field(lot.get('CREATE_TIME'))
                
                return wait_lots
            
        except Exception as e:
            logger.error(f"MySQL获取待排产批次数据失败: {e}")
            return []
    
    def _get_test_spec_from_mysql(self) -> Dict[str, Dict]:
        """🔥 从MySQL获取测试规范数据 - 使用统一连接管理器"""
        try:
            # 🔥 NEW: 使用统一连接管理器替代Flask-SQLAlchemy
            from app.utils.unified_connection_manager import get_unified_read_connection
            import pymysql
            
            with get_unified_read_connection() as conn:
                if hasattr(conn, 'execute'):  # Flask-SQLAlchemy连接
                    from sqlalchemy import text
                    result = conn.execute(text("""
                SELECT * FROM et_ft_test_spec 
                WHERE id IS NOT NULL 
                ORDER BY APPROVAL_STATE DESC, id DESC
                    """))
                    test_specs = {}
                    for i, row in enumerate(result.mappings()):
                        # 使用组合键或序号作为主键
                        device = str(row.get('DEVICE', '') or f'DEVICE_{i:03d}')
                        stage = str(row.get('STAGE', ''))
                        approval_state = str(row.get('APPROVAL_STATE', ''))
                        
                        key = f"{device}|{stage}" if stage else device
                        
                        # 🔧 修复：只有当键不存在或当前记录是Released状态时才覆盖
                        if key not in test_specs or approval_state == 'Released':
                            test_specs[key] = dict(row)
                            
                else:  # 自定义连接池
                    cursor = conn.cursor(pymysql.cursors.DictCursor)
                    cursor.execute("""
                        SELECT * FROM et_ft_test_spec 
                        WHERE id IS NOT NULL 
                        ORDER BY APPROVAL_STATE DESC, id DESC
                    """)
                    results = cursor.fetchall()
                    
                    test_specs = {}
                    for i, row in enumerate(results):
                        # 使用组合键或序号作为主键
                        device = str(row.get('DEVICE', '') or f'DEVICE_{i:03d}')
                        stage = str(row.get('STAGE', ''))
                        approval_state = str(row.get('APPROVAL_STATE', ''))
                        
                        key = f"{device}|{stage}" if stage else device
                        
                        # 🔧 修复：只有当键不存在或当前记录是Released状态时才覆盖
                        if key not in test_specs or approval_state == 'Released':
                            test_specs[key] = dict(row)
                
                logger.info(f"从MySQL获取到 {len(test_specs)} 条测试规范数据")
                return test_specs
            
        except Exception as e:
            logger.error(f"MySQL获取测试规范数据失败: {e}")
            return {}
    
    def _get_recipe_file_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取工艺配方数据 - 使用连接池"""
        try:
            # 🔧 修复：使用连接池而不是直接创建连接
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    cursor.execute("""
                    SELECT DEVICE, STAGE, PKG_PN, CHIP_ID, RECIPE_FILE_NAME, RECIPE_VER, EQP_TYPE, HANDLER_CONFIG
                    FROM et_recipe_file
                    WHERE DEVICE IS NOT NULL AND DEVICE != ''
                """)
                
                    recipe_files = {}
                    for row in cursor.fetchall():
                        device, stage, pkg_pn, chip_id = row[0] or '', row[1] or '', row[2] or '', row[3] or ''
                        
                        keys = [
                            f"{device}|{stage}|{pkg_pn}|{chip_id}",
                            f"{device}|{stage}|{pkg_pn}",
                            f"{device}|{stage}",
                            f"{device}"
                        ]
                        
                        recipe_data = {
                            'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'CHIP_ID': chip_id,
                            'RECIPE_FILE_NAME': row[4] or '', 'RECIPE_VER': row[5] or '',
                            'EQP_TYPE': row[6] or '', 'HANDLER_CONFIG': row[7] or ''
                        }
                        
                        for key in keys:
                            if key not in recipe_files:
                                recipe_files[key] = recipe_data
            
                    return recipe_files
            
        except Exception as e:
            logger.error(f"MySQL获取工艺配方数据失败: {e}")
            return {}
    
    def _get_recipe_file_from_mysql_fixed(self) -> List[Dict]:
        """从MySQL获取配方文件数据（修复版本） - 修复筛选条件和字段获取 - 使用连接池"""
        try:
            if not self.mysql_available:
                return []
            
            # 🔧 修复：使用连接池而不是直接创建连接
            with self._get_db_connection_pooled() as conn:
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                
                # 修复查询：获取所有必要字段，移除错误的筛选条件
                query = """
                SELECT 
                    id,
                    RECIPE_FILE_NAME,
                    RECIPE_FILE_PATH,
                    PROD_TYPE,
                    EQP_TYPE,
                    HANDLER_CONFIG,
                    SIMP_RECIPE_FILE_PATH,
                    RECIPE_VER,
                    SUB_FAC,
                    KIT_PN,
                    SOCKET_PN,
                    FAMILY,
                    COORDINATE_ONE,
                    COORDINATE_TWO,
                    COORDINATE_THREE,
                    PROD_ID,
                    COMPANY_ID,
                    STAGE,
                    DEVICE,
                    CHIP_ID,
                    PKG_PN,
                    APPROVAL_STATE,
                    FAC_ID,
                    EDIT_STATE,
                    EDIT_TIME,
                    EDIT_USER,
                    EVENT,
                    EVENT_KEY,
                    EVENT_TIME,
                    EVENT_USER,
                    EVENT_MSG,
                    CREATE_TIME,
                    CREATE_USER
                FROM et_recipe_file 
                ORDER BY id
                """
                
                cursor.execute(query)
                results = cursor.fetchall()
                
                # 🔧 修复：连接池自动管理连接，无需手动关闭
                logger.info(f"📁 从MySQL获取到 {len(results)} 条配方文件数据（修复版：无筛选条件）")
                return results
            
        except Exception as e:
            logger.error(f"MySQL获取配方文件数据失败: {e}")
            return []
    
    def _get_equipment_status_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取设备状态数据 - 修复字段映射 - 使用连接池"""
        try:
            # 🔧 修复：使用连接池而不是直接创建连接
            with self._get_db_connection_pooled() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 查询所有数据，按ID排序确保一致性
                    cursor.execute("""
                    SELECT * FROM eqp_status 
                    WHERE id IS NOT NULL
                    ORDER BY id
                """)
                
                    equipment_status = {}
                    for row in cursor.fetchall():
                        # 使用ID作为主键确保唯一性（无论ID从何开始）
                        unique_key = str(row['id'])
                        
                        # 保留所有原始数据
                        equipment_status[unique_key] = dict(row)
                        
                        # 添加标准化字段供前端使用
                        equipment_status[unique_key].update({
                            'EQP_ID': str(row.get('DEVICE', '') or row.get('HANDLER_ID', '') or unique_key),
                            'EQP_NAME': str(row.get('HANDLER_TYPE', '') or row.get('DEVICE', '') or '未知设备'),
                            'EQP_STATUS': str(row.get('STATUS', 'UNKNOWN')),
                            'TESTER_ID': str(row.get('TESTER_ID', '')),
                            'HANDLER_ID': str(row.get('HANDLER_ID', '')),
                            'FAC_ID': str(row.get('FAC_ID', '')),
                            'UPDATE_TIME': row.get('updated_at', row.get('UPDATE_TIME')),
                            'available': str(row.get('STATUS', '')).upper() in ['RUN', 'IDLE', 'SETUP', 'READY', 'ACTIVE', 'ONLINE']
                        })
            
                    # 🔧 修复：连接池自动管理连接，无需手动关闭
                    logger.info(f"从MySQL获取到 {len(equipment_status)} 条设备状态数据")
                    return equipment_status
            
        except Exception as e:
            logger.error(f"MySQL获取设备状态数据失败: {e}")
            return {}
    
    def _get_uph_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取UPH数据 - 修复字段映射 - 使用连接池"""
        try:
            # 🔧 修复：使用连接池而不是直接创建连接
            with self._get_db_connection_pooled() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 直接查询所有字段
                    cursor.execute("SELECT * FROM et_uph_eqp WHERE id IS NOT NULL ORDER BY id")
                    
                    uph_data = {}
                    for i, row in enumerate(cursor.fetchall()):
                        # 使用组合键
                        device = str(row.get('DEVICE', '') or f'DEVICE_{i:03d}')
                        stage = str(row.get('STAGE', ''))
                        pkg_pn = str(row.get('PKG_PN', ''))
                        
                        keys = [
                            f"{device}|{stage}|{pkg_pn}" if pkg_pn else f"{device}|{stage}",
                            f"{device}|{stage}",
                            device
                        ]
                        
                        # 直接使用数据库返回的所有字段
                        uph_info = dict(row)
                        for key in keys:
                            if key not in uph_data:
                                uph_data[key] = uph_info
            
                    # 🔧 修复：连接池自动管理连接，无需手动关闭
                    logger.info(f"从MySQL获取到 {len(uph_data)} 条UPH数据")
                    return uph_data
            
        except Exception as e:
            logger.error(f"MySQL获取UPH数据失败: {e}")
            return {}
    
    def _get_priority_configs_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取优先级配置数据 - 使用连接池"""
        try:
            # 🔧 修复：使用连接池而不是直接创建连接
            with self._get_db_connection_pooled() as connection:
                device_priority = {}
                lot_priority = {}
                
                with connection.cursor() as cursor:
                    # 获取设备优先级配置
                    try:
                        cursor.execute("SELECT * FROM device_priority_config")
                        for row in cursor.fetchall():
                            if len(row) >= 2:  # 确保有足够的字段
                                device, stage = row[0] or '', row[1] or ''
                                keys = [f"{device}|{stage}", f"{device}"]
                                for key in keys:
                                    if key not in device_priority:
                                        device_priority[key] = {
                                            'DEVICE': device,
                                            'STAGE': stage,
                                            'PRIORITY': row[2] if len(row) > 2 else 1
                                        }
                    except Exception as e:
                        logger.warning(f"获取设备优先级配置失败: {e}")
                    
                    # 获取批次优先级配置
                    try:
                        cursor.execute("SELECT * FROM lot_priority_config")
                        for row in cursor.fetchall():
                            if len(row) >= 1:  # 确保有足够的字段
                                lot_id = row[0] or ''
                                if lot_id:
                                    lot_priority[lot_id] = {
                                        'LOT_ID': lot_id,
                                        'PRIORITY': row[1] if len(row) > 1 else 1
                                    }
                    except Exception as e:
                        logger.warning(f"获取批次优先级配置失败: {e}")
            
                # 🔧 修复：连接池自动管理连接，无需手动关闭
                return {'device': device_priority, 'lot': lot_priority}
            
        except Exception as e:
            logger.error(f"MySQL获取优先级配置失败: {e}")
            return {'device': {}, 'lot': {}}
    
    # ✅ Excel数据获取方法已全部移除 - 全面转向MySQL
    

    

    

    
    def _get_tcc_inv_data(self) -> List[Dict]:
        """从MySQL获取TCC硬件资源数据 - 🔥 修复表名错误，使用正确的TCC表"""
        try:
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 🎯 修复表名：使用正确的TCC表（不是TCC_INV），基于英文字段名
                    cursor.execute("""
                        SELECT 
                            Num as HARDWARE_CODE,
                            Quantity as QUANTITY,
                            LifeStatus as LIFE_STATUS,
                            WhsCode as WAREHOUSE,
                            BinCode as BIN_CODE,
                            LatestEquipmentCategory as EQUIPMENT_CATEGORY,
                            OwnerName as OWNER_NAME,
                            CurrentCycleLife as CURRENT_CYCLE_LIFE,
                            DefaultBinCode as DEFAULT_BIN_CODE,
                            Type as TYPE,
                            State as STATUS,
                            CreateTime as CREATED_AT,
                            UpdateTime as UPDATED_AT
                        FROM TCC 
                        WHERE Num IS NOT NULL AND Num != ''
                        ORDER BY Num
                    """)
                    
                    tcc_data = []
                    for row in cursor.fetchall():
                        # 🎯 标准化数据格式，统一字段映射
                        standardized_row = dict(row)
                        
                        # 🔧 添加兼容性映射，基于正确的TCC表字段结构
                        standardized_row.update({
                            'INVENTORY_ID': row.get('HARDWARE_CODE', ''),  # 使用Num作为库存ID
                            'INVENTORY_TYPE': row.get('TYPE', 'UNKNOWN'),   # Type字段
                            'INVENTORY_STATUS': row.get('STATUS', 'UNKNOWN'), # State字段
                            'LOCATION': row.get('BIN_CODE', row.get('DEFAULT_BIN_CODE', '')),  # BinCode或DefaultBinCode
                            'QUANTITY': float(row.get('QUANTITY', 0)) if row.get('QUANTITY') else 0,  # 数量
                            'EQUIPMENT_CATEGORY': row.get('EQUIPMENT_CATEGORY', ''),  # 设备类别
                            'WAREHOUSE': row.get('WAREHOUSE', ''),          # 仓库代码
                            'OWNER_NAME': row.get('OWNER_NAME', ''),        # 负责人
                            'LIFE_STATUS': row.get('LIFE_STATUS', ''),      # 寿命状态
                            'CURRENT_CYCLE_LIFE': int(row.get('CURRENT_CYCLE_LIFE', 0)) if row.get('CURRENT_CYCLE_LIFE') else 0,  # 当前周期
                            'CREATED_AT': row.get('CREATED_AT'),            # 创建时间
                            'UPDATED_AT': row.get('UPDATED_AT'),            # 更新时间
                        })
                        
                        tcc_data.append(standardized_row)
                
                logger.info(f"从MySQL获取到 {len(tcc_data)} 条TCC数据")
                return tcc_data
            
        except Exception as e:
            logger.error(f"MySQL获取TCC数据失败: {e}")
            return []
    
    def get_tcc_inventory_data(self, status_filter: str = None, location_filter: str = None) -> List[Dict]:
        """
        获取TCC库存数据，支持状态和位置过滤
        
        Args:
            status_filter: 状态过滤器，如'AVAILABLE', 'IN_USE', 'MAINTENANCE'
            location_filter: 位置过滤器，用于匹配特定位置
            
        Returns:
            List[Dict]: TCC资源数据列表，包含状态和位置信息索引
        """
        try:
            # 🎯 智能缓存：优先从缓存获取，避免频繁数据库查询
            cache_key = f"tcc_inventory_{status_filter}_{location_filter}"
            cached_data = None
            if hasattr(self, 'enhanced_cache_sync') and self.enhanced_cache_sync:
                try:
                    cached_data = self.enhanced_cache_sync.get(cache_key)
                    if cached_data:
                        logger.debug(f"从增强缓存获取TCC数据，key: {cache_key}")
                        return cached_data
                except Exception as cache_e:
                    logger.debug(f"缓存获取失败，直接查询数据库: {cache_e}")
            
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    # 🎯 修复表名：使用正确的TCC表，基于英文字段名构建动态SQL查询
                    base_query = """
                        SELECT 
                            Num as INVENTORY_ID,
                            Type as INVENTORY_TYPE,
                            State as INVENTORY_STATUS,
                            BinCode as LOCATION,
                            DefaultBinCode as DEFAULT_LOCATION,
                            Quantity as QUANTITY,
                            LatestEquipmentCategory as EQUIPMENT_CATEGORY,
                            WhsCode as WAREHOUSE,
                            OwnerName as OWNER_NAME,
                            LifeStatus as LIFE_STATUS,
                            CurrentCycleLife as CURRENT_CYCLE_LIFE,
                            CreateTime as CREATED_AT,
                            UpdateTime as UPDATED_AT
                        FROM TCC 
                        WHERE Num IS NOT NULL AND Num != ''
                    """
                    
                    query_params = []
                    if status_filter:
                        base_query += " AND State = %s"
                        query_params.append(status_filter)
                    
                    if location_filter:
                        base_query += " AND (BinCode LIKE %s OR DefaultBinCode LIKE %s)"
                        query_params.append(f"%{location_filter}%")
                        query_params.append(f"%{location_filter}%")
                    
                    base_query += " ORDER BY Num"
                    
                    cursor.execute(base_query, query_params)
                    
                    tcc_data = []
                    for row in cursor.fetchall():
                        row_dict = dict(row)
                        # 🚀 增加位置索引和状态解析，基于TCC表的英文状态值  
                        status = row_dict.get('INVENTORY_STATUS', '').strip()  # State字段
                        life_status = row_dict.get('LIFE_STATUS', '').strip()  # LifeStatus字段
                        quantity = float(row_dict.get('QUANTITY', 0)) if row_dict.get('QUANTITY') else 0
                        
                        # 基于TCC表的可用性判断：State='可用' AND 数量>0
                        row_dict['is_available'] = (
                            status in ['可用', '闲置', '正常', 'Available', 'IDLE', ''] and
                            quantity > 0 and
                            life_status not in ['报废', '维修', 'SCRAP', 'REPAIR']
                        )
                        row_dict['location_key'] = self._extract_location_key(row_dict.get('LOCATION', row_dict.get('DEFAULT_LOCATION', '')))
                        
                        # 🔧 添加TCC表特有字段，便于算法使用
                        row_dict.update({
                            'QUANTITY': quantity,
                            'EQUIPMENT_CATEGORY': row_dict.get('EQUIPMENT_CATEGORY', ''),
                            'OWNER_NAME': row_dict.get('OWNER_NAME', ''),
                            'CURRENT_CYCLE_LIFE': int(row_dict.get('CURRENT_CYCLE_LIFE', 0)) if row_dict.get('CURRENT_CYCLE_LIFE') else 0,
                        })
                        tcc_data.append(row_dict)
                
                # 🎯 缓存结果，5分钟过期
                if hasattr(self, 'enhanced_cache_sync') and self.enhanced_cache_sync:
                    try:
                        self.enhanced_cache_sync.set(cache_key, tcc_data, 300)
                    except Exception as cache_e:
                        logger.debug(f"缓存设置失败: {cache_e}")
                
                logger.info(f"获取TCC库存数据: {len(tcc_data)} 条记录 (状态:{status_filter}, 位置:{location_filter})")
                return tcc_data
                
        except Exception as e:
            logger.error(f"获取TCC库存数据失败: {e}")
            return []
    
    def get_available_tcc_resources(self, equipment_location: str = None) -> Dict[str, List[Dict]]:
        """
        获取可用的TCC资源，按类型和位置分组
        
        Args:
            equipment_location: 设备位置，用于位置匹配优化
            
        Returns:
            Dict: {resource_type: [resource_list]} 格式的可用资源
        """
        try:
            # 只获取可用状态的TCC资源
            available_resources = self.get_tcc_inventory_data(status_filter='AVAILABLE')
            if not available_resources:
                # 如果没有明确AVAILABLE状态的，获取所有非占用状态的
                all_resources = self.get_tcc_inventory_data()
                available_resources = [r for r in all_resources if r.get('is_available', True)]
            
            # 按资源类型分组
            resources_by_type = {}
            for resource in available_resources:
                resource_type = resource.get('INVENTORY_TYPE', 'UNKNOWN').upper()
                if resource_type not in resources_by_type:
                    resources_by_type[resource_type] = []
                
                # 🎯 位置匹配评分：如果提供了设备位置，计算匹配度
                if equipment_location:
                    resource['location_match_score'] = self._calculate_location_match_score(
                        equipment_location, resource.get('LOCATION', '')
                    )
                else:
                    resource['location_match_score'] = 50.0  # 默认中等匹配度
                
                resources_by_type[resource_type].append(resource)
            
            # 对每个类型内的资源按位置匹配度排序
            for resource_type in resources_by_type:
                resources_by_type[resource_type].sort(
                    key=lambda x: x.get('location_match_score', 0), 
                    reverse=True
                )
            
            logger.info(f"获取可用TCC资源: {sum(len(v) for v in resources_by_type.values())} 个资源，{len(resources_by_type)} 种类型")
            return resources_by_type
            
        except Exception as e:
            logger.error(f"获取可用TCC资源失败: {e}")
            return {}
    
    def _extract_location_key(self, location: str) -> str:
        """从位置字符串中提取关键位置标识"""
        if not location:
            return 'UNKNOWN'
        
        # 简单的位置关键字提取逻辑
        location_upper = location.upper()
        for key in ['LINE1', 'LINE2', 'LINE3', 'AREA_A', 'AREA_B', 'AREA_C', 'ZONE1', 'ZONE2']:
            if key in location_upper:
                return key
        
        # 如果没有匹配的关键字，取前10个字符作为位置键
        return location_upper[:10] if len(location_upper) > 10 else location_upper
    
    def _calculate_location_match_score(self, equipment_location: str, resource_location: str) -> float:
        """
        计算设备位置与TCC资源位置的匹配度
        
        Returns:
            float: 匹配度分数 (0-100)，分数越高表示位置越匹配
        """
        if not equipment_location or not resource_location:
            return 30.0  # 默认低匹配度
        
        eq_loc = equipment_location.upper()
        res_loc = resource_location.upper()
        
        # 完全匹配
        if eq_loc == res_loc:
            return 100.0
        
        # 包含关系匹配
        if eq_loc in res_loc or res_loc in eq_loc:
            return 80.0
        
        # 关键字匹配
        eq_key = self._extract_location_key(eq_loc)
        res_key = self._extract_location_key(res_loc)
        if eq_key == res_key and eq_key != 'UNKNOWN':
            return 70.0
        
        # 部分匹配（前3个字符相同）
        if len(eq_loc) >= 3 and len(res_loc) >= 3 and eq_loc[:3] == res_loc[:3]:
            return 50.0
        
        # 无明显匹配
        return 20.0
    
    def _get_ct_from_mysql(self) -> List[Dict]:
        """从MySQL获取产品周期数据 - 用于缓存系统"""
        try:
            from sqlalchemy import text
            
            # 使用Flask-SQLAlchemy执行查询
            result = db.session.execute(text("""
                SELECT LOT_ID, DEVICE, STAGE, AUXILIARY_EQP_ID, CREATE_TIME,
                       FIRST_PASS_YIELD, FINAL_YIELD, GOOD_QTY, REJECT_QTY, LOSS_QTY,
                       LOT_START_TIME, LOT_END_TIME, SETUP_TIME, COST_TIME,
                       FAC_ID, WORK_ORDER_ID, MAIN_EQP_ID
                FROM ct
                WHERE LOT_ID IS NOT NULL AND LOT_ID != ''
                ORDER BY CREATE_TIME DESC
                LIMIT 50000
            """))
            
            ct_data = [dict(row) for row in result.mappings()]
            
            # 数据清洗
            for record in ct_data:
                # 标准化时间字段
                for time_field in ['CREATE_TIME', 'LOT_START_TIME', 'LOT_END_TIME']:
                    if record.get(time_field):
                        record[time_field] = self._normalize_datetime_field(record[time_field])
                
                # 标准化数值字段
                for num_field in ['GOOD_QTY', 'REJECT_QTY', 'LOSS_QTY']:
                    if record.get(num_field) is not None:
                        try:
                            record[num_field] = int(float(record[num_field]))
                        except (ValueError, TypeError):
                            record[num_field] = 0
                
                # 标准化良率字段
                for yield_field in ['FIRST_PASS_YIELD', 'FINAL_YIELD']:
                    if record.get(yield_field) is not None:
                        try:
                            yield_val = float(record[yield_field])
                            # 确保良率在0-1之间
                            if yield_val > 1:
                                yield_val = yield_val / 100
                            record[yield_field] = yield_val
                        except (ValueError, TypeError):
                            record[yield_field] = None
            
            logger.info(f"📊 从MySQL获取到 {len(ct_data)} 条CT历史数据")
            return ct_data
            
        except Exception as e:
            logger.error(f"❌ MySQL获取CT数据失败: {e}")
            return []
    
    def _get_lotprioritydone_from_mysql(self) -> List[Dict]:
        """从MySQL获取已排产批次数据 - 用于缓存系统"""
        try:
            from sqlalchemy import text
            
            # 使用Flask-SQLAlchemy执行查询
            result = db.session.execute(text("""
                SELECT id, PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                       PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE, STEP,
                       WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                       RELEASE_TIME, FAC_ID, CREATE_TIME,
                       comprehensive_score, processing_time, changeover_time,
                       algorithm_version, match_type, priority_score
                FROM lotprioritydone
                WHERE LOT_ID IS NOT NULL AND LOT_ID != ''
                ORDER BY CREATE_TIME DESC, PRIORITY ASC
            """))
            
            done_data = [dict(row) for row in result.mappings()]
            
            # 数据清洗
            for record in done_data:
                # 标准化时间字段
                for time_field in ['CREATE_TIME', 'RELEASE_TIME']:
                    if record.get(time_field):
                        record[time_field] = self._normalize_datetime_field(record[time_field])
                
                # 标准化数值字段
                for num_field in ['GOOD_QTY', 'PRIORITY', 'HOLD_STATE']:
                    if record.get(num_field) is not None:
                        try:
                            record[num_field] = int(float(record[num_field]))
                        except (ValueError, TypeError):
                            record[num_field] = 0
                
                # 标准化评分字段
                for score_field in ['comprehensive_score', 'processing_time', 'changeover_time', 'priority_score']:
                    if record.get(score_field) is not None:
                        try:
                            record[score_field] = float(record[score_field])
                        except (ValueError, TypeError):
                            record[score_field] = 0.0
                
                # 确保字符串字段不为None
                for str_field in ['HANDLER_ID', 'LOT_ID', 'LOT_TYPE', 'DEVICE', 'STAGE', 'STEP', 
                                'WIP_STATE', 'PROC_STATE', 'algorithm_version', 'match_type']:
                    if record.get(str_field) is None:
                        record[str_field] = ''
            
            logger.info(f"✅ 从MySQL获取到 {len(done_data)} 条已排产数据")
            return done_data
            
        except Exception as e:
            logger.error(f"❌ MySQL获取已排产数据失败: {e}")
            return []
    
    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态信息 - 仅MySQL源"""
        return {
            'mysql_available': self.mysql_available,
            'current_source': 'MySQL' if self.mysql_available else 'None',
            'recommended_source': 'MySQL' if self.mysql_available else 'None'
        }
    
    def get_table_data(self, table_name: str, page: int = 1, per_page: int = None, filters: List = None) -> Dict:
        """获取表格数据（API v2兼容方法）"""
        try:
            # 根据table_name路由到对应的方法
            if table_name in ['wait_lot', 'ET_WAIT_LOT', 'et_wait_lot', 'wip_lot']:
                data, source = self.get_wait_lot_data()
                columns = ['LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PKG_PN', 'CHIP_ID', 'CREATE_TIME'] if data else []
            elif table_name in ['eqp_status', 'EQP_STATUS']:
                data_dict, source = self.get_equipment_status_data()
                data = list(data_dict.values()) if data_dict else []
                # 使用实际存在的字段作为列名
                if data:
                    # 从第一条数据获取可用的列
                    first_record = data[0]
                    # 排除调试字段，只显示业务字段
                    columns = [k for k in first_record.keys() if k not in ['available', 'raw_data']]
                else:
                    columns = ['EQP_ID', 'EQP_NAME', 'EQP_STATUS', 'TESTER_ID', 'HANDLER_ID', 'FAC_ID', 'UPDATE_TIME']
            elif table_name in ['ET_UPH_EQP', 'et_uph_eqp']:
                data_dict, source = self.get_uph_data()
                data = list(data_dict.values()) if data_dict else []
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['DEVICE', 'STAGE', 'PKG_PN', 'UPH', 'SORTER_MODEL', 'FAC_ID']
            elif table_name in ['et_ft_test_spec', 'ET_FT_TEST_SPEC']:
                data_dict, source = self.get_test_spec_data()
                data = list(data_dict.values()) if data_dict else []
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['DEVICE', 'STAGE', 'PKG_PN', 'CHIP_ID', 'TEST_SPEC', 'TEST_TIME']
            elif table_name in ['et_recipe_file', 'ET_RECIPE_FILE']:
                # 配方文件表 - 直接从MySQL获取
                data = self._get_recipe_file_from_mysql_fixed()
                source = "MySQL" if self.mysql_available else "Excel"
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['DEVICE', 'STAGE', 'PKG_PN', 'KIT_PN', 'SOCKET_PN', 'RECIPE_FILE_NAME']
            elif table_name in ['lotprioritydone', 'LOTPRIORITYDONE']:
                # 已排产批次表 - 使用缓存获取
                data, source = self.get_lotprioritydone_data()
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['id', 'PRIORITY', 'HANDLER_ID', 'LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'CREATE_TIME']
            elif table_name in ['ct', 'CT']:
                # CT生产历史表 - 使用缓存获取
                data, source = self.get_ct_data()
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['LOT_ID', 'DEVICE', 'STAGE', 'AUXILIARY_EQP_ID', 'CREATE_TIME', 'FIRST_PASS_YIELD', 'FINAL_YIELD']
            elif table_name in ['TCC_INV', 'tcc_inv']:
                # 套件资源表 - 从MySQL获取实际数据
                data = self._get_tcc_inv_data()
                source = "MySQL" if self.mysql_available else "Excel" 
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['INVENTORY_ID', 'INVENTORY_TYPE', 'INVENTORY_STATUS', 'LOCATION', 'REMARK']
            elif table_name in ['CT', 'ct']:
                # 产品周期表 - 从MySQL获取实际数据
                data = self._get_ct_data()
                source = "MySQL" if self.mysql_available else "Excel"
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['PRODUCT', 'STAGE', 'CT_VALUE', 'UNIT', 'REMARK']
            elif table_name in ['devicepriorityconfig']:
                # 产品优先级配置表
                data = self._get_device_priority_data()
                source = "MySQL" if self.mysql_available else "Excel"
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['ID', 'DEVICE', 'PRIORITY', 'FROM_TIME', 'END_TIME', 'REFRESH_TIME', 'USER']
            elif table_name in ['lotpriorityconfig']:
                # 批次优先级配置表
                data = self._get_lot_priority_data()
                source = "MySQL" if self.mysql_available else "Excel"
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['ID', 'DEVICE', 'STAGE', 'PRIORITY', 'REFRESH_TIME', 'USER']
            elif table_name in ['stage_mapping_config', 'stageMappingConfig']:
                # 🔗 新增：STAGE映射配置表
                data = self._get_stage_mapping_config_data()
                source = "MySQL" if self.mysql_available else "None"
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['id', 'source_stage', 'target_stage', 'mapping_type', 'priority', 'is_active', 'description', 'created_at', 'updated_at']
            elif table_name in ['lotprioritydone']:
                # 已排产批次由专用API处理，避免冲突
                logger.info(f"⚠️  {table_name} 数据由专用API处理，跳过DataSourceManager")
                return {
                    'success': True,
                    'data': [],
                    'columns': [],
                    'total': 0,
                    'pages': 0,
                    'data_source': 'Dedicated API',
                    'timestamp': datetime.now().isoformat(),
                    'note': '此表由专用API处理，请使用 /api/v2/production/done-lots 端点'
                }
            else:
                return {
                    'success': False,
                    'error': f'不支持的表格: {table_name}'
                }
            
            # 应用筛选条件
            if filters and len(filters) > 0:
                data = self._apply_filters(data, filters)
                logger.info(f"应用筛选条件后，数据从原始条数筛选到 {len(data)} 条")
            
            # 记录总数（筛选后）
            total = len(data)
            
            # 应用分页（如果指定了per_page）
            if per_page is not None:
                start_idx = (page - 1) * per_page
                end_idx = start_idx + per_page
                paged_data = data[start_idx:end_idx]
            else:
                paged_data = data
            
            logger.info(f"表格 {table_name}: 总计 {total} 条记录, 显示 {len(paged_data)} 条")
            
            return {
                'success': True,
                'data': paged_data,
                'columns': columns,
                'total': total,
                'pages': (total + per_page - 1) // per_page if per_page else 1,
                'data_source': source,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"获取表格数据失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_table_columns(self, table_name: str) -> Dict:
        """获取表格列信息（API v2兼容方法）- 已修复字段映射与MySQL数据库完全一致"""
        try:
            # 🔧 修复后的列映射 - 与MySQL数据库字段完全对应
            column_mapping = {
                # ✅ 修复1：待排产批次表 - 添加缺失的STEP字段，修复字段错位问题
                'wait_lot': ['id', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STEP', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME', 'created_at', 'updated_at'],
                'ET_WAIT_LOT': ['id', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STEP', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME', 'created_at', 'updated_at'],
                'et_wait_lot': ['id', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STEP', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME', 'created_at', 'updated_at'],
                
                # 🚨 修复2：在制品批次表 - 完整的147个字段（含id字段）
                'wip_lot': ['id', 'LOT_ID', 'LOT_TYPE', 'DET_LOT_TYPE', 'LOT_QTY', 'SUB_QTY', 'UNIT', 'SUB_UNIT', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'RW_STATE', 'REPAIR_STATE', 'QC_STATE', 'PROD_ID', 'PROC_RULE_ID', 'PRP_ID', 'FLOW_ID', 'STAGE', 'PRP_VER', 'FLOW_VER', 'OPER_VER', 'CARR_ID', 'USE_SUB_LOT', 'AREA_ID', 'LOC_ID', 'EQP_ID', 'SUB_EQP_ID', 'PORT_ID', 'RECIPE_ID', 'SUB_RECIPE_ID', 'MARK_ID', 'LOT_IN_QTY', 'LOT_OUT_QTY', 'GOOD_QTY', 'NG_QTY', 'PREV_PROD_ID', 'PREV_PROC_RULE_ID', 'PREV_PRP_ID', 'PREV_PRP_VER', 'PREV_FLOW_ID', 'PREV_FLOW_VER', 'PREV_OPER_ID', 'PREV_OPER_VER', 'PREV_EQP_ID', 'PREV_PORT_ID', 'PREV_RECIPE_ID', 'PREV_SUB_RECIPE_ID', 'RTCL_ID', 'BATCH_ID', 'LAST_BATCH_ID', 'CTM_ID', 'LOT_GRP_ID', 'RESV_EQP_ID', 'HOT_TYPE', 'SEND_COMPANY_ID', 'OPER_CHANGE_TIME', 'JOB_START_TIME', 'JOB_END_TIME', 'PLAN_START_DATE', 'PLAN_DUE_DATE', 'GRADE', 'REASON_GRP', 'REASON_CODE', 'FR_RW_PROC_RULE_ID', 'FR_RW_PRP_ID', 'FR_RW_PRP_VER', 'FR_RW_FLOW_ID', 'FR_RW_FLOW_VER', 'FR_RW_OPER_ID', 'FR_RW_OPER_VER', 'RW_RT_PROC_RULE_ID', 'RW_RT_PRP_ID', 'RW_RT_PRP_VER', 'RW_RT_FLOW_ID', 'RW_RT_FLOW_VER', 'RW_RT_OPER_ID', 'RW_RT_OPER_VER', 'PILOT_TYPE', 'MERGE_OPER_ID', 'ACT_NM', 'LOT_JUDGE', 'FAC_ID', 'SUB_FAC', 'ROOT_LOT_ID', 'PARENT_LOT_ID', 'CHILD_LOT_ID', 'LOT_OBJ_ID', 'CUST_LOT_ID', 'WORK_ORDER_ID', 'WORK_ORDER_VER', 'BOM_ID', 'BOM_VER', 'PO_ID', 'LOT_OWNER', 'PKG_PN', 'RELEASE_TIME', 'SHIP_TIME', 'SHIP_ORDER_ID', 'SHIP_FAC_ID', 'CREATE_LOT_QTY', 'CREATE_SUB_QTY', 'ROOT_LOT_QTY', 'TRACK_CARD_ID', 'DBP_ID', 'CJOB_ID', 'PROC_CNT', 'RETEST_YN', 'DUT_ID', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'STR_FLAG', 'MAIN_EQP_ID', 'AUXILIARY_EQP_ID', 'CONTAINER_ID', 'CHIP_ID', 'ACT_QTY', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'ORT_QTY', 'OA_FLAG', 'DEVICE', 'WAREHOUSE_CONTAINER_ID', 'PROD_THICKNESS', 'IQC_QTY', 'UPH', 'SEAL_FLAG', 'PACK_SPEC_ID', 'PACK_SPEC_VER', 'FULL_INSP_QC', 'SPLIT_TYPE', 'WH_LOCATION_NO', 'RELEASE_HOLD_TYPE', 'DATA_CONFIRM_HOLD_YN', 'ORT_SAMP_QTY', 'IQC_SAMP_QTY', 'LOCATION', 'RETEST_FLOW_ID', 'HALF_LOT_HOLD', 'MERGE_LOT_ID', 'STRM_QTY', 'STRM_SAMP_QTY'],
                'WIP_LOT': ['id', 'LOT_ID', 'LOT_TYPE', 'DET_LOT_TYPE', 'LOT_QTY', 'SUB_QTY', 'UNIT', 'SUB_UNIT', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'RW_STATE', 'REPAIR_STATE', 'QC_STATE', 'PROD_ID', 'PROC_RULE_ID', 'PRP_ID', 'FLOW_ID', 'STAGE', 'PRP_VER', 'FLOW_VER', 'OPER_VER', 'CARR_ID', 'USE_SUB_LOT', 'AREA_ID', 'LOC_ID', 'EQP_ID', 'SUB_EQP_ID', 'PORT_ID', 'RECIPE_ID', 'SUB_RECIPE_ID', 'MARK_ID', 'LOT_IN_QTY', 'LOT_OUT_QTY', 'GOOD_QTY', 'NG_QTY', 'PREV_PROD_ID', 'PREV_PROC_RULE_ID', 'PREV_PRP_ID', 'PREV_PRP_VER', 'PREV_FLOW_ID', 'PREV_FLOW_VER', 'PREV_OPER_ID', 'PREV_OPER_VER', 'PREV_EQP_ID', 'PREV_PORT_ID', 'PREV_RECIPE_ID', 'PREV_SUB_RECIPE_ID', 'RTCL_ID', 'BATCH_ID', 'LAST_BATCH_ID', 'CTM_ID', 'LOT_GRP_ID', 'RESV_EQP_ID', 'HOT_TYPE', 'SEND_COMPANY_ID', 'OPER_CHANGE_TIME', 'JOB_START_TIME', 'JOB_END_TIME', 'PLAN_START_DATE', 'PLAN_DUE_DATE', 'GRADE', 'REASON_GRP', 'REASON_CODE', 'FR_RW_PROC_RULE_ID', 'FR_RW_PRP_ID', 'FR_RW_PRP_VER', 'FR_RW_FLOW_ID', 'FR_RW_FLOW_VER', 'FR_RW_OPER_ID', 'FR_RW_OPER_VER', 'RW_RT_PROC_RULE_ID', 'RW_RT_PRP_ID', 'RW_RT_PRP_VER', 'RW_RT_FLOW_ID', 'RW_RT_FLOW_VER', 'RW_RT_OPER_ID', 'RW_RT_OPER_VER', 'PILOT_TYPE', 'MERGE_OPER_ID', 'ACT_NM', 'LOT_JUDGE', 'FAC_ID', 'SUB_FAC', 'ROOT_LOT_ID', 'PARENT_LOT_ID', 'CHILD_LOT_ID', 'LOT_OBJ_ID', 'CUST_LOT_ID', 'WORK_ORDER_ID', 'WORK_ORDER_VER', 'BOM_ID', 'BOM_VER', 'PO_ID', 'LOT_OWNER', 'PKG_PN', 'RELEASE_TIME', 'SHIP_TIME', 'SHIP_ORDER_ID', 'SHIP_FAC_ID', 'CREATE_LOT_QTY', 'CREATE_SUB_QTY', 'ROOT_LOT_QTY', 'TRACK_CARD_ID', 'DBP_ID', 'CJOB_ID', 'PROC_CNT', 'RETEST_YN', 'DUT_ID', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'STR_FLAG', 'MAIN_EQP_ID', 'AUXILIARY_EQP_ID', 'CONTAINER_ID', 'CHIP_ID', 'ACT_QTY', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'ORT_QTY', 'OA_FLAG', 'DEVICE', 'WAREHOUSE_CONTAINER_ID', 'PROD_THICKNESS', 'IQC_QTY', 'UPH', 'SEAL_FLAG', 'PACK_SPEC_ID', 'PACK_SPEC_VER', 'FULL_INSP_QC', 'SPLIT_TYPE', 'WH_LOCATION_NO', 'RELEASE_HOLD_TYPE', 'DATA_CONFIRM_HOLD_YN', 'ORT_SAMP_QTY', 'IQC_SAMP_QTY', 'LOCATION', 'RETEST_FLOW_ID', 'HALF_LOT_HOLD', 'MERGE_LOT_ID', 'STRM_QTY', 'STRM_SAMP_QTY'],
                
                # 🚨 修复3：设备状态表 - 修正为实际的18个字段（含id字段）
                'eqp_status': ['id', 'HANDLER_ID', 'HANDLER_TYPE', 'TESTER_ID', 'HANDLER_CONFIG', 'SOCKET_PN', 'KIT_PN', 'EQP_CLASS', 'EQP_TYPE', 'TEMPERATURE_RANGE', 'TEMPERATURE_CAPACITY', 'LOT_ID', 'DEVICE', 'STATUS', 'HB_PN', 'TB_PN', 'TESTER_CONFIG', 'STAGE', 'EVENT_TIME', 'created_at', 'updated_at'],
                'EQP_STATUS': ['id', 'HANDLER_ID', 'HANDLER_TYPE', 'TESTER_ID', 'HANDLER_CONFIG', 'SOCKET_PN', 'KIT_PN', 'EQP_CLASS', 'EQP_TYPE', 'TEMPERATURE_RANGE', 'TEMPERATURE_CAPACITY', 'LOT_ID', 'DEVICE', 'STATUS', 'HB_PN', 'TB_PN', 'TESTER_CONFIG', 'STAGE', 'EVENT_TIME', 'created_at', 'updated_at'],
                
                # 🚨 修复4：UPH设备表 - 修正为实际的16个字段（含id字段）
                'ET_UPH_EQP': ['id', 'DEVICE', 'PKG_PN', 'STAGE', 'UPH', 'HANDLER', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'created_at', 'updated_at'],
                'et_uph_eqp': ['id', 'DEVICE', 'PKG_PN', 'STAGE', 'UPH', 'HANDLER', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'created_at', 'updated_at'],
                
                # 🚨 修复5：测试规格表 - 扩展至完整的72个字段（含id字段）
                'et_ft_test_spec': ['id', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'STAGE', 'TESTER', 'INV_ID', 'TEST_SPEC_TYPE', 'APPROVAL_STATE', 'ACTV_YN', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'COMPANY_ID', 'DISABLE_USER', 'DISABLE_REASON', 'DISABLE_TIME', 'NOTE', 'APPROVE_USER', 'APPROVE_TIME', 'ORT_QTY', 'REMAIN_QTY', 'STANDARD_YIELD', 'LOW_YIELD', 'DOWN_YIELD', 'TEST_AREA', 'HANDLER', 'TEMPERATURE', 'FT_PROGRAM', 'QA_PROGRAM', 'GU_PROGRAM', 'TB_PN', 'HB_PN', 'TIB', 'TEST_TIME', 'UPH', 'SUFFIX_CODE', 'TESTER_CONFIG', 'GU_COMPARE_PARAM', 'STA_COMPARE_PARAM', 'DNR', 'SITE', 'DPAT', 'BS_NAME', 'GU_NAME', 'C_SPEC', 'TEST_ENG', 'TEST_OPERATION', 'ORDER_COMMENT', 'HIGH_YIELD', 'VISION_LOSS_YIELD', 'VISION_YIELD', 'LOSS_YIELD', 'RETEST_YN', 'FT_PROGRAM_PATH', 'QA_PROGRAM_PATH', 'GU_PROGRAM_PATH', 'EFFECTIVE_TIME', 'TPL_RULE_TEMP', 'TPL_RULE_TEMP_PATH', 'ALARM_DATE', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER'],
                'ET_FT_TEST_SPEC': ['id', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'STAGE', 'TESTER', 'INV_ID', 'TEST_SPEC_TYPE', 'APPROVAL_STATE', 'ACTV_YN', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'COMPANY_ID', 'DISABLE_USER', 'DISABLE_REASON', 'DISABLE_TIME', 'NOTE', 'APPROVE_USER', 'APPROVE_TIME', 'ORT_QTY', 'REMAIN_QTY', 'STANDARD_YIELD', 'LOW_YIELD', 'DOWN_YIELD', 'TEST_AREA', 'HANDLER', 'TEMPERATURE', 'FT_PROGRAM', 'QA_PROGRAM', 'GU_PROGRAM', 'TB_PN', 'HB_PN', 'TIB', 'TEST_TIME', 'UPH', 'SUFFIX_CODE', 'TESTER_CONFIG', 'GU_COMPARE_PARAM', 'STA_COMPARE_PARAM', 'DNR', 'SITE', 'DPAT', 'BS_NAME', 'GU_NAME', 'C_SPEC', 'TEST_ENG', 'TEST_OPERATION', 'ORDER_COMMENT', 'HIGH_YIELD', 'VISION_LOSS_YIELD', 'VISION_YIELD', 'LOSS_YIELD', 'RETEST_YN', 'FT_PROGRAM_PATH', 'QA_PROGRAM_PATH', 'GU_PROGRAM_PATH', 'EFFECTIVE_TIME', 'TPL_RULE_TEMP', 'TPL_RULE_TEMP_PATH', 'ALARM_DATE', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER'],
                
                # 🚨 修复6：库存管理表 - 修正为实际中文字段名（含id字段）
                'TCC_INV': ['id', 'UNNAMED__0', '硬件编码', '关键硬件', '图片', '寿命状态', '仓库', '初始库位', '当前储位1', '当前储位2', '责任人', '周期消耗数', '当前库位', '封装形式', '状态', '类别', '设备机型', '寄放方', '备注_状态_SHIPOUT信息_', '类型', '状态_1', '操作', 'data_source', 'source_priority', 'last_sync_time', 'sync_status', 'mysql_hash', 'excel_override'],
                'tcc_inv': ['id', 'UNNAMED__0', '硬件编码', '关键硬件', '图片', '寿命状态', '仓库', '初始库位', '当前储位1', '当前储位2', '责任人', '周期消耗数', '当前库位', '封装形式', '状态', '类别', '设备机型', '寄放方', '备注_状态_SHIPOUT信息_', '类型', '状态_1', '操作', 'data_source', 'source_priority', 'last_sync_time', 'sync_status', 'mysql_hash', 'excel_override'],
                
                # 🚨 修复7：CT表 - 修正为实际的34个字段（含id字段）
                'CT': ['id', 'LOT_ID', 'WORK_ORDER_ID', 'PROD_ID', 'DEVICE', 'PKG_PN', 'CHIP_ID', 'FLOW_ID', 'STAGE', 'LOT_QTY', 'ACT_QTY', 'GOOD_QTY', 'REJECT_QTY', 'LOSS_QTY', 'MAIN_EQP_ID', 'AUXILIARY_EQP_ID', 'LOT_START_TIME', 'LOT_END_TIME', 'SETUP_TIME', 'FT_TEST_PROGRAM', 'IS_HALF_LOT_DOWN', 'FIRST_PASS_YIELD', 'FINAL_YIELD', 'VM_QTY', 'ALARM_BIN', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'FAC_ID', 'TRACK_CNT', 'COST_TIME', 'created_at', 'updated_at'],
                'ct': ['id', 'LOT_ID', 'WORK_ORDER_ID', 'PROD_ID', 'DEVICE', 'PKG_PN', 'CHIP_ID', 'FLOW_ID', 'STAGE', 'LOT_QTY', 'ACT_QTY', 'GOOD_QTY', 'REJECT_QTY', 'LOSS_QTY', 'MAIN_EQP_ID', 'AUXILIARY_EQP_ID', 'LOT_START_TIME', 'LOT_END_TIME', 'SETUP_TIME', 'FT_TEST_PROGRAM', 'IS_HALF_LOT_DOWN', 'FIRST_PASS_YIELD', 'FINAL_YIELD', 'VM_QTY', 'ALARM_BIN', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'FAC_ID', 'TRACK_CNT', 'COST_TIME', 'created_at', 'updated_at'],
                
                # 🚨 修复8：优先级配置表 - 使用Excel匹配的大写字段名
                'devicepriorityconfig': ['ID', 'DEVICE', 'PRIORITY', 'FROM_TIME', 'END_TIME', 'REFRESH_TIME', 'USER', 'created_at', 'updated_at'],
                'device_priority_config': ['ID', 'DEVICE', 'PRIORITY', 'FROM_TIME', 'END_TIME', 'REFRESH_TIME', 'USER', 'created_at', 'updated_at'],

                'lotpriorityconfig': ['ID', 'DEVICE', 'STAGE', 'PRIORITY', 'REFRESH_TIME', 'USER', 'created_at', 'updated_at'],
                'lot_priority_config': ['ID', 'DEVICE', 'STAGE', 'PRIORITY', 'REFRESH_TIME', 'USER', 'created_at', 'updated_at'],
                
                # 🚨 修复9：已排产批次表 - 修正为实际的字段（含id字段，移除已删除的SCHDULED_TIME字段，增加智能排产详情字段）
                'lotprioritydone': ['id', 'PRIORITY', 'HANDLER_ID', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STAGE', 'STEP', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME', 'match_type', 'comprehensive_score', 'processing_time', 'changeover_time', 'algorithm_version'],
                'lot_priority_done': ['id', 'PRIORITY', 'HANDLER_ID', 'LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'PO_ID', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER', 'RELEASE_TIME', 'FAC_ID', 'CREATE_TIME'],
                
                # 🚨 修复10：配方文件表 - 修正为实际的32个字段（含id字段）
                'ET_RECIPE_FILE': ['id', 'PROD_ID', 'COMPANY_ID', 'STAGE', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'RECIPE_FILE_NAME', 'RECIPE_FILE_PATH', 'APPROVAL_STATE', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'PROD_TYPE', 'EQP_TYPE', 'HANDLER_CONFIG', 'SIMP_RECIPE_FILE_PATH', 'RECIPE_VER', 'SUB_FAC', 'KIT_PN', 'SOCKET_PN', 'FAMILY', 'COORDINATE_ONE', 'COORDINATE_TWO', 'COORDINATE_THREE'],
                'recipe_file': ['id', 'PROD_ID', 'COMPANY_ID', 'STAGE', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'RECIPE_FILE_NAME', 'RECIPE_FILE_PATH', 'APPROVAL_STATE', 'FAC_ID', 'EDIT_STATE', 'EDIT_TIME', 'EDIT_USER', 'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG', 'CREATE_TIME', 'CREATE_USER', 'PROD_TYPE', 'EQP_TYPE', 'HANDLER_CONFIG', 'SIMP_RECIPE_FILE_PATH', 'RECIPE_VER', 'SUB_FAC', 'KIT_PN', 'SOCKET_PN', 'FAMILY', 'COORDINATE_ONE', 'COORDINATE_TWO', 'COORDINATE_THREE'],
                
                # 兼容性映射（保持向后兼容）
                'test_spec': ['TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER', 'STAGE', 'TESTER', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'TEST_TIME', 'UPH']
            }
            
            columns = column_mapping.get(table_name, [])
            
            # 🔍 字段验证和修复日志
            if columns:
                logger.info(f"✅ 字段映射已修复 - 表:{table_name}, 字段数:{len(columns)}, 数据源:{'MySQL' if self.mysql_available else 'Excel'}")
            else:
                logger.warning(f"⚠️ 未找到字段映射 - 表:{table_name}, 请检查配置")
            
            return {
                'success': True,
                'columns': columns,
                'data_source': 'MySQL' if self.mysql_available else 'Excel',
                'field_count': len(columns),
                'mapping_status': 'fixed' if columns else 'missing'
            }
        except Exception as e:
            logger.error(f"❌ 获取字段映射失败 - 表:{table_name}, 错误:{str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_field_mapping(self, table_name: str) -> Dict:
        """🔍 验证字段映射与数据库结构的一致性"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法验证字段映射'
                }
            
            # 获取代码定义的字段
            code_result = self.get_table_columns(table_name)
            if not code_result['success']:
                return code_result
                
            code_columns = set(code_result['columns'])
            
            # 获取数据库实际字段 - 🔥 修复连接泄漏
            database = 'aps'
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps'  # 已迁移到aps数据库
            
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    cursor.execute(f"DESCRIBE {table_name}")
                    db_columns = set([row[0] for row in cursor.fetchall() if row[0] not in ['id', 'created_at', 'updated_at']])
            
            # 比较字段
            missing_in_code = db_columns - code_columns
            extra_in_code = code_columns - db_columns
            common_fields = code_columns & db_columns
            
            match_rate = len(common_fields) / len(db_columns) * 100 if db_columns else 0
            
            validation_result = {
                'success': True,
                'table_name': table_name,
                'database': database,
                'code_fields_count': len(code_columns),
                'db_fields_count': len(db_columns),
                'common_fields_count': len(common_fields),
                'match_rate': round(match_rate, 2),
                'missing_in_code': list(missing_in_code),
                'extra_in_code': list(extra_in_code),
                'status': 'perfect' if match_rate == 100 else 'partial' if match_rate > 80 else 'poor'
            }
            
            # 记录验证结果
            if match_rate == 100:
                logger.info(f"🎉 字段映射完美匹配 - 表:{table_name}, 匹配率:{match_rate}%")
            elif match_rate > 80:
                logger.warning(f"⚠️ 字段映射部分匹配 - 表:{table_name}, 匹配率:{match_rate}%, 缺失:{len(missing_in_code)}个")
            else:
                logger.error(f"❌ 字段映射严重不匹配 - 表:{table_name}, 匹配率:{match_rate}%")
                
            return validation_result
            
        except Exception as e:
            logger.error(f"❌ 字段映射验证失败 - 表:{table_name}, 错误:{str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_available_tables(self) -> Dict:
        """获取可用表格列表（API v2兼容方法）"""
        try:
            tables = [
                'wait_lot', 'ET_WAIT_LOT', 'test_spec', 'recipe_file', 
                'equipment_status', 'uph_data', 'priority_config'
            ]
            return {
                'success': True,
                'tables': tables,
                'data_source': 'MySQL' if self.mysql_available else 'Excel'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_table_data(self, table_name: str, format_type: str = 'excel', filters: List = None) -> Dict:
        """导出表格数据（API v2兼容方法）"""
        try:
            import os
            import pandas as pd
            from flask import current_app
            
            # 获取完整的表格数据（不分页）
            data_result = self.get_table_data(table_name, page=1, per_page=10000, filters=filters)
            
            if not data_result['success']:
                return {
                    'success': False,
                    'error': f'获取数据失败: {data_result.get("error", "未知错误")}'
                }
            
            data = data_result['data']
            columns = data_result['columns']
            total_records = data_result['total']
            
            if not data:
                return {
                    'success': False,
                    'error': '没有数据可以导出'
                }
            
            # 创建导出目录
            export_dir = os.path.join(current_app.root_path, 'static', 'exports')
            os.makedirs(export_dir, exist_ok=True)
            
            # ✅ 修复：统一文件命名规范
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            display_name = self._get_table_display_name(table_name)
            filename = f'{display_name}_导出_{timestamp}.xlsx'
            filepath = os.path.join(export_dir, filename)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 确保列顺序正确
            if columns:
                # 只保留存在的列
                available_columns = [col for col in columns if col in df.columns]
                if available_columns:
                    df = df[available_columns]
            
            # 导出到Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=table_name, index=False)
                
                # 获取工作表对象并设置列宽
                worksheet = writer.sheets[table_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 生成下载URL
            export_url = f'/static/exports/{filename}'
            
            logger.info(f"成功导出表格 {table_name}，记录数: {total_records}，文件: {filename}")
            
            return {
                'success': True,
                'export_url': export_url,
                'filename': filename,
                'records_count': total_records,
                'format': format_type,
                'table_name': table_name
            }
            
        except Exception as e:
            logger.error(f"导出表格数据失败: {e}")
            return {
                'success': False,
                'error': f'导出失败: {str(e)}'
            }
    
    def _get_table_display_name(self, table_name: str) -> str:
        """获取表格显示名称"""
        display_names = {
            'et_wait_lot': '待排产批次',
            'et_ft_test_spec': '测试规范', 
            'et_uph_eqp': 'UPH设备',
            'et_recipe_file': '设备配方',
            'eqp_status': '设备状态',
            'tcc_inv': '硬件库存',
            'ct': '生产周期',
            'wip_lot': '在制品',
            'lotprioritydone': '已排产批次',
            'devicepriorityconfig': '设备优先级配置',
            'lotpriorityconfig': '批次优先级配置',
            'product_priority_config': '产品优先级配置'
        }
        return display_names.get(table_name, table_name.upper())
    
    def create_record(self, table_name: str, data: Dict) -> Dict:
        """创建新记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法创建记录'
                }
            
            # 数据预处理 - 处理日期字段和数据类型
            processed_data = self._preprocess_record_data(table_name, data)
            
            if not processed_data:
                return {
                    'success': False,
                    'error': '没有有效的数据可以插入'
                }
            
            # 确定使用哪个数据库 - 🔥 修复连接泄漏
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps'  # 已迁移到aps数据库
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    # 构建插入语句
                    columns = list(processed_data.keys())
                    placeholders = ', '.join(['%s'] * len(columns))
                    column_names = ', '.join(columns)
                    
                    sql = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"
                    values = list(processed_data.values())
                    
                    logger.debug(f"执行插入SQL: {sql}")
                    logger.debug(f"插入数据: {values}")
                    
                    cursor.execute(sql, values)
                    connection.commit()
                    
                    # 获取插入的记录ID
                    record_id = cursor.lastrowid
            
            # 🔥 Phase 7: 使用强化缓存同步机制
            if self.enhanced_cache_sync:
                self.enhanced_cache_sync.on_data_change(table_name, CacheEventType.CREATE, [record_id])
            else:
                # 降级处理：使用原有缓存失效机制
                self.invalidate_cache_on_data_change(table_name)
            
            logger.info(f"成功创建{table_name}记录，ID: {record_id}")
            return {
                'success': True,
                'record_id': record_id,
                'message': '记录创建成功'
            }
            
        except Exception as e:
            logger.error(f"创建{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'创建记录失败: {str(e)}'
            }
    
    def update_record(self, table_name: str, data: Dict) -> Dict:
        """更新记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法更新记录'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            
            primary_key = field_config['primary_key']
            
            if primary_key not in data:
                return {
                    'success': False,
                    'error': f'缺少主键字段: {primary_key}'
                }
            
            # 保存主键值
            primary_key_value = data[primary_key]
            
            # 数据预处理 - 处理日期字段和数据类型
            processed_data = self._preprocess_record_data(table_name, data)
            
            # 移除主键，避免更新主键
            if primary_key in processed_data:
                processed_data.pop(primary_key)
            
            if not processed_data:  # 如果除了主键没有其他字段
                return {
                    'success': False,
                    'error': '没有要更新的字段'
                }
            
            # 确定使用哪个数据库 - 🔥 修复连接泄漏
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps'  # 已迁移到aps数据库
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    # 构建更新语句
                    set_clauses = ', '.join([f"{col} = %s" for col in processed_data.keys()])
                    sql = f"UPDATE {table_name} SET {set_clauses} WHERE {primary_key} = %s"
                    
                    values = list(processed_data.values()) + [primary_key_value]
                    
                    logger.debug(f"执行更新SQL: {sql}")
                    logger.debug(f"更新数据: {values}")
                    
                    cursor.execute(sql, values)
                    affected_rows = cursor.rowcount
                    connection.commit()
            
            # 🔥 Phase 7: 使用强化缓存同步机制  
            if self.enhanced_cache_sync:
                self.enhanced_cache_sync.on_data_change(table_name, CacheEventType.UPDATE, [primary_key_value])
            else:
                # 降级处理：清理所有缓存
                self.clear_cache()
            
            logger.info(f"成功更新{table_name}记录，主键: {primary_key_value}，影响行数: {affected_rows}")
            return {
                'success': True,
                'affected_rows': affected_rows,
                'message': '记录更新成功'
            }
            
        except Exception as e:
            logger.error(f"更新{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'更新记录失败: {str(e)}'
            }
    
    def delete_record(self, table_name: str, record_id: str) -> Dict:
        """删除单条记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法删除记录'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            
            primary_key = field_config['primary_key']
            
            # 确定使用哪个数据库 - 🔥 修复连接泄漏
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps'  # 已迁移到aps数据库
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    sql = f"DELETE FROM {table_name} WHERE {primary_key} = %s"
                    cursor.execute(sql, [record_id])
                    affected_rows = cursor.rowcount
                    connection.commit()
            
            # 🔥 Phase 7: 使用强化缓存同步机制
            if self.enhanced_cache_sync:
                self.enhanced_cache_sync.on_data_change(table_name, CacheEventType.DELETE, [record_id])
            else:
                # 降级处理：清理所有缓存
                self.clear_cache()
            
            logger.info(f"成功删除{table_name}记录，主键: {record_id}，影响行数: {affected_rows}")
            return {
                'success': True,
                'affected_rows': affected_rows,
                'message': '记录删除成功'
            }
            
        except Exception as e:
            logger.error(f"删除{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'删除记录失败: {str(e)}'
            }
    
    def batch_delete_records(self, table_name: str, record_ids: List[str]) -> Dict:
        """🔥 批量删除记录 - 使用统一连接管理器优化"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法删除记录'
                }
            
            if not record_ids:
                return {
                    'success': False,
                    'error': '没有提供要删除的记录ID'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            
            primary_key = field_config['primary_key']
            
            # 处理主键类型 - 特别是字符串类型的LOT_ID
            processed_ids = []
            for record_id in record_ids:
                # 确保记录ID是正确的格式
                if primary_key == 'LOT_ID' or 'LOT_ID' in primary_key:
                    # LOT_ID通常是字符串，保持原样
                    processed_ids.append(str(record_id))
                else:
                    # 数值型ID，尝试转换
                    try:
                        processed_ids.append(int(record_id))
                    except (ValueError, TypeError):
                        processed_ids.append(str(record_id))
            
            logger.debug(f"批量删除 {table_name}，主键: {primary_key}，ID列表: {processed_ids}")
            
            # 确定使用哪个数据库 - 🔥 修复连接泄漏
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps'  # 已迁移到aps数据库
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    # 构建批量删除语句
                    placeholders = ', '.join(['%s'] * len(processed_ids))
                    sql = f"DELETE FROM {table_name} WHERE {primary_key} IN ({placeholders})"
                    
                    logger.debug(f"执行批量删除SQL: {sql}")
                    logger.debug(f"删除ID列表: {processed_ids}")
                    
                    cursor.execute(sql, processed_ids)
                    deleted_count = cursor.rowcount
                    connection.commit()
            
            # 🔥 Phase 7: 使用强化缓存同步机制
            if self.enhanced_cache_sync:
                self.enhanced_cache_sync.on_data_change(table_name, CacheEventType.BULK_OPERATION, record_ids)
            else:
                # 降级处理：清理所有缓存
                self.clear_cache()
            
            logger.info(f"成功批量删除{table_name}记录，删除数量: {deleted_count}")
            return {
                'success': True,
                'deleted_count': deleted_count,
                'message': f'成功删除 {deleted_count} 条记录'
            }
            
        except Exception as e:
            logger.error(f"批量删除{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'批量删除失败: {str(e)}'
            }
    
    # 🔥 NEW: 增强批量CRUD操作方法 - 使用统一连接管理器
    
    def batch_create_records(self, table_name: str, records_data: List[Dict], batch_id: str = None) -> Dict:
        """
        🔥 批量创建记录 - 使用统一连接管理器批量优化
        
        单个连接处理数百条记录插入，从数百个连接减少到1个连接
        
        Args:
            table_name: 表名
            records_data: 记录数据列表
            batch_id: 批次标识符(可选)
            
        Returns:
            Dict: 执行结果
            
        Usage:
            # 批量插入268个排产结果，只用1个连接
            result = data_manager.batch_create_records(
                'lotprioritydone', 
                scheduling_results, 
                'scheduling_batch_001'
            )
        """
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法批量创建记录'
                }
            
            if not records_data:
                return {
                    'success': False,
                    'error': '没有提供要创建的记录数据'
                }
            
            # 🔥 使用统一连接管理器的批量连接
            from app.utils.unified_connection_manager import get_unified_batch_connection
            
            with get_unified_batch_connection('aps', batch_id or f'batch_create_{table_name}') as conn:
                cursor = conn.cursor()
                
                # 获取第一条记录的字段列表
                first_record = records_data[0]
                columns = list(first_record.keys())
                placeholders = ', '.join(['%s'] * len(columns))
                columns_str = ', '.join(columns)
                
                sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
                
                # 准备批量插入数据
                batch_values = []
                for record in records_data:
                    values = [record.get(col) for col in columns]
                    batch_values.append(values)
                
                # 执行批量插入
                cursor.executemany(sql, batch_values)
                inserted_count = cursor.rowcount
                conn.commit()
                cursor.close()
            
            # 🔥 Phase 7: 使用强化缓存同步机制
            if self.enhanced_cache_sync:
                # 批量创建，传递空的record_ids（因为无法知道具体ID）
                self.enhanced_cache_sync.on_data_change(table_name, CacheEventType.BULK_OPERATION, [])
            else:
                # 降级处理：清理所有缓存
                self.clear_cache()
            
            logger.info(f"✅ 批量创建{table_name}记录成功，插入数量: {inserted_count}")
            return {
                'success': True,
                'inserted_count': inserted_count,
                'message': f'成功插入 {inserted_count} 条记录'
            }
            
        except Exception as e:
            logger.error(f"❌ 批量创建{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'批量创建失败: {str(e)}'
            }
    
    def batch_update_records(self, table_name: str, records_data: List[Dict], 
                           primary_key: str = 'id', batch_id: str = None) -> Dict:
        """
        🔥 批量更新记录 - 使用统一连接管理器批量优化
        
        Args:
            table_name: 表名
            records_data: 更新记录数据列表，每条记录必须包含主键字段
            primary_key: 主键字段名
            batch_id: 批次标识符(可选)
            
        Returns:
            Dict: 执行结果
        """
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法批量更新记录'
                }
            
            if not records_data:
                return {
                    'success': False,
                    'error': '没有提供要更新的记录数据'
                }
            
            # 🔥 使用统一连接管理器的批量连接
            from app.utils.unified_connection_manager import get_unified_batch_connection
            
            with get_unified_batch_connection('aps', batch_id or f'batch_update_{table_name}') as conn:
                cursor = conn.cursor()
                updated_count = 0
                
                for record in records_data:
                    if primary_key not in record:
                        logger.warning(f"⚠️ 记录缺少主键{primary_key}，跳过更新: {record}")
                        continue
                    
                    # 构建UPDATE语句
                    update_fields = {k: v for k, v in record.items() if k != primary_key}
                    set_clauses = ', '.join([f"{col} = %s" for col in update_fields.keys()])
                    
                    sql = f"UPDATE {table_name} SET {set_clauses} WHERE {primary_key} = %s"
                    values = list(update_fields.values()) + [record[primary_key]]
                    
                    cursor.execute(sql, values)
                    updated_count += cursor.rowcount
                
                conn.commit()
                cursor.close()
            
            # 🔥 Phase 7: 使用强化缓存同步机制
            if self.enhanced_cache_sync:
                # 批量更新，传递相关的record_ids
                record_ids = [record.get(primary_key) for record in records_data if record.get(primary_key)]
                self.enhanced_cache_sync.on_data_change(table_name, CacheEventType.BULK_OPERATION, record_ids)
            else:
                # 降级处理：清理所有缓存
                self.clear_cache()
            
            logger.info(f"✅ 批量更新{table_name}记录成功，更新数量: {updated_count}")
            return {
                'success': True,
                'updated_count': updated_count,
                'message': f'成功更新 {updated_count} 条记录'
            }
            
        except Exception as e:
            logger.error(f"❌ 批量更新{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'批量更新失败: {str(e)}'
            }
    
    def batch_upsert_records(self, table_name: str, records_data: List[Dict], 
                           unique_keys: List[str], batch_id: str = None) -> Dict:
        """
        🔥 批量插入或更新记录 - 使用统一连接管理器批量优化
        
        基于唯一键执行 INSERT ... ON DUPLICATE KEY UPDATE 操作
        
        Args:
            table_name: 表名
            records_data: 记录数据列表
            unique_keys: 唯一键字段列表(用于判断是否存在)
            batch_id: 批次标识符(可选)
            
        Returns:
            Dict: 执行结果
        """
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法批量插入/更新记录'
                }
            
            if not records_data:
                return {
                    'success': False,
                    'error': '没有提供要插入/更新的记录数据'
                }
            
            # 🔥 使用统一连接管理器的批量连接
            from app.utils.unified_connection_manager import get_unified_batch_connection
            
            with get_unified_batch_connection('aps', batch_id or f'batch_upsert_{table_name}') as conn:
                cursor = conn.cursor()
                
                # 获取字段列表
                first_record = records_data[0]
                columns = list(first_record.keys())
                
                # 构建INSERT ... ON DUPLICATE KEY UPDATE语句
                columns_str = ', '.join(columns)
                placeholders = ', '.join(['%s'] * len(columns))
                update_clauses = ', '.join([f"{col} = VALUES({col})" for col in columns if col not in unique_keys])
                
                sql = f"""
                INSERT INTO {table_name} ({columns_str}) 
                VALUES ({placeholders})
                ON DUPLICATE KEY UPDATE {update_clauses}
                """
                
                # 准备批量数据
                batch_values = []
                for record in records_data:
                    values = [record.get(col) for col in columns]
                    batch_values.append(values)
                
                # 执行批量插入/更新
                cursor.executemany(sql, batch_values)
                affected_count = cursor.rowcount
                conn.commit()
                cursor.close()
            
            # 🔥 Phase 7: 使用强化缓存同步机制
            if self.enhanced_cache_sync:
                # 批量插入/更新操作
                self.enhanced_cache_sync.on_data_change(table_name, CacheEventType.BULK_OPERATION, [])
            else:
                # 降级处理：清理所有缓存
                self.clear_cache()
            
            logger.info(f"✅ 批量插入/更新{table_name}记录成功，影响数量: {affected_count}")
            return {
                'success': True,
                'affected_count': affected_count,
                'message': f'成功插入/更新 {affected_count} 条记录'
            }
            
        except Exception as e:
            logger.error(f"❌ 批量插入/更新{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'批量插入/更新失败: {str(e)}'
            }
    
    def batch_enhanced_delete_records(self, table_name: str, record_ids: List[str], 
                                    batch_id: str = None) -> Dict:
        """
        🔥 增强批量删除记录 - 使用统一连接管理器批量优化
        
        相比原有batch_delete_records，增加了批次ID支持和更好的连接管理
        
        Args:
            table_name: 表名
            record_ids: 要删除的记录ID列表
            batch_id: 批次标识符(可选)
            
        Returns:
            Dict: 执行结果
        """
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法批量删除记录'
                }
            
            if not record_ids:
                return {
                    'success': False,
                    'error': '没有提供要删除的记录ID'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            primary_key = field_config['primary_key']
            
            # 🔥 使用统一连接管理器的批量连接
            from app.utils.unified_connection_manager import get_unified_batch_connection
            
            with get_unified_batch_connection('aps', batch_id or f'batch_delete_{table_name}') as conn:
                cursor = conn.cursor()
                
                # 构建批量删除语句
                placeholders = ', '.join(['%s'] * len(record_ids))
                sql = f"DELETE FROM {table_name} WHERE {primary_key} IN ({placeholders})"
                
                cursor.execute(sql, record_ids)
                deleted_count = cursor.rowcount
                conn.commit()
                cursor.close()
            
            # 🔥 Phase 7: 使用强化缓存同步机制
            if self.enhanced_cache_sync:
                # 增强批量删除，使用batch_id作为元数据
                metadata = {'batch_id': batch_id} if batch_id else {}
                self.enhanced_cache_sync.on_data_change(table_name, CacheEventType.BULK_OPERATION, 
                                                       record_ids, metadata)
            else:
                # 降级处理：清理所有缓存
                self.clear_cache()
            
            logger.info(f"✅ 增强批量删除{table_name}记录成功，删除数量: {deleted_count}")
            return {
                'success': True,
                'deleted_count': deleted_count,
                'message': f'成功删除 {deleted_count} 条记录'
            }
            
        except Exception as e:
            logger.error(f"❌ 增强批量删除{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'增强批量删除失败: {str(e)}'
            }

    def _get_device_priority_data(self) -> List[Dict]:
        """
        🎯 获取产品优先级配置数据 - 仅MySQL源，使用确定性缓存
        
        Returns:
            List[Dict]: 产品优先级配置列表
        """
        data_type = "device_priority_config"
        try:
            if self.mysql_available:
                return self._get_cached_data(data_type, self._get_device_priority_from_mysql)
            else:
                logger.error("❌ MySQL数据源不可用，无法获取产品优先级配置")
                return []
        except Exception as e:
            logger.error(f"获取产品优先级数据失败: {e}")
            return []
    
    def _get_lot_priority_data(self) -> List[Dict]:
        """
        🎯 获取批次优先级配置数据 - 仅MySQL源，使用确定性缓存
        
        Returns:
            List[Dict]: 批次优先级配置列表
        """
        data_type = "lot_priority_config"
        try:
            if self.mysql_available:
                return self._get_cached_data(data_type, self._get_lot_priority_from_mysql)
            else:
                logger.error("❌ MySQL数据源不可用，无法获取批次优先级配置")
                return []
        except Exception as e:
            logger.error(f"获取批次优先级数据失败: {e}")
            return []
    
    def _get_lotprioritydone_data(self) -> List[Dict]:
        """获取已排产批次数据"""
        try:
            if self.mysql_available:
                return self._get_lotprioritydone_from_mysql()
            else:
                return []
        except Exception as e:
            logger.error(f"获取已排产批次数据失败: {e}")
            return []

    def _get_stage_mapping_config_data(self) -> List[Dict]:
        """🔗 新增：获取STAGE映射配置数据"""
        try:
            if self.mysql_available:
                return self._get_stage_mapping_config_from_mysql()
            else:
                return []
        except Exception as e:
            logger.error(f"获取STAGE映射配置数据失败: {e}")
            return []
    
    def _get_device_priority_from_mysql(self) -> List[Dict]:
        """从MySQL获取产品优先级配置数据 - 🔥 修复连接泄漏"""
        try:
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT * FROM devicepriorityconfig ORDER BY id")
                    data = []
                    for row in cursor.fetchall():
                        record = dict(row)
                        for time_field in ['from_time', 'end_time', 'refresh_time', 'created_at', 'updated_at']:
                            if record.get(time_field):
                                record[time_field] = str(record[time_field])
                        data.append(record)
                
                logger.info(f"从MySQL获取到 {len(data)} 条产品优先级配置数据")
                return data
            
        except Exception as e:
            logger.error(f"MySQL获取产品优先级配置数据失败: {e}")
            return []
    
    def _get_lot_priority_from_mysql(self) -> List[Dict]:
        """从MySQL获取批次优先级配置数据 - 🔥 修复连接泄漏"""
        try:
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT * FROM lotpriorityconfig ORDER BY id")
                    data = []
                    for row in cursor.fetchall():
                        record = dict(row)
                        for time_field in ['refresh_time', 'created_at', 'updated_at']:
                            if record.get(time_field):
                                record[time_field] = str(record[time_field])
                        data.append(record)
                
                logger.info(f"从MySQL获取到 {len(data)} 条批次优先级配置数据")
                return data
            
        except Exception as e:
            logger.error(f"MySQL获取批次优先级配置数据失败: {e}")
            return []
    

    
    def _get_lotprioritydone_from_mysql(self) -> List[Dict]:
        """从MySQL获取已排产批次数据 - 🔥 修复连接泄漏"""
        try:
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT * FROM lotprioritydone 
                        ORDER BY CREATE_TIME DESC, id DESC
                    """)
                    
                    lotprioritydone_data = []
                    for row in cursor.fetchall():
                        record = dict(row)
                        # 转换时间字段为字符串格式
                        for time_field in ['RELEASE_TIME', 'CREATE_TIME', 'created_at', 'updated_at']:
                            if record.get(time_field):
                                record[time_field] = str(record[time_field])
                        lotprioritydone_data.append(record)
                
                logger.info(f"从MySQL获取到 {len(lotprioritydone_data)} 条已排产批次数据")
                return lotprioritydone_data
            
        except Exception as e:
            logger.error(f"MySQL获取已排产批次数据失败: {e}")
            return []

    def _get_stage_mapping_config_from_mysql(self) -> List[Dict]:
        """🔗 从MySQL获取STAGE映射配置数据 - 🔥 修复连接泄漏"""
        try:
            # 🔥 修复: 使用统一连接池管理器，避免连接泄漏
            with self._get_db_connection_pooled() as connection:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT * FROM stage_mapping_config 
                        WHERE is_active = true
                        ORDER BY target_stage, priority, id
                    """)
                    
                    data = []
                    for row in cursor.fetchall():
                        record = dict(row)
                        # 转换时间字段为字符串格式
                        for time_field in ['created_at', 'updated_at']:
                            if record.get(time_field):
                                record[time_field] = str(record[time_field])
                        data.append(record)
                
                logger.info(f"从MySQL获取到 {len(data)} 条STAGE映射配置数据")
                return data
            
        except Exception as e:
            logger.error(f"MySQL获取STAGE映射配置数据失败: {e}")
            return []

    def _check_performance_requirements(self, table_name: str, estimated_size: int = None) -> Dict:
        """检查性能要求和优化策略"""
        try:
            # 获取系统资源状态
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 性能评估
            performance_status = {
                'memory_available': memory.available,
                'memory_percent': memory.percent,
                'cpu_percent': cpu_percent,
                'should_optimize': False,
                'optimization_strategy': 'none',
                'recommended_timeout': self.PERFORMANCE_OPTIMIZATION['max_query_timeout'],
                'use_chunked_loading': False
            }
            
            # 评估是否需要优化
            if memory.percent > self.PERFORMANCE_OPTIMIZATION['memory_threshold'] * 100:
                performance_status['should_optimize'] = True
                performance_status['optimization_strategy'] = 'memory_optimization'
                performance_status['use_chunked_loading'] = True
                performance_status['recommended_timeout'] = 60  # 增加超时时间
                logger.warning(f"⚠️ 内存使用率过高({memory.percent:.1f}%)，启用性能优化")
            
            if cpu_percent > 80:
                performance_status['should_optimize'] = True
                performance_status['optimization_strategy'] = 'cpu_optimization'
                performance_status['recommended_timeout'] = 45
                logger.warning(f"⚠️ CPU使用率过高({cpu_percent:.1f}%)，启用性能优化")
            
            # 预估数据大小优化
            if estimated_size and estimated_size > 50000:
                performance_status['should_optimize'] = True
                performance_status['optimization_strategy'] = 'large_dataset_optimization'
                performance_status['use_chunked_loading'] = True
                performance_status['recommended_timeout'] = 120
                logger.info(f"📊 预估数据量较大({estimated_size}条)，启用分块加载优化")
            
            return performance_status
            
        except Exception as e:
            logger.warning(f"⚠️ 性能检查失败: {e}")
            return {
                'should_optimize': False,
                'optimization_strategy': 'none',
                'recommended_timeout': self.PERFORMANCE_OPTIMIZATION['max_query_timeout'],
                'use_chunked_loading': False
            }

    def _get_mysql_table_data_optimized(self, table_name: str, performance_status: Dict) -> Dict[str, Dict]:
        """优化版MySQL表数据获取方法 - 使用连接池"""
        try:
            import time
            # 🔧 修复：使用连接池而不是直接创建连接
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 获取表的唯一字段配置
                    field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                        'primary_key': 'id',
                        'business_key': 'id', 
                        'display_key': 'id'
                    })
                    
                    if performance_status.get('use_chunked_loading', False):
                        # 分块加载策略
                        logger.info(f"🔄 使用分块加载策略获取 {table_name} 数据")
                        return self._chunked_mysql_loading(cursor, table_name, field_config)
                    else:
                        # 标准加载策略
                        start_time = time.time()
                        cursor.execute(f"""
                            SELECT * FROM {table_name} 
                            WHERE id IS NOT NULL
                            ORDER BY id
                        """)
                        
                        table_data = {}
                        row_count = 0
                        
                        for row in cursor.fetchall():
                            row_count += 1
                            
                            # 性能监控
                            if row_count % 10000 == 0:
                                elapsed = time.time() - start_time
                                logger.info(f"📊 已处理 {row_count} 条记录，耗时 {elapsed:.2f}秒")
                            
                            # 数据处理
                            processed_row = self._process_mysql_row(dict(row))
                            
                            # 存储数据
                            primary_key = processed_row.get(field_config['primary_key'])
                            business_key = processed_row.get(field_config['business_key'])
                            display_key = processed_row.get(field_config['display_key'])
                            
                            if primary_key:
                                table_data[str(primary_key)] = processed_row
                            if business_key and business_key != primary_key:
                                table_data[str(business_key)] = processed_row
                            if display_key and display_key not in [primary_key, business_key]:
                                table_data[str(display_key)] = processed_row
                        
                        total_time = time.time() - start_time
                        logger.info(f"✅ 成功获取 {table_name} 数据: {row_count} 条记录，耗时 {total_time:.2f}秒")
                        return table_data
                        
        except Exception as e:
            logger.error(f"❌ 优化版MySQL数据获取失败 - {table_name}: {e}")
            return {}

    def _chunked_mysql_loading(self, cursor, table_name: str, field_config: Dict) -> Dict[str, Dict]:
        """分块加载MySQL数据"""
        try:
            import time
            
            # 获取总记录数
            cursor.execute(f"SELECT COUNT(*) as total FROM {table_name} WHERE id IS NOT NULL")
            total_records = cursor.fetchone()['total']
            
            batch_size = self.PERFORMANCE_OPTIMIZATION['batch_size']
            total_batches = (total_records + batch_size - 1) // batch_size
            
            logger.info(f"📊 开始分块加载 {table_name}: 总记录数 {total_records}, 分为 {total_batches} 个批次")
            
            table_data = {}
            processed_count = 0
            start_time = time.time()
            
            for batch_num in range(total_batches):
                offset = batch_num * batch_size
                
                cursor.execute(f"""
                    SELECT * FROM {table_name} 
                    WHERE id IS NOT NULL
                    ORDER BY id
                    LIMIT {batch_size} OFFSET {offset}
                """)
                
                batch_start = time.time()
                batch_rows = cursor.fetchall()
                
                for row in batch_rows:
                    processed_count += 1
                    
                    # 数据处理
                    processed_row = self._process_mysql_row(dict(row))
                    
                    # 存储数据
                    primary_key = processed_row.get(field_config['primary_key'])
                    business_key = processed_row.get(field_config['business_key'])
                    display_key = processed_row.get(field_config['display_key'])
                    
                    if primary_key:
                        table_data[str(primary_key)] = processed_row
                    if business_key and business_key != primary_key:
                        table_data[str(business_key)] = processed_row
                    if display_key and display_key not in [primary_key, business_key]:
                        table_data[str(display_key)] = processed_row
                
                batch_time = time.time() - batch_start
                progress = (batch_num + 1) / total_batches * 100
                
                logger.info(f"📊 批次 {batch_num + 1}/{total_batches} 完成 ({progress:.1f}%): "
                           f"处理 {len(batch_rows)} 条记录，耗时 {batch_time:.2f}秒")
                
                # 内存检查
                if batch_num % 10 == 0:
                    try:
                        import psutil
                        memory = psutil.virtual_memory()
                        if memory.percent > 85:
                            logger.warning(f"⚠️ 内存使用率过高({memory.percent:.1f}%)，建议优化查询")
                    except:
                        pass
            
            total_time = time.time() - start_time
            logger.info(f"✅ 分块加载完成 {table_name}: 总计 {processed_count} 条记录，耗时 {total_time:.2f}秒")
            return table_data
            
        except Exception as e:
            logger.error(f"❌ 分块加载失败 - {table_name}: {e}")
            return {}

    def _process_mysql_row(self, row: Dict) -> Dict:
        """处理MySQL行数据"""
        processed_row = {}
        
        for key, value in row.items():
            if value is None:
                processed_row[key] = ''
            elif isinstance(value, bytes):
                processed_row[key] = value.decode('utf-8', errors='ignore')
            elif hasattr(value, 'isoformat'):
                processed_row[key] = value.isoformat()
            else:
                processed_row[key] = str(value)
        
        return processed_row
