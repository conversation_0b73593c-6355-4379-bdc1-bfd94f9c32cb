#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正后的数据完整性深度分析 - 使用正确的表名et_recipe_file
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict
import json

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('corrected_data_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('CorrectedDataAnalyzer')

def analyze_corrected_data_integrity():
    """使用正确表名的数据完整性分析"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            
            data_manager = DataSourceManager()
            
            print("="*80)
            print("🔍 修正后的数据完整性深度分析")
            print("="*80)
            
            # ==================== 获取正确的数据 ====================
            print(f"\n📊 获取基础数据 (使用正确表名)...")
            
            # 获取待排产批次
            wait_lots_result = data_manager.get_table_data('et_wait_lot')
            wait_lots = wait_lots_result.get('data', [])
            
            # 获取测试规格数据
            test_spec_result = data_manager.get_table_data('et_ft_test_spec')
            test_specs = test_spec_result.get('data', [])
            
            # 获取配方文件数据 (使用正确表名!)
            recipe_result = data_manager.get_table_data('et_recipe_file')
            recipe_files = recipe_result.get('data', [])
            
            print(f"   待排产批次: {len(wait_lots)} 个")
            print(f"   测试规格: {len(test_specs)} 条")
            print(f"   配方文件 (et_recipe_file): {len(recipe_files)} 条")
            
            # ==================== 分析et_recipe_file数据质量 ====================
            print(f"\n🔍 分析et_recipe_file表的数据质量")
            
            recipe_quality = {
                'total_recipes': len(recipe_files),
                'complete_recipes': 0,
                'device_types': defaultdict(int),
                'stage_types': defaultdict(int),
                'approval_states': defaultdict(int),
                'handler_configs': defaultdict(int),
                'device_stage_pairs': defaultdict(int)
            }
            
            recipe_combinations = set()
            
            for recipe in recipe_files:
                device = (recipe.get('DEVICE') or '').strip()
                stage = (recipe.get('STAGE') or '').strip()
                approval = (recipe.get('APPROVAL_STATE') or '').strip()
                handler_config = (recipe.get('HANDLER_CONFIG') or '').strip()
                
                if device and stage:
                    recipe_quality['complete_recipes'] += 1
                    recipe_combinations.add((device, stage))
                    recipe_quality['device_types'][device] += 1
                    recipe_quality['stage_types'][stage] += 1
                    recipe_quality['device_stage_pairs'][f"{device}-{stage}"] += 1
                
                recipe_quality['approval_states'][approval or '空'] += 1
                if handler_config:
                    recipe_quality['handler_configs'][handler_config] += 1
            
            print(f"   et_recipe_file数据统计:")
            print(f"   - 总配方数: {recipe_quality['total_recipes']}")
            print(f"   - 完整配方 (有DEVICE+STAGE): {recipe_quality['complete_recipes']}")
            print(f"   - 不同DEVICE类型: {len(recipe_quality['device_types'])}")
            print(f"   - 不同STAGE类型: {len(recipe_quality['stage_types'])}")
            print(f"   - 不同DEVICE-STAGE组合: {len(recipe_combinations)}")
            print(f"   - 不同HANDLER_CONFIG: {len(recipe_quality['handler_configs'])}")
            
            print(f"\n   审批状态分布:")
            for state, count in sorted(recipe_quality['approval_states'].items(), key=lambda x: x[1], reverse=True):
                print(f"     - {state:<15}: {count} 条")
            
            print(f"\n   Top 10 HANDLER_CONFIG:")
            for i, (config, count) in enumerate(sorted(recipe_quality['handler_configs'].items(), key=lambda x: x[1], reverse=True)[:10], 1):
                print(f"     {i:2d}. {config:<20} : {count} 条配方")
            
            # ==================== 重新计算覆盖率 ====================
            print(f"\n🔍 重新计算匹配覆盖率")
            
            # 待排产批次需要的DEVICE-STAGE组合
            wait_combinations = set()
            for lot in wait_lots:
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                if device and stage:
                    wait_combinations.add((device, stage))
            
            # ET_FT_TEST_SPEC中Released状态的组合
            released_spec_combinations = set()
            for spec in test_specs:
                if (spec.get('APPROVAL_STATE') or '').strip() == 'Released':
                    device = (spec.get('DEVICE') or '').strip()
                    stage = (spec.get('STAGE') or '').strip()
                    if device and stage:
                        released_spec_combinations.add((device, stage))
            
            # et_recipe_file的Released状态组合
            released_recipe_combinations = set()
            for recipe in recipe_files:
                if (recipe.get('APPROVAL_STATE') or '').strip() == 'Released':
                    device = (recipe.get('DEVICE') or '').strip()
                    stage = (recipe.get('STAGE') or '').strip()
                    if device and stage:
                        released_recipe_combinations.add((device, stage))
            
            # 计算各种覆盖率
            spec_covered = wait_combinations & released_spec_combinations
            recipe_covered = wait_combinations & released_recipe_combinations
            either_covered = released_spec_combinations | released_recipe_combinations
            joint_coverage = wait_combinations & either_covered
            still_uncovered = wait_combinations - either_covered
            
            print(f"   修正后的匹配覆盖率分析:")
            print(f"   - 待排产需要的组合: {len(wait_combinations)} 种")
            print(f"   - ET_FT_TEST_SPEC覆盖: {len(spec_covered)} 种 ({len(spec_covered)/len(wait_combinations)*100:.1f}%)")
            print(f"   - et_recipe_file覆盖: {len(recipe_covered)} 种 ({len(recipe_covered)/len(wait_combinations)*100:.1f}%)")
            print(f"   - 联合总覆盖率: {len(joint_coverage)/len(wait_combinations)*100:.1f}%")
            print(f"   - 仍未覆盖: {len(still_uncovered)} 种")
            
            if still_uncovered:
                print(f"\n   🚨 两个表都无法覆盖的DEVICE-STAGE组合:")
                for i, (device, stage) in enumerate(list(still_uncovered)[:10], 1):
                    affected_count = sum(1 for lot in wait_lots 
                                       if (lot.get('DEVICE') or '').strip() == device 
                                       and (lot.get('STAGE') or '').strip() == stage)
                    print(f"     {i:2d}. {device}-{stage} (影响 {affected_count} 个批次)")
            
            # ==================== 分析当前匹配算法为什么仍然失败 ====================
            print(f"\n🔍 分析当前匹配算法失败的真正原因")
            
            # 导入排产服务
            from app.services.real_scheduling_service import RealSchedulingService
            scheduling_service = RealSchedulingService()
            
            # 预加载数据
            preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
            
            print(f"   预加载数据统计:")
            print(f"   - recipe_files: {len(preloaded_data.get('recipe_files', []))} 条")
            print(f"   - test_specs: {len(preloaded_data.get('test_specs', []))} 条")
            
            # 测试几个代表性批次
            sample_lots = wait_lots[:5]
            
            print(f"\n   分析代表性批次的匹配过程:")
            
            for i, lot in enumerate(sample_lots, 1):
                lot_id = lot.get('LOT_ID', '')
                device = (lot.get('DEVICE') or '').strip()
                stage = (lot.get('STAGE') or '').strip()
                
                print(f"\n   批次 {i}: {lot_id} ({device}-{stage})")
                
                # 检查ET_FT_TEST_SPEC匹配
                exact_spec_matches = []
                for spec in test_specs:
                    spec_device = (spec.get('DEVICE') or '').strip()
                    spec_stage = (spec.get('STAGE') or '').strip()
                    spec_approval = (spec.get('APPROVAL_STATE') or '').strip()
                    
                    if spec_device == device and spec_stage == stage:
                        exact_spec_matches.append({
                            'approval': spec_approval,
                            'handler': (spec.get('HANDLER') or '').strip()
                        })
                
                released_spec_matches = [m for m in exact_spec_matches if m['approval'] == 'Released']
                
                print(f"     ET_FT_TEST_SPEC: {len(exact_spec_matches)} 条匹配, {len(released_spec_matches)} 条Released")
                
                # 检查et_recipe_file匹配
                exact_recipe_matches = []
                for recipe in recipe_files:
                    recipe_device = (recipe.get('DEVICE') or '').strip()
                    recipe_stage = (recipe.get('STAGE') or '').strip()
                    recipe_approval = (recipe.get('APPROVAL_STATE') or '').strip()
                    
                    if recipe_device == device and recipe_stage == stage:
                        exact_recipe_matches.append({
                            'approval': recipe_approval,
                            'handler_config': (recipe.get('HANDLER_CONFIG') or '').strip(),
                            'kit_pn': (recipe.get('KIT_PN') or '').strip()
                        })
                
                released_recipe_matches = [m for m in exact_recipe_matches if m['approval'] == 'Released']
                
                print(f"     et_recipe_file: {len(exact_recipe_matches)} 条匹配, {len(released_recipe_matches)} 条Released")
                
                # 如果两个表都有Released匹配，那为什么算法还是失败？
                if released_spec_matches and released_recipe_matches:
                    print(f"     ✅ 两个表都有Released匹配，理论上应该成功")
                    
                    # 测试实际的匹配函数
                    try:
                        config = scheduling_service.get_lot_configuration_requirements_optimized(lot, preloaded_data)
                        if config:
                            print(f"     ✅ 实际匹配成功: {config.get('SOURCE', 'Unknown')}")
                        else:
                            print(f"     ❌ 实际匹配失败 - 算法存在Bug!")
                    except Exception as e:
                        print(f"     ❌ 匹配异常: {str(e)[:50]}")
                elif released_spec_matches and not released_recipe_matches:
                    print(f"     ⚠️ 只有ET_FT_TEST_SPEC匹配，缺少recipe_file配置")
                elif not released_spec_matches and released_recipe_matches:
                    print(f"     ⚠️ 只有et_recipe_file匹配，缺少测试规格")
                else:
                    print(f"     ❌ 两个表都无Released匹配")
            
            # 保存修正后的分析结果
            corrected_result = {
                'timestamp': datetime.now().isoformat(),
                'recipe_file_records': len(recipe_files),
                'recipe_quality': recipe_quality,
                'coverage_analysis': {
                    'wait_combinations_count': len(wait_combinations),
                    'spec_coverage_count': len(spec_covered),
                    'spec_coverage_rate': len(spec_covered)/len(wait_combinations)*100 if wait_combinations else 0,
                    'recipe_coverage_count': len(recipe_covered),
                    'recipe_coverage_rate': len(recipe_covered)/len(wait_combinations)*100 if wait_combinations else 0,
                    'joint_coverage_count': len(joint_coverage),
                    'joint_coverage_rate': len(joint_coverage)/len(wait_combinations)*100 if wait_combinations else 0,
                    'uncovered_combinations': list(still_uncovered)
                },
                'correction_note': '使用了正确的表名et_recipe_file而不是recipe_file'
            }
            
            result_file = f'corrected_data_integrity_result_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(corrected_result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n📄 修正后的分析结果已保存到: {result_file}")
            print("="*80)
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_corrected_data_integrity()
    if success:
        print("\n🎉 修正后的数据完整性分析完成！")
    else:
        print("\n❌ 分析失败！")

if __name__ == "__main__":
    main()