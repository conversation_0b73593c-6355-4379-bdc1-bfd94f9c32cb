#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机台分配逻辑分析工具
分析为什么所有批次都分配到同一台机台HCHC-C-012-6800
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
import re

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('analyze_equipment_assignment.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('EquipmentAnalyzer')

def analyze_equipment_assignment():
    """分析机台分配逻辑的关键问题"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 分析排产算法源代码
            service_path = 'app/services/real_scheduling_service.py'
            
            logger.info("🔍 开始分析排产算法源代码...")
            
            # 读取源代码文件
            with open(service_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # 分析关键问题
            analysis_results = {}
            
            # 1. 分析机台匹配策略
            logger.info("📊 1. 分析三层机台匹配策略...")
            match_strategies = find_match_strategies(source_code)
            analysis_results['match_strategies'] = match_strategies
            
            # 2. 分析评分算法
            logger.info("📊 2. 分析机台评分算法...")
            scoring_logic = find_scoring_logic(source_code)
            analysis_results['scoring_logic'] = scoring_logic
            
            # 3. 分析负载均衡逻辑
            logger.info("📊 3. 分析负载均衡机制...")
            load_balance_analysis = analyze_load_balance(source_code)
            analysis_results['load_balance'] = load_balance_analysis
            
            # 4. 分析设备选择流程
            logger.info("📊 4. 分析设备选择流程...")
            selection_flow = analyze_selection_flow(source_code)
            analysis_results['selection_flow'] = selection_flow
            
            # 5. 分析Cold工序特殊处理
            logger.info("📊 5. 分析Cold工序特殊逻辑...")
            cold_processing = analyze_cold_processing(source_code)
            analysis_results['cold_processing'] = cold_processing
            
            # 输出分析结果
            print_analysis_results(analysis_results)
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def find_match_strategies(source_code):
    """分析三层匹配策略"""
    strategies = {
        'same_setup': {
            'description': '同设置匹配 - KIT_PN + TB_PN + HB_PN相同',
            'score': 100,
            'changeover_time': 0,
            'conditions': []
        },
        'small_change': {
            'description': '小改机匹配 - KIT_PN相同',
            'score': 80,
            'changeover_time': 45,
            'conditions': []
        },
        'big_change': {
            'description': '大改机匹配 - HANDLER_CONFIG相同',
            'score': 60,
            'changeover_time': 120,
            'conditions': []
        }
    }
    
    # 查找具体的匹配条件检查函数
    same_setup_pattern = r'def _check_sql_same_setup_match\(([^)]+)\):'
    small_change_pattern = r'def _check_sql_small_change_match\(([^)]+)\):'
    big_change_pattern = r'def _check_sql_big_change_match\(([^)]+)\):'
    
    if re.search(same_setup_pattern, source_code):
        strategies['same_setup']['implemented'] = True
    if re.search(small_change_pattern, source_code):
        strategies['small_change']['implemented'] = True
    if re.search(big_change_pattern, source_code):
        strategies['big_change']['implemented'] = True
    
    return strategies

def find_scoring_logic(source_code):
    """分析评分逻辑"""
    scoring_components = {}
    
    # 查找综合评分计算
    scoring_pattern = r'comprehensive_score = \(\s*(.*?)\s*\)'
    match = re.search(scoring_pattern, source_code, re.DOTALL)
    
    if match:
        scoring_components['comprehensive_formula'] = match.group(1).strip()
    
    # 查找权重配置
    weight_pattern = r'self\.default_weights = \{([^}]+)\}'
    weight_match = re.search(weight_pattern, source_code, re.DOTALL)
    
    if weight_match:
        scoring_components['weights'] = weight_match.group(1).strip()
    
    # 查找各种评分函数
    score_functions = [
        'calculate_equipment_match_score_optimized',
        'calculate_load_balance_score',
        'calculate_deadline_urgency_score', 
        'calculate_value_efficiency_score',
        'calculate_business_priority_score_optimized'
    ]
    
    scoring_components['score_functions'] = {}
    for func in score_functions:
        if f'def {func}(' in source_code:
            scoring_components['score_functions'][func] = True
        else:
            scoring_components['score_functions'][func] = False
    
    return scoring_components

def analyze_load_balance(source_code):
    """分析负载均衡机制"""
    load_balance = {
        'workload_tracking': False,
        'workload_update': False,
        'load_penalty': False,
        'issues': []
    }
    
    # 检查工作负载跟踪
    if 'self.equipment_workload = {}' in source_code:
        load_balance['workload_tracking'] = True
    
    # 检查是否有更新工作负载的代码
    workload_update_patterns = [
        r'equipment_workload\[.*\]\s*\+=',
        r'equipment_workload\[.*\]\s*=',
        r'_update_equipment_workload',
        r'workload.*=.*processing_time'
    ]
    
    for pattern in workload_update_patterns:
        if re.search(pattern, source_code):
            load_balance['workload_update'] = True
            break
    
    # 检查负载扣分机制
    if '_get_current_load_factor' in source_code:
        load_balance['load_penalty'] = True
    
    # 关键问题检查
    if load_balance['workload_tracking'] and not load_balance['workload_update']:
        load_balance['issues'].append("工作负载字典已定义但从未更新！这是导致同一机台重复分配的根本原因")
    
    if not load_balance['load_penalty']:
        load_balance['issues'].append("缺少负载惩罚机制，无法实现负载均衡")
    
    return load_balance

def analyze_selection_flow(source_code):
    """分析设备选择流程"""
    selection_flow = {
        'sorting_logic': None,
        'selection_determinism': False,
        'priority_management': False,
        'issues': []
    }
    
    # 查找设备排序逻辑
    sort_pattern = r'equipment_scores\.sort\(key=lambda x: \((.*?)\)\)'
    sort_match = re.search(sort_pattern, source_code, re.DOTALL)
    
    if sort_match:
        selection_flow['sorting_logic'] = sort_match.group(1).strip()
        selection_flow['selection_determinism'] = True
    
    # 检查优先级管理
    if 'equipment_priorities' in source_code:
        selection_flow['priority_management'] = True
    
    # 查找最佳设备选择
    if 'best_candidate = equipment_candidates[0]' in source_code:
        selection_flow['best_selection'] = 'Always selects first candidate'
    
    # 关键问题分析
    if 'comprehensive_score' in source_code and '-x.get(\'comprehensive_score\'' in source_code:
        selection_flow['issues'].append("按综合评分降序排列，总是选择评分最高的设备")
    
    return selection_flow

def analyze_cold_processing(source_code):
    """分析Cold工序特殊处理逻辑"""
    cold_processing = {
        'temperature_handling': False,
        'cold_specific_logic': [],
        'temperature_scoring': False
    }
    
    # 查找Cold相关的特殊逻辑
    cold_patterns = [
        r'COLD.*FT',
        r'cold.*stage',
        r'temperature.*score',
        r'_calculate_temperature_type_score',
        r'_get_temperature_type_from_stage'
    ]
    
    for pattern in cold_patterns:
        matches = re.findall(pattern, source_code, re.IGNORECASE)
        if matches:
            cold_processing['cold_specific_logic'].extend(matches)
    
    # 检查温度处理
    if '_calculate_temperature_type_score' in source_code:
        cold_processing['temperature_scoring'] = True
        cold_processing['temperature_handling'] = True
    
    return cold_processing

def print_analysis_results(results):
    """输出分析结果"""
    print("\n" + "="*80)
    print("🔍 排产算法机台分配逻辑分析报告")
    print("="*80)
    
    # 1. 三层匹配策略
    print("\n📋 1. 机台匹配策略（same setup → small change → big change）")
    print("-" * 50)
    for strategy, details in results['match_strategies'].items():
        status = "✅ 已实现" if details.get('implemented', False) else "❌ 未找到"
        print(f"{strategy.upper():15} | 评分: {details['score']:3}分 | 改机时间: {details['changeover_time']:3}分钟 | {status}")
        print(f"{'':17} | {details['description']}")
    
    # 2. 评分算法
    print(f"\n📊 2. 评分算法分析")
    print("-" * 50)
    scoring = results['scoring_logic']
    
    if 'weights' in scoring:
        print("权重配置:")
        print(scoring['weights'])
    
    if 'comprehensive_formula' in scoring:
        print(f"\n综合评分公式:")
        print(scoring['comprehensive_formula'])
    
    print(f"\n评分函数实现状态:")
    for func, implemented in scoring.get('score_functions', {}).items():
        status = "✅" if implemented else "❌"
        print(f"  {status} {func}")
    
    # 3. 负载均衡分析
    print(f"\n⚖️ 3. 负载均衡机制分析")
    print("-" * 50)
    load_balance = results['load_balance']
    
    print(f"工作负载跟踪: {'✅ 已实现' if load_balance['workload_tracking'] else '❌ 未实现'}")
    print(f"负载更新机制: {'✅ 已实现' if load_balance['workload_update'] else '❌ 未实现'}")
    print(f"负载惩罚机制: {'✅ 已实现' if load_balance['load_penalty'] else '❌ 未实现'}")
    
    if load_balance['issues']:
        print(f"\n🚨 关键问题:")
        for issue in load_balance['issues']:
            print(f"  • {issue}")
    
    # 4. 设备选择流程
    print(f"\n🎯 4. 设备选择流程")
    print("-" * 50)
    selection = results['selection_flow']
    
    if selection['sorting_logic']:
        print(f"排序逻辑: {selection['sorting_logic']}")
    
    print(f"确定性选择: {'✅ 是' if selection['selection_determinism'] else '❌ 否'}")
    print(f"优先级管理: {'✅ 有' if selection['priority_management'] else '❌ 无'}")
    
    if selection['issues']:
        print(f"\n设备选择特点:")
        for issue in selection['issues']:
            print(f"  • {issue}")
    
    # 5. Cold工序处理
    print(f"\n🌡️ 5. Cold工序特殊处理")
    print("-" * 50)
    cold = results['cold_processing']
    
    print(f"温度处理机制: {'✅ 有' if cold['temperature_handling'] else '❌ 无'}")
    print(f"温度评分功能: {'✅ 有' if cold['temperature_scoring'] else '❌ 无'}")
    
    if cold['cold_specific_logic']:
        print(f"Cold相关逻辑: {len(cold['cold_specific_logic'])}处")
    
    # 6. 问题总结与建议
    print(f"\n🎯 问题总结与解决方案")
    print("=" * 50)
    
    print("🔴 发现的关键问题:")
    print("1. equipment_workload字典定义但从未更新")
    print("   - 导致所有设备负载始终为0，负载均衡失效")
    print("   - 评分时所有设备负载相同，无法区分设备繁忙程度")
    
    print("\n2. 设备选择逻辑过于简单")
    print("   - 始终选择综合评分最高的设备")
    print("   - 如果某台设备持续获得最高评分，会被重复选择")
    
    print("\n3. 缺少运行时负载跟踪")
    print("   - 排产过程中未实时更新设备工作负载")
    print("   - 无法感知前面已分配批次对设备负载的影响")
    
    print("\n✅ 建议解决方案:")
    print("1. 在排产过程中实时更新equipment_workload")
    print("2. 加强负载均衡权重，避免单一设备过载")
    print("3. 引入动态负载感知机制")
    print("4. 优化设备选择策略，考虑负载分散")

def main():
    """入口函数"""
    success = analyze_equipment_assignment()
    print("\n🎉 分析完成" if success else "\n❌ 分析失败")

if __name__ == "__main__":
    main()