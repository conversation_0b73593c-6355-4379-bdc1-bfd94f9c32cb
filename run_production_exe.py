#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 车规芯片终测智能调度平台 - 生产级EXE启动脚本
版本: v5.0 (Waitress生产服务器 + 完整功能集成)
专门用于PyInstaller打包的exe环境
基于 run_production.py 的逻辑进行EXE适配
"""

import sys
import os
import logging
import traceback

# 在导入app之前设置基础日志级别
logging.getLogger().setLevel(logging.WARNING)

# 设置静默启动环境变量
os.environ['FLASK_QUIET_STARTUP'] = '1'
os.environ['FLASK_ENV'] = 'production'
os.environ['FLASK_DEBUG'] = '0'

# 临时屏蔽第三方库和app模块的INFO级别日志
for logger_name in ['app', 'app.models', 'app.services']:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

from app import create_app

# 导入新的统一配置管理器
from config.aps_config import config

# 确保必要目录存在 - 使用配置化路径
critical_dirs = [
    config.LOG_DIR,
    config.INSTANCE_DIR,
    config.STATIC_EXPORTS_DIR,
    config.DOWNLOAD_DIR,
    config.UPLOAD_DIR
]
for directory in critical_dirs:
    try:
        os.makedirs(directory, exist_ok=True)
        logging.debug(f"确保目录存在: {directory}")
    except Exception as e:
        logging.warning(f"创建目录失败 {directory}: {e}")

# 配置日志系统 - 使用配置化路径
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.WARNING if config.QUIET_STARTUP else getattr(logging, config.LOG_LEVEL))

# 创建日志文件处理器 - 支持exe环境
try:
    handlers_list = [console_handler]
    
    # 为exe环境特殊处理日志配置
    if getattr(sys, 'frozen', False):
        # exe环境：优先使用exe目录下的logs文件夹
        exe_dir = os.path.dirname(sys.executable)
        exe_logs_dir = os.path.join(exe_dir, 'logs')
        os.makedirs(exe_logs_dir, exist_ok=True)
        
        exe_log_file = os.path.join(exe_logs_dir, 'app.log')
        exe_file_handler = logging.FileHandler(exe_log_file, encoding='utf-8')
        exe_file_handler.setLevel(logging.INFO)  # exe环境使用INFO级别
        
        handlers_list.append(exe_file_handler)
        print(f"[EXE] Log file: {exe_log_file}")
        
        # 同时也创建项目根目录的日志（如果可能）
        try:
            if not os.path.exists(config.LOG_DIR):
                os.makedirs(config.LOG_DIR, exist_ok=True)
            
            log_file_path = config.get_log_file_path('app.log')
            file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)  # 项目日志保留详细信息
            handlers_list.append(file_handler)
        except Exception as fallback_error:
            print(f"[WARN] Project logs fallback failed: {fallback_error}")
    else:
        # 开发环境：使用项目根目录的logs
        if not os.path.exists(config.LOG_DIR):
            os.makedirs(config.LOG_DIR, exist_ok=True)
        
        log_file_path = config.get_log_file_path('app.log')
        file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)  # 文件保留详细信息
        handlers_list.append(file_handler)
        
except Exception as e:
    # 如果文件日志失败，只使用控制台
    print(f"[ERROR] Log configuration failed: {e}, using console only")
    handlers_list = [console_handler]

# 清除现有的处理器，避免重复配置
root_logger = logging.getLogger()
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

logging.basicConfig(
    level=logging.INFO,  # 根日志器设为INFO，避免过多DEBUG信息
    format='%(asctime)s | %(levelname)-8s | %(message)s',   # 标准化格式
    datefmt='%H:%M:%S',
    handlers=handlers_list
)

# 添加用户友好的日志过滤器
class UserFriendlyFilter(logging.Filter):
    def filter(self, record):
        # 过滤掉对用户无意义的技术信息
        ignore_patterns = [
            '✅.*蓝图注册', '✅.*API.*蓝图', '✅.*初始化成功', 
            'Server initialized', 'Python版本', '模块.*已安装',
            '上下文处理器', '数据库.*存在', '运行时环境检查完成',
            '无法导入CP订单模型', 'attempted relative import',
            '传统模型导入成功', '系统模型包已禁用', '已禁用',
            'INFO:app.models', 'INFO:app.', 'INFO:engineio',
            '✅.*模型.*验证', '✅.*开发环境', '已应用功能配置',
            '生产模型包已禁用', '失败跟踪系统已集成', '横向信息提取器',
            '解析器初始化', '通用Excel解析器', '手动排产API蓝图',
            '生产管理视图蓝图', '订单.*蓝图', '高并发.*蓝图',
            '已排产批次.*蓝图', '手动调整.*蓝图', '最终排产.*蓝图',
            '资源.*蓝图', '系统.*蓝图', '多级缓存.*蓝图',
            '并行计算.*蓝图', '认证.*蓝图', 'WIP批次.*蓝图',
            '所有API.*蓝图', 'API v3.*蓝图', 'API v3处于开发',
            '统一日志系统已配置', 'APS应用启动.*v2.1',
            '应用启动$'
        ]
        
        message = record.getMessage()
        for pattern in ignore_patterns:
            import re
            if re.search(pattern, message):
                return False
        return True

logger = logging.getLogger('APS-Platform')

# 为控制台处理器添加过滤器（过滤技术信息）
console_handler.addFilter(UserFriendlyFilter())

# 设置第三方库日志级别，减少噪音
logging.getLogger('werkzeug').setLevel(logging.WARNING)
logging.getLogger('engineio').setLevel(logging.ERROR)
logging.getLogger('socketio').setLevel(logging.ERROR)
logging.getLogger('apscheduler').setLevel(logging.WARNING)
logging.getLogger('waitress').setLevel(logging.WARNING)

def get_app_path():
    """获取应用程序路径，处理PyInstaller打包和开发环境"""
    if getattr(sys, 'frozen', False):
        # 运行在PyInstaller打包环境
        # sys._MEIPASS 是PyInstaller解压临时文件的路径
        return getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
    else:
        # 运行在常规Python环境
        return os.path.dirname(os.path.abspath(__file__))

def check_mysql_connection():
    """检查MySQL数据库连接和表结构"""
    try:
        # 导入必要模块
        import pymysql

        # 使用新的统一配置系统（自动处理配置优先级：config.ini > 环境变量 > 默认值）
        try:
            mysql_config = {
                'host': config.DB_HOST,
                'port': config.DB_PORT,
                'user': config.DB_USER,
                'password': config.DB_PASSWORD,
                'charset': config.DB_CHARSET
            }
            config_source = f"统一配置系统 ({config.DB_HOST}:{config.DB_PORT})"
            logger.info(f"✅ 使用统一配置系统: {config.DB_HOST}:{config.DB_PORT}")
        except (AttributeError, Exception) as e:
            logger.error(f"统一配置系统失败: {e}")
            if getattr(sys, 'frozen', False):
                raise Exception("❌ exe环境必须提供config.ini配置文件！请检查config.ini是否存在。")
            # 仅开发环境使用硬编码默认值
            mysql_config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': 'WWWwww123!',
                'charset': 'utf8mb4'
            }
            config_source = f"开发环境默认配置 (localhost:3306)"
            logger.warning(f"回退到开发环境默认配置")

        # 显示当前使用的配置源
        print(f"[INFO] Database config source: {config_source}")

        # 需要检查的数据库和关键表 - 单数据库模式（Linux MySQL区分大小写，统一使用小写）
        database_checks = {
            'aps': [
                # 业务表（统一使用小写表名，兼容Linux MySQL）
                'et_wait_lot', 'wip_lot', 'eqp_status', 'et_ft_test_spec',
                'et_uph_eqp', 'ct', 'tcc_inv', 'lotprioritydone',
                # 系统表（已迁移到aps数据库）
                'users', 'user_permissions', 'menu_permissions',
                'devicepriorityconfig', 'lotpriorityconfig'
            ]
        }

        # 连接MySQL服务器
        try:
            conn = pymysql.connect(**mysql_config)
            cursor = conn.cursor()
            print(f"[OK] Database connection successful: {mysql_config['host']}:{mysql_config['port']}")
        except Exception as conn_error:
            print(f"[ERROR] Database connection failed: {mysql_config['host']}:{mysql_config['port']}")
            print(f"   Error message: {conn_error}")

            # 提供详细的故障排除指导
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe
                print("\n[TROUBLESHOOTING] For EXE deployment mode:")
                print("1. Check if config.ini file exists in the same directory as the exe file")
                print("2. Confirm the database server address in config.ini is correct")
                print("3. Confirm MySQL server is running and accessible")
                print("4. Run database_config_wizard.bat to reconfigure database connection")
                print("5. Check firewall settings to allow connections")
            else:
                # 开发环境
                print("\n[TROUBLESHOOTING] For development environment:")
                print("1. Check if MySQL service is running")
                print("2. Confirm username and password are correct")
                print("3. Check network connection")
                print("4. Confirm MySQL port 3306 is open")

            raise conn_error

        missing_items = []

        for db_name, required_tables in database_checks.items():
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            if not cursor.fetchone():
                logger.error(f"数据库 '{db_name}' 不存在")
                missing_items.append(f"数据库: {db_name}")
                continue

            logger.info(f"数据库 '{db_name}' 存在")

            # 检查关键表是否存在
            cursor.execute(f"USE {db_name}")
            for table_name in required_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if cursor.fetchone():
                    logger.debug(f"表 '{db_name}.{table_name}' 存在")
                else:
                    logger.warning(f"表 '{db_name}.{table_name}' 不存在")
                    missing_items.append(f"表: {db_name}.{table_name}")

        conn.close()

        if missing_items:
            logger.error("❌ 数据库检查发现问题:")
            for item in missing_items:
                logger.error(f"   - {item}")
            logger.error("请运行: python run.py init-db")
            return False

        # 执行数据库安全检查
        try:
            logger.info("执行数据库安全检查...")
            # 将安全检查移到应用创建后执行，这样就有应用上下文了
            # 这里只做基本的连接检查，详细的安全检查在应用启动后进行
            logger.info("[INFO] 数据库基本连接检查完成，详细安全检查将在应用启动后进行")

        except Exception as e:
            logger.warning(f"[WARNING] 数据库安全检查失败: {e}")
            logger.info("[INFO] 安全检查失败不影响系统运行")

        logger.info("MySQL 数据库和表结构检查完成")
        return True

    except ImportError as e:
        logger.error(f"❌ 缺少必要模块: {e}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    except Exception as e:
        logger.error(f"❌ MySQL 数据库检查失败: {e}")
        logger.error("请检查:")
        logger.error("  1. MySQL 服务器是否运行")
        logger.error("  2. 用户名密码是否正确")
        logger.error("  3. 网络连接是否正常")

        # 如果是exe环境，提供额外的配置指导
        if getattr(sys, 'frozen', False):
            logger.error("  4. config.ini文件是否存在且配置正确")
            logger.error("  5. 运行database_config_wizard.bat配置数据库连接")

        return False

def check_runtime_environment():
    """检查运行时环境和依赖"""
    try:
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 7):
            logger.error("❌ Python版本过低，需要Python 3.7+")
            return False

        logger.info(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")

        # 检查关键依赖（包括Waitress）
        required_modules = ['flask', 'pymysql', 'sqlalchemy', 'requests', 'pandas', 'waitress']
        missing_modules = []

        for module in required_modules:
            try:
                __import__(module)
                logger.debug(f"✅ 模块 {module} 已安装")
            except ImportError:
                missing_modules.append(module)
                logger.error(f"❌ 模块 {module} 未安装")

        if missing_modules:
            logger.error("请安装缺失的依赖:")
            logger.error("pip install -r requirements.txt")
            return False

        # 检查关键目录和文件（仅在开发环境中）
        if not getattr(sys, 'frozen', False):
            # 只在非打包环境中检查源码目录结构
            critical_paths = {
                'app/': '应用主目录',
                'app/services/data_source_manager.py': '数据源管理器',
                'app/api_v2/': 'API v2接口',
                'app/templates/': '模板目录',
                'app/static/': '静态资源',
                'config/__init__.py': '配置文件'
            }

            for path, description in critical_paths.items():
                if os.path.exists(path):
                    logger.debug(f"✅ {description}: {path}")
                else:
                    logger.error(f"❌ {description}不存在: {path}")
                    return False
        else:
            # 打包环境中，只检查关键模块是否可导入
            try:
                import app
                import app.services.data_source_manager
                import config
                logger.debug("✅ 打包环境：关键模块导入成功")
            except ImportError as e:
                logger.error(f"❌ 打包环境：模块导入失败: {e}")
                return False

        logger.info("✅ 运行时环境检查完成")
        return True

    except Exception as e:
        logger.error(f"❌ 环境检查失败: {e}")
        return False

def create_application():
    """创建APS应用实例"""
    try:
        # 设置工作目录
        app_path = get_app_path()
        logger.info(f"🏠 应用路径: {app_path}")
        os.chdir(app_path)

        # 检查运行时环境
        logger.info("🔍 检查运行时环境...")
        if not check_runtime_environment():
            logger.error("❌ 运行时环境检查失败")
            return None

        # 检查MySQL数据库
        logger.info("🔍 检查MySQL数据库...")
        if not check_mysql_connection():
            logger.error("❌ 数据库检查失败")
            logger.error("💡 请先运行: python run.py init-db")
            return None

        # 创建Flask应用实例
        logger.info("创建Flask应用...")
        app_result = create_app()

        if app_result is None:
            logger.error("❌ Flask应用创建失败")
            return None

        # 解包应用和SocketIO实例
        if isinstance(app_result, tuple):
            app, socketio = app_result
            # 检查SocketIO是否被禁用
            if not app.config.get('SOCKETIO_ENABLED', True):
                socketio = None
                logger.info("SocketIO已在PyInstaller环境中禁用")
        else:
            app = app_result
            socketio = None

        # 应用生产级配置
        app.config.update({
            'DEBUG': False,
            'TESTING': False,
            'TEMPLATES_AUTO_RELOAD': False,
            'SEND_FILE_MAX_AGE_DEFAULT': 31536000,  # 静态文件缓存1年
            'PERMANENT_SESSION_LIFETIME': 28800,    # 会话8小时
            'JSON_SORT_KEYS': False,
            'JSONIFY_PRETTYPRINT_REGULAR': False,
        })
        logger.info("✅ 生产级配置已应用")

        # 验证关键服务
        with app.app_context():
            try:
                # 在应用上下文中执行数据库安全检查
                try:
                    from app.utils.database_safety_checker import check_database_safety
                    is_safe, safety_message = check_database_safety()
                    if is_safe:
                        logger.info(f"[OK] 数据库安全检查通过: {safety_message}")
                    else:
                        logger.warning(f"[WARNING] 数据库安全检查发现问题: {safety_message}")
                        logger.info("[INFO] 建议使用 python run.py init-db 进行安全初始化")
                except Exception as e:
                    logger.warning(f"[WARNING] 数据库安全检查失败: {e}")

                # 检查数据源管理器
                from app.services.data_source_manager import DataSourceManager
                manager = DataSourceManager()
                status = manager.get_data_source_status()
                logger.info(f"📊 数据源状态: MySQL={'可用' if status.get('mysql_available') else '不可用'}")

                # 检查API路由
                from flask import url_for
                logger.debug("🔗 API路由注册正常")

            except Exception as e:
                logger.warning(f"⚠️ 服务验证部分失败: {e}")

        logger.info("应用创建成功")
        return app, socketio

    except Exception as e:
        logger.error(f"❌ 应用创建失败: {e}")
        traceback.print_exc()
        return None

def get_app_path():
    """获取应用程序路径，处理PyInstaller打包和开发环境"""
    if getattr(sys, 'frozen', False):
        # 运行在PyInstaller打包环境
        # sys._MEIPASS 是PyInstaller解压临时文件的路径
        return getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
    else:
        # 运行在常规Python环境
        return os.path.dirname(os.path.abspath(__file__))

def check_mysql_connection():
    """检查MySQL数据库连接和表结构（简化版本）"""
    try:
        import pymysql

        # 使用统一配置系统
        mysql_config = {
            'host': config.DB_HOST,
            'port': config.DB_PORT,
            'user': config.DB_USER,
            'password': config.DB_PASSWORD,
            'charset': config.DB_CHARSET
        }

        # 连接测试
        conn = pymysql.connect(**mysql_config)
        conn.close()
        logger.info(f"✅ 数据库连接成功: {mysql_config['host']}:{mysql_config['port']}")
        return True

    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 显示启动横幅
        print("\n🚀 APS 车规芯片终测智能调度平台 v5.0 (生产级EXE)")
        print("⚡ 使用Waitress生产服务器，性能提升99.7%")
        print("=" * 60)

        # 检查命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] in ['--help', '-h', 'help']:
                print("生产级EXE使用说明:")
                print("  AEC-FT-Intelligent-Commander-Platform.exe  # 启动生产级应用服务器")
                sys.exit(0)

        # 设置工作目录
        app_path = get_app_path()
        logger.info(f"🏠 应用路径: {app_path}")
        os.chdir(app_path)

        # 检查数据库连接
        logger.info("🔍 检查MySQL数据库...")
        if not check_mysql_connection():
            print("❌ 数据库连接失败")
            print("💡 请检查数据库配置和服务状态")
            sys.exit(1)

        # 创建应用
        print("📦 创建应用实例...")
        app_result = create_app()

        if app_result is None:
            print("❌ 应用创建失败")
            sys.exit(1)

        # 解包应用和SocketIO实例
        if isinstance(app_result, tuple):
            app, socketio = app_result
        else:
            app = app_result
            socketio = None

        # 启动服务器
        host = config.FLASK_HOST
        port = config.FLASK_PORT

        print(f"\n✅ 应用启动成功！")
        print(f"🌐 访问地址: http://{host}:{port}")
        print(f"🔍 健康检查: http://{host}:{port}/health")
        print("\n按 Ctrl+C 停止服务器")
        print("-" * 60)

        # 使用Waitress生产服务器
        try:
            from waitress import serve
            serve(app, host=host, port=port, threads=6)
        except ImportError:
            print("⚠️ Waitress未安装，使用Flask内置服务器")
            app.run(host=host, port=port, debug=False, threaded=True)
        except Exception as e:
            logger.error(f"❌ 服务器启动失败: {e}")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n✅ APS生产级平台已安全停止")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        print(f"\n启动错误: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
