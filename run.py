#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 车规芯片终测智能调度平台 - 应用启动脚本
版本: v2.0 (Flask + MySQL 架构)
"""

import sys
import os
import io
import logging
import webbrowser
from threading import Timer
import traceback

# Windows环境编码修复 - 必须在最开头
if sys.platform.startswith('win'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

# 在导入app之前设置基础日志级别
logging.getLogger().setLevel(logging.WARNING)

# 设置静默启动环境变量
os.environ['FLASK_QUIET_STARTUP'] = '1'

# 临时屏蔽第三方库和app模块的INFO级别日志
for logger_name in ['app', 'app.models', 'app.services', 
                   'app.services.real_scheduling_service',
                   'app.utils.enhanced_cache_sync',
                   'werkzeug', 'flask', 'urllib3', 
                   'socketio', 'engineio']:
    logging.getLogger(logger_name).setLevel(logging.WARNING)

from app import create_app

# 导入新的统一配置管理器
from config.aps_config import config

# 确保必要目录存在 - 使用配置化路径
critical_dirs = [
    config.LOG_DIR,
    config.INSTANCE_DIR,
    config.STATIC_EXPORTS_DIR,
    config.DOWNLOAD_DIR,
    config.UPLOAD_DIR
]
for directory in critical_dirs:
    try:
        os.makedirs(directory, exist_ok=True)
        logging.debug(f"确保目录存在: {directory}")
    except Exception as e:
        logging.warning(f"创建目录失败 {directory}: {e}")

# 配置日志系统 - 使用配置化路径
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.WARNING if config.QUIET_STARTUP else getattr(logging, config.LOG_LEVEL))

# 创建日志文件处理器 - 支持exe环境
try:
    handlers_list = [console_handler]
    
    # 为exe环境特殊处理日志配置
    if getattr(sys, 'frozen', False):
        # exe环境：优先使用exe目录下的logs文件夹
        exe_dir = os.path.dirname(sys.executable)
        exe_logs_dir = os.path.join(exe_dir, 'logs')
        os.makedirs(exe_logs_dir, exist_ok=True)
        
        exe_log_file = os.path.join(exe_logs_dir, 'app.log')
        exe_file_handler = logging.FileHandler(exe_log_file, encoding='utf-8')
        exe_file_handler.setLevel(logging.INFO)  # exe环境使用INFO级别
        
        handlers_list.append(exe_file_handler)
        print(f"[EXE] Log file: {exe_log_file}")
        
        # 同时也创建项目根目录的日志（如果可能）
        try:
            if not os.path.exists(config.LOG_DIR):
                os.makedirs(config.LOG_DIR, exist_ok=True)
            
            log_file_path = config.get_log_file_path('app.log')
            file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)  # 项目日志保留详细信息
            handlers_list.append(file_handler)
        except Exception as fallback_error:
            print(f"[WARN] Project logs fallback failed: {fallback_error}")
    else:
        # 开发环境：使用项目根目录的logs
        if not os.path.exists(config.LOG_DIR):
            os.makedirs(config.LOG_DIR, exist_ok=True)
        
        log_file_path = config.get_log_file_path('app.log')
        file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)  # 文件保留详细信息
        handlers_list.append(file_handler)
        
except Exception as e:
    # 如果文件日志失败，只使用控制台
    print(f"[ERROR] Log configuration failed: {e}, using console only")
    handlers_list = [console_handler]

# 清除现有的处理器，避免重复配置
root_logger = logging.getLogger()
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

logging.basicConfig(
    level=logging.INFO,  # 根日志器设为INFO，避免过多DEBUG信息
    format='%(asctime)s | %(levelname)-8s | %(message)s',   # 标准化格式
    datefmt='%H:%M:%S',
    handlers=handlers_list
)

# 添加用户友好的日志过滤器
class UserFriendlyFilter(logging.Filter):
    def filter(self, record):
        # 更严格的过滤，只保留关键错误和用户需要的信息
        message = record.getMessage()
        
        # 允许通过的关键信息（错误、警告、用户操作结果）
        allow_patterns = [
            r'启动失败', r'连接失败', r'ERROR', r'CRITICAL',
            r'默认账户', r'数据库.*失败', r'缺少.*模块',
            r'请运行', r'请检查', r'解决建议',
            r'系统启动失败', r'数据库初始化', r'迁移.*完成'
        ]
        
        # 检查是否为关键信息
        import re
        for pattern in allow_patterns:
            if re.search(pattern, message):
                return True
        
        # 过滤掉所有其他技术信息
        return False

logger = logging.getLogger('APS-Platform')

# 为控制台处理器添加过滤器（过滤技术信息）
console_handler.addFilter(UserFriendlyFilter())

# 设置第三方库日志级别，减少噪音
logging.getLogger('werkzeug').setLevel(logging.WARNING)
logging.getLogger('engineio').setLevel(logging.ERROR)
logging.getLogger('socketio').setLevel(logging.ERROR)
logging.getLogger('apscheduler').setLevel(logging.WARNING)

def get_app_path():
    """获取应用程序路径，处理PyInstaller打包和开发环境"""
    if getattr(sys, 'frozen', False):
        # 运行在PyInstaller打包环境
        # sys._MEIPASS 是PyInstaller解压临时文件的路径
        return getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
    else:
        # 运行在常规Python环境
        return os.path.dirname(os.path.abspath(__file__))

def check_mysql_connection():
    """检查MySQL数据库连接和表结构"""
    try:
        # 导入必要模块
        import pymysql
        
        # 使用新的统一配置系统（自动处理配置优先级：config.ini > 环境变量 > 默认值）
        try:
            mysql_config = {
                'host': config.DB_HOST,
                'port': config.DB_PORT,
                'user': config.DB_USER,
                'password': config.DB_PASSWORD,
                'charset': config.DB_CHARSET
            }
            config_source = f"统一配置系统 ({config.DB_HOST}:{config.DB_PORT})"
            logger.info(f"使用统一配置系统: {config.DB_HOST}:{config.DB_PORT}")
        except (AttributeError, Exception) as e:
            logger.error(f"统一配置系统失败: {e}")
            if getattr(sys, 'frozen', False):
                raise Exception("exe环境必须提供config.ini配置文件！请检查config.ini是否存在。")
            # 仅开发环境使用硬编码默认值
            mysql_config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': 'WWWwww123!',
                'charset': 'utf8mb4'
            }
            config_source = f"开发环境默认配置 (localhost:3306)"
            logger.warning(f"回退到开发环境默认配置")
        
        # 简化输出，只在有问题时显示配置信息
        logger.debug(f"Database config source: {config_source}")
        
        # 需要检查的数据库和关键表 - 单数据库模式（Linux MySQL区分大小写，统一使用小写）
        database_checks = {
            'aps': [
                # 业务表（统一使用小写表名，兼容Linux MySQL）
                'et_wait_lot', 'wip_lot', 'eqp_status', 'et_ft_test_spec',
                'et_uph_eqp', 'ct', 'tcc_inv', 'lotprioritydone',
                # 系统表（已迁移到aps数据库）
                'users', 'user_permissions', 
                'devicepriorityconfig', 'lotpriorityconfig'
            ]
        }
        
        # 连接MySQL服务器
        try:
            conn = pymysql.connect(**mysql_config)
            cursor = conn.cursor()
            logger.debug(f"Database connection successful: {mysql_config['host']}:{mysql_config['port']}")
        except Exception as conn_error:
            print(f"[ERROR] Database connection failed: {mysql_config['host']}:{mysql_config['port']}")
            print(f"   Error message: {conn_error}")
            
            # 提供详细的故障排除指导
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe
                print("\n[TROUBLESHOOTING] For EXE deployment mode:")
                print("1. Check if config.ini file exists in the same directory as the exe file")
                print("2. Confirm the database server address in config.ini is correct")
                print("3. Confirm MySQL server is running and accessible")
                print("4. Run database_config_wizard.bat to reconfigure database connection")
                print("5. Check firewall settings to allow connections")
            else:
                # 开发环境
                print("\n[TROUBLESHOOTING] For development environment:")
                print("1. Check if MySQL service is running")
                print("2. Confirm username and password are correct")
                print("3. Check network connection")
                print("4. Confirm MySQL port 3306 is open")
            
            raise conn_error
        
        missing_items = []
        
        for db_name, required_tables in database_checks.items():
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            if not cursor.fetchone():
                logger.error(f"数据库 '{db_name}' 不存在")
                missing_items.append(f"数据库: {db_name}")
                continue
            
            logger.info(f"数据库 '{db_name}' 存在")
            
            # 检查关键表是否存在
            cursor.execute(f"USE {db_name}")
            for table_name in required_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if cursor.fetchone():
                    logger.debug(f"表 '{db_name}.{table_name}' 存在")
                else:
                    logger.warning(f"表 '{db_name}.{table_name}' 不存在")
                    missing_items.append(f"表: {db_name}.{table_name}")
        
        conn.close()
        
        if missing_items:
            logger.error("数据库检查发现问题:")
            for item in missing_items:
                logger.error(f"   - {item}")
            logger.error("请运行: python run.py init-db")
            return False
        
        # 执行数据库安全检查
        try:
            logger.info("执行数据库安全检查...")
            # 将安全检查移到应用创建后执行，这样就有应用上下文了
            # 这里只做基本的连接检查，详细的安全检查在应用启动后进行
            logger.info("[INFO] 数据库基本连接检查完成，详细安全检查将在应用启动后进行")
                
        except Exception as e:
            logger.warning(f"[WARNING] 数据库安全检查失败: {e}")
            logger.info("[INFO] 安全检查失败不影响系统运行")
        
        logger.info("MySQL 数据库和表结构检查完成")
        return True
        
    except ImportError as e:
        logger.error(f"缺少必要模块: {e}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    except Exception as e:
        logger.error(f"MySQL 数据库检查失败: {e}")
        logger.error("请检查:")
        logger.error("  1. MySQL 服务器是否运行")
        logger.error("  2. 用户名密码是否正确")
        logger.error("  3. 网络连接是否正常")
        
        # 如果是exe环境，提供额外的配置指导
        if getattr(sys, 'frozen', False):
            logger.error("  4. config.ini文件是否存在且配置正确")
            logger.error("  5. 运行database_config_wizard.bat配置数据库连接")
        
        return False

def check_runtime_environment():
    """检查运行时环境和依赖"""
    try:
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 7):
            logger.error("Python版本过低，需要Python 3.7+")
            return False
        
        logger.info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查关键依赖
        required_modules = ['flask', 'pymysql', 'sqlalchemy', 'requests', 'pandas']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                logger.debug(f"模块 {module} 已安装")
            except ImportError:
                missing_modules.append(module)
                logger.error(f"模块 {module} 未安装")
        
        if missing_modules:
            logger.error("请安装缺失的依赖:")
            logger.error("pip install -r requirements.txt")
            return False
        
        # 检查关键目录和文件（仅在开发环境中）
        if not getattr(sys, 'frozen', False):
            # 只在非打包环境中检查源码目录结构
            critical_paths = {
                'app/': '应用主目录',
                'app/services/data_source_manager.py': '数据源管理器',
                'app/api_v2/': 'API v2接口',
                'app/templates/': '模板目录',
                'app/static/': '静态资源',
                'config/__init__.py': '配置文件'
            }
            
            for path, description in critical_paths.items():
                if os.path.exists(path):
                    logger.debug(f"{description}: {path}")
                else:
                    logger.error(f"{description}不存在: {path}")
                    return False
        else:
            # 打包环境中，只检查关键模块是否可导入
            try:
                import app
                import app.services.data_source_manager
                import config
                logger.debug("打包环境：关键模块导入成功")
            except ImportError as e:
                logger.error(f"打包环境：模块导入失败: {e}")
                return False
        
        logger.info("运行时环境检查完成")
        return True
        
    except Exception as e:
        logger.error(f"环境检查失败: {e}")
        return False

def create_application():
    """创建APS应用实例"""
    try:
        # 设置工作目录
        app_path = get_app_path()
        logger.info(f"应用路径: {app_path}")
        os.chdir(app_path)
        
        # 检查运行时环境
        logger.info("检查运行时环境...")
        if not check_runtime_environment():
            logger.error("运行时环境检查失败")
            return None
        
        # 检查MySQL数据库
        logger.info("检查MySQL数据库...")
        if not check_mysql_connection():
            logger.error("数据库检查失败")
            logger.error("请先运行: python run.py init-db")
            return None
        
        # 创建Flask应用实例
        logger.info("创建Flask应用...")
        app_result = create_app()
        
        if app_result is None:
            logger.error("Flask应用创建失败")
            return None
        
        # 解包应用和SocketIO实例
        if isinstance(app_result, tuple):
            app, socketio = app_result
            # 检查SocketIO是否被禁用
            if not app.config.get('SOCKETIO_ENABLED', True):
                socketio = None
                logger.info("SocketIO已在PyInstaller环境中禁用")
        else:
            app = app_result
            socketio = None
        
        # 验证关键服务
        with app.app_context():
            try:
                # 在应用上下文中执行数据库安全检查
                try:
                    from app.utils.database_safety_checker import check_database_safety
                    is_safe, safety_message = check_database_safety()
                    if is_safe:
                        logger.info(f"[OK] 数据库安全检查通过: {safety_message}")
                    else:
                        logger.warning(f"[WARNING] 数据库安全检查发现问题: {safety_message}")
                        logger.info("[INFO] 建议使用 python run.py init-db 进行安全初始化")
                except Exception as e:
                    logger.warning(f"[WARNING] 数据库安全检查失败: {e}")
                
                # 检查数据源管理器
                from app.services.data_source_manager import DataSourceManager
                manager = DataSourceManager()
                status = manager.get_data_source_status()
                logger.info(f"数据源状态: MySQL={'可用' if status.get('mysql_available') else '不可用'}")
                
                # 检查API路由
                from flask import url_for
                logger.debug("API路由注册正常")
                
            except Exception as e:
                logger.warning(f"服务验证部分失败: {e}")
        
        logger.info("应用创建成功")
        return app, socketio
        
    except Exception as e:
        logger.error(f"应用创建失败: {e}")
        traceback.print_exc()
        return None

def main():
    """主启动函数"""
    try:
        # 简化启动横幅
        logger.debug("APS 车规芯片终测智能调度平台 v2.0")
                
        # 检查命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] == 'init-db':
                logger.info("正在初始化MySQL数据库...")
                try:
                    from init_db import main as init_main
                    success = init_main()
                    if success:
                        logger.info("数据库初始化完成")
                        print("\n数据库初始化成功！现在可以启动应用:")
                        print("   python run.py")
                    else:
                        logger.error("数据库初始化失败")
                    sys.exit(0 if success else 1)
                except ImportError as e:
                    logger.error(f"无法导入初始化模块: {e}")
                    sys.exit(1)
                    
            elif sys.argv[1] in ['--help', '-h', 'help']:
                print("使用说明:")
                print("  python run.py              # 启动应用服务器")
                print("  python run.py init-db      # 初始化MySQL数据库")
                sys.exit(0)
                
            elif sys.argv[1] == 'migrate':
                logger.info("执行数据库迁移...")
                try:
                    from migrate_priority_tables import migrate_priority_tables
                    migrate_priority_tables()
                    logger.info("数据库迁移完成")
                    sys.exit(0)
                except Exception as e:
                    logger.error(f"数据库迁移失败: {e}")
                    sys.exit(1)
        
        # 创建应用实例
        logger.debug("正在初始化系统...")
        app_result = create_application()
        if app_result is None:
            print("系统启动失败")
            print("\n解决建议:")
            print("1. 确保MySQL服务正在运行")
            print("2. 检查数据库连接配置")
            print("3. 运行数据库初始化: python run.py init-db")
            print("4. 查看详细日志: logs/app.log")
            sys.exit(1)
        
        # 解包应用和SocketIO实例
        if isinstance(app_result, tuple):
            app, socketio = app_result
        else:
            app = app_result
            socketio = None
        
        # 启动服务器 - 使用新的统一配置系统
        host = config.FLASK_HOST  # 自动处理配置优先级：config.ini > 环境变量 > 默认值
        port = config.FLASK_PORT  
        logger.info(f"使用统一配置系统的服务器配置: {host}:{port}")
        
        # 为浏览器访问准备正确的地址
        browser_host = 'localhost' if host == '0.0.0.0' else host
        
        # 只显示关键的访问信息
        print(f"\n✅ 系统启动成功！访问地址: http://localhost:{port}")
        print("默认账户: admin / admin")
        print("按 Ctrl+C 停止服务")
        
        # 判断运行环境
        is_production = getattr(sys, 'frozen', False)
        if not is_production:
            # 开发环境：延迟打开浏览器
            Timer(2.0, lambda: webbrowser.open(f'http://{browser_host}:{port}')).start()
        
        # 启动Flask应用
        if socketio:
            # 使用SocketIO运行，支持WebSocket
            socketio.run(
                app,
                host=host,
                port=port,
                debug=False,
                use_reloader=False,
                log_output=True,
                allow_unsafe_werkzeug=True  # 生产环境必需
            )
        else:
            # fallback到普通Flask
            app.run(
                host=host,
                port=port,
                debug=False,
                threaded=True,
                use_reloader=False  # 避免重载器问题
            )
        
    except KeyboardInterrupt:
        print("\nAPS平台已安全停止")
        sys.exit(0)
    except Exception as e:
        logger.error(f"启动失败: {e}")
        print(f"\n启动错误: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main() 