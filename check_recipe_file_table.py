#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查recipe_file表的实际数据情况
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('check_recipe_table.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('RecipeTableChecker')

def check_recipe_file_table():
    """检查recipe_file表的实际数据"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            
            data_manager = DataSourceManager()
            
            print("="*80)
            print("🔍 recipe_file表数据检查")
            print("="*80)
            
            # ==================== 检查所有可能的recipe表名 ====================
            print(f"\n📊 检查所有可能的recipe相关表...")
            
            # 获取数据库中所有表名
            try:
                from sqlalchemy import text
                db_engine = data_manager.get_db_engine()
                
                with db_engine.connect() as conn:
                    result = conn.execute(text("SHOW TABLES"))
                    all_tables = [row[0] for row in result.fetchall()]
                    
                print(f"   数据库中所有表: {len(all_tables)} 个")
                
                # 找出包含recipe的表
                recipe_tables = [table for table in all_tables if 'recipe' in table.lower()]
                print(f"   包含recipe的表: {recipe_tables}")
                
                # 找出包含file的表  
                file_tables = [table for table in all_tables if 'file' in table.lower()]
                print(f"   包含file的表: {file_tables}")
                
                # 可能的recipe表名变体
                possible_recipe_tables = [
                    'recipe_file',
                    'et_recipe_file', 
                    'recipe_files',
                    'ET_RECIPE_FILE',
                    'RECIPE_FILE'
                ]
                
                print(f"\n   检查可能的recipe表名:")
                existing_recipe_tables = []
                for table_name in possible_recipe_tables:
                    if table_name in all_tables:
                        existing_recipe_tables.append(table_name)
                        print(f"     ✅ {table_name} - 存在")
                    else:
                        print(f"     ❌ {table_name} - 不存在")
                
            except Exception as e:
                print(f"   获取表列表失败: {e}")
                existing_recipe_tables = ['recipe_file']  # 默认假设存在
            
            # ==================== 检查每个找到的recipe表 ====================
            if not existing_recipe_tables:
                print(f"\n❌ 没有找到任何recipe相关的表！")
                return False
            
            for table_name in existing_recipe_tables:
                print(f"\n🔍 检查表: {table_name}")
                
                try:
                    # 使用data_manager获取数据
                    if table_name.upper() == 'ET_RECIPE_FILE':
                        result = data_manager.get_table_data('et_recipe_file')
                    else:
                        result = data_manager.get_table_data(table_name)
                    
                    recipe_data = result.get('data', [])
                    
                    print(f"   记录数: {len(recipe_data)}")
                    
                    if len(recipe_data) > 0:
                        print(f"   ✅ 表有数据！")
                        
                        # 分析数据质量
                        sample_record = recipe_data[0]
                        print(f"   字段列表: {list(sample_record.keys())}")
                        
                        # 统计关键字段
                        device_count = sum(1 for r in recipe_data if r.get('DEVICE'))
                        stage_count = sum(1 for r in recipe_data if r.get('STAGE')) 
                        handler_config_count = sum(1 for r in recipe_data if r.get('HANDLER_CONFIG'))
                        approval_count = sum(1 for r in recipe_data if r.get('APPROVAL_STATE'))
                        
                        print(f"   数据质量分析:")
                        print(f"     - 有DEVICE字段: {device_count}")
                        print(f"     - 有STAGE字段: {stage_count}")
                        print(f"     - 有HANDLER_CONFIG字段: {handler_config_count}")
                        print(f"     - 有APPROVAL_STATE字段: {approval_count}")
                        
                        # 审批状态分布
                        approval_states = defaultdict(int)
                        for record in recipe_data:
                            state = (record.get('APPROVAL_STATE') or '').strip()
                            approval_states[state or '空'] += 1
                        
                        print(f"   审批状态分布:")
                        for state, count in sorted(approval_states.items(), key=lambda x: x[1], reverse=True):
                            print(f"     - {state}: {count} 条")
                        
                        # 显示前几条记录
                        print(f"\n   前3条记录示例:")
                        for i, record in enumerate(recipe_data[:3], 1):
                            device = (record.get('DEVICE') or '').strip()
                            stage = (record.get('STAGE') or '').strip()
                            handler_config = (record.get('HANDLER_CONFIG') or '').strip()
                            approval = (record.get('APPROVAL_STATE') or '').strip()
                            
                            print(f"     {i}. DEVICE='{device}', STAGE='{stage}', HANDLER_CONFIG='{handler_config}', APPROVAL='{approval}'")
                    else:
                        print(f"   ❌ 表为空！")
                        
                except Exception as e:
                    print(f"   查询失败: {e}")
            
            # ==================== 检查DataSourceManager的get_table_data实现 ====================
            print(f"\n🔍 检查DataSourceManager的get_table_data方法")
            
            # 检查get_table_data方法如何处理recipe_file
            try:
                # 直接调用原始方法
                print(f"   测试data_manager.get_table_data('recipe_file')...")
                result1 = data_manager.get_table_data('recipe_file')
                print(f"   结果: {len(result1.get('data', []))} 条记录")
                
                print(f"   测试data_manager.get_table_data('et_recipe_file')...")
                result2 = data_manager.get_table_data('et_recipe_file')
                print(f"   结果: {len(result2.get('data', []))} 条记录")
                
            except Exception as e:
                print(f"   测试失败: {e}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = check_recipe_file_table()
    if success:
        print("\n🎉 recipe_file表检查完成！")
    else:
        print("\n❌ 检查失败！")

if __name__ == "__main__":
    main()