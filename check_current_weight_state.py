#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前权重配置的实际状态
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
from datetime import datetime

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('check_current_weight_state.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('WeightStateChecker')

def check_weight_configuration_state():
    """检查当前权重配置状态"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.models import SchedulingConfig
            from app import db
            
            logger.info("🔍 检查当前所有活跃配置...")
            
            # 查询所有活跃配置
            active_configs = SchedulingConfig.query.filter_by(is_active=True).all()
            
            logger.info(f"📊 当前活跃配置总数: {len(active_configs)}")
            
            # 按策略分组显示
            strategy_configs = {}
            for config in active_configs:
                strategy = config.strategy_name
                if strategy not in strategy_configs:
                    strategy_configs[strategy] = {'default': None, 'users': []}
                
                if config.user_id is None:
                    strategy_configs[strategy]['default'] = config
                else:
                    strategy_configs[strategy]['users'].append(config)
            
            for strategy_name, configs in strategy_configs.items():
                logger.info(f"\n--- 策略: {strategy_name} ---")
                
                # 默认配置
                default_config = configs['default']
                if default_config:
                    logger.info(f"默认配置 (ID={default_config.id}):")
                    logger.info(f"  技术匹配: {default_config.tech_match_weight}%")
                    logger.info(f"  负载均衡: {default_config.load_balance_weight}%") 
                    logger.info(f"  截止时间: {default_config.deadline_weight}%")
                    logger.info(f"  更新时间: {default_config.updated_at}")
                else:
                    logger.warning(f"❌ 没有找到 {strategy_name} 策略的默认配置")
                
                # 用户配置
                user_configs = configs['users']
                if user_configs:
                    logger.info(f"用户配置 ({len(user_configs)} 个):")
                    for user_config in user_configs:
                        logger.info(f"  用户: {user_config.user_id} (ID={user_config.id})")
                        logger.info(f"    技术匹配: {user_config.tech_match_weight}%")
                        logger.info(f"    负载均衡: {user_config.load_balance_weight}%")
                        logger.info(f"    截止时间: {user_config.deadline_weight}%")
                        logger.info(f"    更新时间: {user_config.updated_at}")
                else:
                    logger.info("  无用户特定配置")
            
            logger.info("\n🔍 测试配置获取API逻辑...")
            
            # 测试各种情况下的配置获取
            test_cases = [
                ('admin', 'intelligent'),
                (None, 'intelligent'),
                ('admin', 'deadline'),
                (None, 'deadline')
            ]
            
            for user_id, strategy in test_cases:
                logger.info(f"\n--- 测试: user_id={user_id}, strategy={strategy} ---")
                
                # 使用get_active_config方法
                config = SchedulingConfig.get_active_config(user_id=user_id, strategy_name=strategy)
                if config:
                    logger.info(f"get_active_config 返回: ID={config.id}, user_id={config.user_id}")
                    logger.info(f"  技术匹配: {config.tech_match_weight}%")
                    logger.info(f"  负载均衡: {config.load_balance_weight}%")
                    logger.info(f"  截止时间: {config.deadline_weight}%")
                else:
                    logger.info("get_active_config 返回: None")
                
                # 使用get_strategy_weights方法
                weights = SchedulingConfig.get_strategy_weights(strategy_name=strategy, user_id=user_id)
                if weights:
                    logger.info(f"get_strategy_weights 返回:")
                    logger.info(f"  ID: {weights.get('id')}, user_id: {weights.get('user_id')}")
                    logger.info(f"  技术匹配: {weights.get('tech_match_weight')}%")
                    logger.info(f"  负载均衡: {weights.get('load_balance_weight')}%")  
                    logger.info(f"  截止时间: {weights.get('deadline_weight')}%")
                else:
                    logger.info("get_strategy_weights 返回: None")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = check_weight_configuration_state()
    print("🎉 检查: 通过" if success else "❌ 检查: 失败")

if __name__ == "__main__":
    main()