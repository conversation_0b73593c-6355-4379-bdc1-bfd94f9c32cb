<!DOCTYPE html>
<html>
<head>
    <title>调整模式调试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>调整模式数据加载调试</h1>
    <button onclick="testAdjustModeLoading()">测试调整模式加载</button>
    <div id="results"></div>

    <script>
        // 从原页面复制的常量
        const API_ENDPOINT = '/api/v2/production/done-lots';
        const TABLE_NAME = 'lotprioritydone';
        
        async function testAdjustModeLoading() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>开始测试...</p>';
            
            try {
                console.log('🚀 开始测试调整模式数据加载...');
                
                // 测试基本的成功批次API
                console.log('1. 测试基本API调用...');
                const basicResponse = await fetch(`${API_ENDPOINT}?table=${TABLE_NAME}&page=1&size=50`);
                console.log('基本API响应状态:', basicResponse.status);
                
                if (!basicResponse.ok) {
                    throw new Error(`基本API失败: HTTP ${basicResponse.status}`);
                }
                
                const basicData = await basicResponse.json();
                console.log('基本API数据:', basicData);
                
                resultsDiv.innerHTML += `<p>✅ 基本API测试成功: ${basicData.data?.length || 0} 条数据</p>`;
                
                // 测试失败批次API
                console.log('2. 测试失败批次API...');
                try {
                    const failedResponse = await fetch('/api/v2/production/get-failed-lots-from-logs?current_only=true&hours=24');
                    console.log('失败批次API响应状态:', failedResponse.status);
                    
                    if (failedResponse.ok) {
                        const failedData = await failedResponse.json();
                        console.log('失败批次数据:', failedData);
                        resultsDiv.innerHTML += `<p>✅ 失败批次API测试成功</p>`;
                    } else {
                        console.warn('失败批次API失败，但这是可选的');
                        resultsDiv.innerHTML += `<p>⚠️ 失败批次API失败 (HTTP ${failedResponse.status})，但不影响主要功能</p>`;
                    }
                } catch (failedError) {
                    console.warn('失败批次API异常:', failedError);
                    resultsDiv.innerHTML += `<p>⚠️ 失败批次API异常: ${failedError.message}</p>`;
                }
                
                // 测试大数据量请求
                console.log('3. 测试大数据量请求...');
                const largeResponse = await fetch(`${API_ENDPOINT}?table=${TABLE_NAME}&page=1&size=500`);
                console.log('大数据量响应状态:', largeResponse.status);
                
                if (!largeResponse.ok) {
                    throw new Error(`大数据量请求失败: HTTP ${largeResponse.status}`);
                }
                
                const largeData = await largeResponse.json();
                console.log('大数据量结果:', {
                    success: largeData.success,
                    dataLength: largeData.data?.length || 0,
                    total: largeData.pagination?.total || largeData.total,
                    message: largeData.message
                });
                
                resultsDiv.innerHTML += `<p>✅ 大数据量测试成功: ${largeData.data?.length || 0} 条数据</p>`;
                resultsDiv.innerHTML += `<p>📊 真实总数: ${largeData.pagination?.total || largeData.total || '未知'} 条</p>`;
                
                resultsDiv.innerHTML += `<p>🎉 所有测试完成！调整模式数据加载应该正常工作。</p>`;
                
            } catch (error) {
                console.error('❌ 测试失败:', error);
                resultsDiv.innerHTML += `<p style="color: red;">❌ 测试失败: ${error.message}</p>`;
                resultsDiv.innerHTML += `<p style="color: red;">详细错误: ${error.stack}</p>`;
            }
        }
    </script>
</body>
</html>