#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定性测试框架快速验证
"""

# 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import json

# 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_framework():
    """测试框架组件"""
    
    print("🧪 稳定性测试框架验证")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 4
    
    # 测试1: 配置文件
    try:
        with open('stability_test_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ 配置文件加载成功")
        print(f"  测试时长: {config['test_duration']['full_test_hours']} 小时")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
    
    # 测试2: 运行器文件
    try:
        if os.path.exists('stability_test_runner.py'):
            print("✅ 稳定性测试运行器文件存在")
            tests_passed += 1
        else:
            print("❌ 稳定性测试运行器文件不存在")
    except Exception as e:
        print(f"❌ 运行器文件检查失败: {e}")
    
    # 测试3: 基准测试文件
    try:
        if os.path.exists('performance_benchmark.py'):
            print("✅ 性能基准测试文件存在")
            tests_passed += 1
        else:
            print("❌ 性能基准测试文件不存在")
    except Exception as e:
        print(f"❌ 基准测试文件检查失败: {e}")
    
    # 测试4: 框架报告
    try:
        if os.path.exists('stability_testing_framework_report.json'):
            with open('stability_testing_framework_report.json', 'r', encoding='utf-8') as f:
                report = json.load(f)
            print("✅ 框架部署报告存在")
            print(f"  框架组件: {len(report['framework_components'])} 个")
            tests_passed += 1
        else:
            print("❌ 框架部署报告不存在")
    except Exception as e:
        print(f"❌ 框架报告检查失败: {e}")
    
    print("\n" + "=" * 40)
    print(f"验证结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 稳定性测试框架验证通过")
        return True
    else:
        print("⚠️ 稳定性测试框架部分组件异常")
        return False

if __name__ == "__main__":
    success = test_framework()
    print("\n✅ 框架验证完成" if success else "\n❌ 框架验证未完全通过")