#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面功能测试 - 排产失败批次处理系统
综合测试所有修复的功能是否正常工作

使用标准化测试脚本模板，确保Flask上下文正确管理
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
import time

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('comprehensive_failed_lots_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('ComprehensiveFailedLotsTest')

def test_database_table():
    """测试数据库表"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("🔍 测试数据库表结构...")
                
                # 检查表是否存在
                check_table_sql = """
                SELECT COUNT(*) as count
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
                """
                cursor.execute(check_table_sql)
                result = cursor.fetchone()
                
                table_exists = (result['count'] if isinstance(result, dict) else result[0]) > 0
                
                if not table_exists:
                    logger.error("❌ scheduling_failed_lots表不存在")
                    return False
                
                # 检查字段
                describe_sql = "DESCRIBE scheduling_failed_lots"
                cursor.execute(describe_sql)
                columns = cursor.fetchall()
                
                required_fields = ['lot_id', 'device', 'stage', 'failure_reason', 'suggestion', 'timestamp']
                existing_fields = [col['Field'] if isinstance(col, dict) else col[0] for col in columns]
                
                missing_fields = [field for field in required_fields if field not in existing_fields]
                if missing_fields:
                    logger.error(f"❌ 缺少必需字段: {missing_fields}")
                    return False
                
                # 检查索引
                show_indexes_sql = "SHOW INDEX FROM scheduling_failed_lots"
                cursor.execute(show_indexes_sql)
                indexes = cursor.fetchall()
                
                index_count = len(indexes)
                logger.info(f"✅ 表结构检查通过，包含 {len(existing_fields)} 个字段，{index_count} 个索引")
                
                cursor.close()
                return True
                
    except Exception as e:
        logger.error(f"❌ 数据库表测试失败: {e}")
        return False

def test_failure_tracker():
    """测试失败跟踪器"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from scheduling_failure_fix import SchedulingFailureTracker
            
            logger.info("🔧 测试失败跟踪器...")
            
            # 创建跟踪器实例
            tracker = SchedulingFailureTracker()
            
            # 添加测试记录
            test_lot = {
                'LOT_ID': 'COMPREHENSIVE_TEST_001',
                'DEVICE': 'TestDevice_Comprehensive',
                'STAGE': 'FT',
                'GOOD_QTY': 5000,
                'PKG_PN': 'TEST_PKG'
            }
            
            tracker.add_failed_lot(
                lot=test_lot,
                failure_reason="综合测试失败",
                failure_details="这是全面功能测试的失败记录",
                algorithm_version="v2.0",
                session_id="COMPREHENSIVE_TEST_SESSION"
            )
            
            # 保存到数据库
            save_result = tracker.save_to_database()
            
            logger.info("✅ 失败跟踪器测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 失败跟踪器测试失败: {e}")
        return False

def test_api_functions():
    """测试API功能"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.api_v2.production.done_lots_api import get_failed_lots_from_logs
            
            logger.info("🌐 测试API功能...")
            
            # 创建模拟请求
            class MockRequest:
                def __init__(self):
                    self.args = {'current_only': 'false', 'hours': '24'}
            
            # 测试获取失败批次API
            import app.api_v2.production.done_lots_api as api_module
            original_request = getattr(api_module, 'request', None)
            api_module.request = MockRequest()
            
            try:
                response = get_failed_lots_from_logs()
                
                if hasattr(response, 'get_json'):
                    result = response.get_json()
                else:
                    result = response[0].get_json() if isinstance(response, tuple) else response
                
                if result and result.get('success'):
                    failed_lots = result.get('data', {}).get('failed_lots', [])
                    logger.info(f"✅ API测试通过，返回 {len(failed_lots)} 条失败记录")
                    
                    # 检查是否包含测试数据
                    test_records = [lot for lot in failed_lots if lot.get('LOT_ID', '').startswith('COMPREHENSIVE_TEST_')]
                    logger.info(f"🎯 找到 {len(test_records)} 条综合测试记录")
                    
                    return True
                else:
                    logger.error(f"❌ API返回失败: {result}")
                    return False
                    
            finally:
                if original_request:
                    api_module.request = original_request
            
    except Exception as e:
        logger.error(f"❌ API功能测试失败: {e}")
        return False

def test_database_operations():
    """测试数据库操作"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("💾 测试数据库操作...")
                
                # 测试查询
                count_sql = "SELECT COUNT(*) as total FROM scheduling_failed_lots"
                cursor.execute(count_sql)
                result = cursor.fetchone()
                total_count = result['total'] if isinstance(result, dict) else result[0]
                
                logger.info(f"📊 数据库中共有 {total_count} 条失败记录")
                
                # 测试按时间查询
                time_query_sql = """
                SELECT * FROM scheduling_failed_lots 
                ORDER BY timestamp DESC 
                LIMIT 10
                """
                start_time = time.time()
                cursor.execute(time_query_sql)
                recent_records = cursor.fetchall()
                end_time = time.time()
                
                query_time = (end_time - start_time) * 1000
                logger.info(f"⚡ 时间查询性能: {query_time:.2f}ms (返回{len(recent_records)}条)")
                
                # 测试按设备查询
                device_query_sql = """
                SELECT device, COUNT(*) as count 
                FROM scheduling_failed_lots 
                GROUP BY device 
                ORDER BY count DESC 
                LIMIT 5
                """
                cursor.execute(device_query_sql)
                device_stats = cursor.fetchall()
                
                logger.info(f"📈 设备统计查询: 返回{len(device_stats)}个设备")
                for stat in device_stats:
                    if isinstance(stat, dict):
                        logger.info(f"   - {stat['device']}: {stat['count']} 条")
                    else:
                        logger.info(f"   - {stat[0]}: {stat[1]} 条")
                
                cursor.close()
                logger.info("✅ 数据库操作测试通过")
                return True
                
    except Exception as e:
        logger.error(f"❌ 数据库操作测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("⚡ 测试性能表现...")
                
                # 性能测试查询
                performance_tests = [
                    {
                        'name': '全量统计查询',
                        'sql': 'SELECT COUNT(*) FROM scheduling_failed_lots',
                        'threshold': 100  # 100ms内
                    },
                    {
                        'name': '时间倒序查询',
                        'sql': 'SELECT * FROM scheduling_failed_lots ORDER BY timestamp DESC LIMIT 50',
                        'threshold': 200  # 200ms内
                    },
                    {
                        'name': '设备筛选查询',
                        'sql': "SELECT * FROM scheduling_failed_lots WHERE device LIKE '%Test%' LIMIT 20",
                        'threshold': 150  # 150ms内
                    },
                    {
                        'name': '失败类型统计',
                        'sql': 'SELECT failure_reason, COUNT(*) FROM scheduling_failed_lots GROUP BY failure_reason',
                        'threshold': 300  # 300ms内
                    }
                ]
                
                performance_passed = 0
                for test in performance_tests:
                    start_time = time.time()
                    cursor.execute(test['sql'])
                    result = cursor.fetchall()
                    end_time = time.time()
                    
                    execution_time = (end_time - start_time) * 1000
                    threshold = test['threshold']
                    
                    if execution_time <= threshold:
                        logger.info(f"✅ {test['name']}: {execution_time:.2f}ms (目标: <{threshold}ms)")
                        performance_passed += 1
                    else:
                        logger.warning(f"⚠️ {test['name']}: {execution_time:.2f}ms (超出目标: {threshold}ms)")
                
                logger.info(f"📊 性能测试: {performance_passed}/{len(performance_tests)} 项通过")
                cursor.close()
                return performance_passed >= len(performance_tests) * 0.8  # 80%通过率
                
    except Exception as e:
        logger.error(f"❌ 性能测试失败: {e}")
        return False

def test_clear_function():
    """测试清空功能"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("🧹 测试清空功能...")
                
                # 查询测试记录数量
                test_count_sql = """
                SELECT COUNT(*) as count 
                FROM scheduling_failed_lots 
                WHERE lot_id LIKE 'COMPREHENSIVE_TEST_%'
                """
                cursor.execute(test_count_sql)
                result = cursor.fetchone()
                test_count = result['count'] if isinstance(result, dict) else result[0]
                
                if test_count > 0:
                    # 删除测试记录
                    delete_test_sql = "DELETE FROM scheduling_failed_lots WHERE lot_id LIKE 'COMPREHENSIVE_TEST_%'"
                    cursor.execute(delete_test_sql)
                    deleted_count = cursor.rowcount
                    
                    logger.info(f"✅ 清空功能测试通过，删除了 {deleted_count} 条测试记录")
                else:
                    logger.info("✅ 清空功能测试通过，无需清理测试记录")
                
                cursor.close()
                return True
                
    except Exception as e:
        logger.error(f"❌ 清空功能测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("📋 生成测试报告...")
                
                # 基本统计
                stats_queries = {
                    '总记录数': 'SELECT COUNT(*) FROM scheduling_failed_lots',
                    '索引数量': 'SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = "scheduling_failed_lots"',
                    '不同设备数': 'SELECT COUNT(DISTINCT device) FROM scheduling_failed_lots',
                    '不同工序数': 'SELECT COUNT(DISTINCT stage) FROM scheduling_failed_lots',
                    '不同失败类型数': 'SELECT COUNT(DISTINCT failure_reason) FROM scheduling_failed_lots'
                }
                
                logger.info("=" * 50)
                logger.info("📊 系统状态报告")
                logger.info("=" * 50)
                
                for desc, sql in stats_queries.items():
                    try:
                        cursor.execute(sql)
                        result = cursor.fetchone()
                        value = list(result.values())[0] if isinstance(result, dict) else result[0]
                        logger.info(f"{desc}: {value}")
                    except Exception as e:
                        logger.warning(f"{desc}: 获取失败 ({e})")
                
                # 最近记录样本
                sample_sql = """
                SELECT lot_id, device, stage, failure_reason, timestamp
                FROM scheduling_failed_lots 
                ORDER BY timestamp DESC 
                LIMIT 5
                """
                cursor.execute(sample_sql)
                samples = cursor.fetchall()
                
                if samples:
                    logger.info("\n📄 最近失败记录样本:")
                    for sample in samples:
                        if isinstance(sample, dict):
                            logger.info(f"  {sample['lot_id']}: {sample['device']} | {sample['failure_reason']}")
                        else:
                            logger.info(f"  {sample[0]}: {sample[1]} | {sample[3]}")
                
                cursor.close()
                logger.info("=" * 50)
                return True
                
    except Exception as e:
        logger.error(f"❌ 生成测试报告失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始全面功能测试...")
    
    test_results = {}
    
    # 测试1: 数据库表
    logger.info("=" * 60)
    logger.info("测试1: 数据库表结构")
    test_results['database_table'] = test_database_table()
    
    # 测试2: 失败跟踪器
    logger.info("=" * 60)
    logger.info("测试2: 失败跟踪器")
    test_results['failure_tracker'] = test_failure_tracker()
    
    # 测试3: API功能
    logger.info("=" * 60)
    logger.info("测试3: API功能")
    test_results['api_functions'] = test_api_functions()
    
    # 测试4: 数据库操作
    logger.info("=" * 60)
    logger.info("测试4: 数据库操作")
    test_results['database_operations'] = test_database_operations()
    
    # 测试5: 性能测试
    logger.info("=" * 60)
    logger.info("测试5: 性能测试")
    test_results['performance'] = test_performance()
    
    # 测试6: 清空功能
    logger.info("=" * 60)
    logger.info("测试6: 清空功能")
    test_results['clear_function'] = test_clear_function()
    
    # 生成报告
    logger.info("=" * 60)
    logger.info("生成测试报告")
    generate_test_report()
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("🎯 测试结果汇总")
    logger.info("=" * 60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    logger.info(f"\n📊 总体结果: {passed_tests}/{total_tests} 项测试通过 ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        logger.info("🎉 全面功能测试：成功")
        return True
    else:
        logger.error("❌ 全面功能测试：失败")
        return False

if __name__ == "__main__":
    success = main()
    print("🎉 全面功能测试: 成功" if success else "❌ 全面功能测试: 失败")