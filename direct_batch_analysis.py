#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接查询数据库分析YX0125GA0419批次问题
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('direct_batch_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('DirectBatchAnalysis')

def analyze_database_directly():
    """直接查询数据库分析批次问题"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入数据管理器
            from app.services.data_source_manager import DataSourceManager
            
            data_manager = DataSourceManager()
            target_lot_id = "YX0125GA0419"
            
            logger.info(f"🔍 直接查询分析批次: {target_lot_id}")
            
            # 1. 查询批次信息
            logger.info("📋 Step 1: 直接查询批次信息")
            wait_lots = data_manager.get_table_data('ET_WAIT_LOT')
            target_lot = None
            
            for lot in wait_lots.get('data', []):
                if lot.get('LOT_ID') == target_lot_id:
                    target_lot = lot
                    break
            
            if not target_lot:
                logger.error(f"❌ 未找到批次: {target_lot_id}")
                return False
            
            device = target_lot.get('DEVICE')
            stage = target_lot.get('STAGE')
            pkg_pn = target_lot.get('PKG_PN')
            
            logger.info(f"✅ 批次信息: {device} - {stage} - {pkg_pn}")
            
            # 2. 查询测试规范
            logger.info(f"\n📋 Step 2: 查询匹配的测试规范")
            test_specs = data_manager.get_table_data('ET_FT_TEST_SPEC')
            
            matching_specs = []
            for spec in test_specs.get('data', []):
                if (spec.get('DEVICE') == device and 
                    spec.get('STAGE') == stage and
                    spec.get('APPROVAL_STATE') == 'Released'):
                    matching_specs.append(spec)
            
            logger.info(f"✅ 找到 {len(matching_specs)} 个匹配的测试规范:")
            for i, spec in enumerate(matching_specs, 1):
                logger.info(f"   {i}. KIT_PN: {spec.get('KIT_PN')}")
                logger.info(f"      HB_PN: {spec.get('HB_PN')}")
                logger.info(f"      TB_PN: {spec.get('TB_PN')}")
                logger.info(f"      HANDLER_CONFIG: {spec.get('HANDLER_CONFIG')}")
                logger.info(f"      EQP_CLASS: {spec.get('EQP_CLASS')}")
                logger.info("      ---")
            
            # 3. 查询LSTR相关设备
            logger.info(f"\n📋 Step 3: 查询LSTR相关设备")
            equipment_status = data_manager.get_table_data('EQP_STATUS')
            
            lstr_equipment = []
            all_equipment = []
            
            for equipment in equipment_status.get('data', []):
                handler_id = equipment.get('HANDLER_ID', '')
                eqp_class = equipment.get('EQP_CLASS', '')
                stage_field = equipment.get('STAGE', '')
                status = equipment.get('STATUS', '')
                handler_config = equipment.get('HANDLER_CONFIG', '')
                
                all_equipment.append({
                    'HANDLER_ID': handler_id,
                    'EQP_CLASS': eqp_class,
                    'STAGE': stage_field,
                    'STATUS': status,
                    'HANDLER_CONFIG': handler_config,
                    'DEVICE': equipment.get('DEVICE', ''),
                })
                
                # 查找LSTR相关设备
                if ('LSTR' in stage_field.upper() or 
                    'LSTR' in eqp_class.upper() or 
                    'TAPE' in stage_field.upper() or 
                    'REEL' in stage_field.upper()):
                    lstr_equipment.append({
                        'HANDLER_ID': handler_id,
                        'EQP_CLASS': eqp_class,
                        'STAGE': stage_field,
                        'STATUS': status,
                        'HANDLER_CONFIG': handler_config,
                        'DEVICE': equipment.get('DEVICE', ''),
                    })
            
            logger.info(f"✅ 找到 {len(lstr_equipment)} 台LSTR相关设备:")
            for i, eqp in enumerate(lstr_equipment, 1):
                logger.info(f"   {i}. {eqp['HANDLER_ID']}")
                logger.info(f"      EQP_CLASS: {eqp['EQP_CLASS']}")
                logger.info(f"      STAGE: {eqp['STAGE']}")
                logger.info(f"      STATUS: {eqp['STATUS']}")
                logger.info(f"      HANDLER_CONFIG: {eqp['HANDLER_CONFIG']}")
                logger.info(f"      DEVICE: {eqp['DEVICE']}")
                logger.info("      ---")
            
            # 4. 分析匹配问题
            logger.info(f"\n📋 Step 4: 分析为什么没有匹配成功")
            
            if not matching_specs:
                logger.error("❌ 问题1: 没有找到匹配的测试规范")
                logger.info("   可能原因:")
                logger.info("   - ET_FT_TEST_SPEC表中没有JWQ85113CS-C293QFND-SJA1_TR1 + LSTR的记录")
                logger.info("   - APPROVAL_STATE不是'Released'")
                
                # 查找类似的规范
                similar_specs = []
                for spec in test_specs.get('data', []):
                    if spec.get('DEVICE') == device:
                        similar_specs.append(f"   - DEVICE={spec.get('DEVICE')}, STAGE={spec.get('STAGE')}, APPROVAL={spec.get('APPROVAL_STATE')}")
                
                if similar_specs:
                    logger.info("   找到该DEVICE的其他STAGE配置:")
                    for similar in similar_specs[:5]:  # 只显示前5个
                        logger.info(similar)
                else:
                    logger.info("   ❌ 该DEVICE在测试规范中完全没有配置")
                        
            if not lstr_equipment:
                logger.error("❌ 问题2: 没有找到LSTR相关设备")
                logger.info("   查看所有设备的EQP_CLASS和STAGE:")
                class_count = {}
                stage_count = {}
                for eqp in all_equipment:
                    eqp_class = eqp['EQP_CLASS']
                    stage_field = eqp['STAGE']
                    class_count[eqp_class] = class_count.get(eqp_class, 0) + 1
                    stage_count[stage_field] = stage_count.get(stage_field, 0) + 1
                
                logger.info("   EQP_CLASS统计:")
                for eqp_class, count in sorted(class_count.items()):
                    logger.info(f"     {eqp_class}: {count}台")
                
                logger.info("   STAGE统计:")
                for stage_field, count in sorted(stage_count.items()):
                    if stage_field:  # 只显示非空的
                        logger.info(f"     {stage_field}: {count}台")
            
            # 5. 检查HANDLER_CONFIG匹配
            if matching_specs and lstr_equipment:
                logger.info(f"\n📋 Step 5: 检查HANDLER_CONFIG匹配情况")
                
                for spec in matching_specs:
                    spec_handler_config = spec.get('HANDLER_CONFIG', '')
                    logger.info(f"测试规范要求的HANDLER_CONFIG: '{spec_handler_config}'")
                    
                    for eqp in lstr_equipment:
                        eqp_handler_config = eqp.get('HANDLER_CONFIG', '')
                        match = (spec_handler_config.strip().upper() == eqp_handler_config.strip().upper()) if spec_handler_config and eqp_handler_config else False
                        
                        logger.info(f"   设备{eqp['HANDLER_ID']}: '{eqp_handler_config}' - {'✅匹配' if match else '❌不匹配'}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 直接查询分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_database_directly()
    print("🎉 分析完成" if success else "❌ 分析失败")

if __name__ == "__main__":
    main()