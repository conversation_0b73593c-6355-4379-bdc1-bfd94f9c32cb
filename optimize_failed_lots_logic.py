#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化failed-lots页面显示逻辑：基于调度会话而不是时间范围
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('optimize_failed_lots_logic.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('OptimizeFailedLotsLogic')

def optimize_failed_lots_api():
    """优化失败批次API，支持基于最新调度会话的筛选"""
    try:
        api_file_path = "app/api_v2/production/done_lots_api.py"
        
        with open(api_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找get_failed_lots_from_logs函数
        if 'def get_failed_lots_from_logs():' not in content:
            logger.error("未找到get_failed_lots_from_logs函数")
            return False
        
        # 新的API实现代码
        new_api_implementation = '''
def get_failed_lots_from_logs():
    """从数据库中获取排产失败的批次信息（基于调度会话的优化版本）"""
    try:
        import os
        import re
        from datetime import datetime, timedelta
        from app.utils.db_connection_pool import get_db_connection_context
        
        # 🔥 获取查询参数
        current_only = request.args.get('current_only', 'false').lower() == 'true'
        session_id = request.args.get('session_id', '')  # 新增：特定会话ID
        
        logger.info(f"🔍 开始获取失败批次信息... (current_only={current_only}, session_id={session_id})")
        
        try:
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
            
                # 检查表是否存在
                check_table_sql = """
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
                """
                cursor.execute(check_table_sql)
                check_result = cursor.fetchone()
                
                if isinstance(check_result, dict):
                    table_exists = list(check_result.values())[0] > 0
                else:
                    table_exists = check_result[0] > 0
                
                if not table_exists:
                    return jsonify({
                        'success': True,
                        'data': {
                            'failed_lots': [],
                            'total_count': 0,
                            'summary': {
                                'total_failed': 0,
                                'config_missing': 0,
                                'equipment_incompatible': 0,
                                'other_reasons': 0
                            }
                        },
                        'debug_info': {
                            'data_source': 'database_empty',
                            'table_name': 'scheduling_failed_lots',
                            'total_records': 0
                        },
                        'message': 'scheduling_failed_lots表不存在'
                    })
                
                # 🚀 新逻辑：基于调度会话的查询
                failed_lots = []
                
                if current_only:
                    if session_id:
                        # 指定会话ID的失败批次
                        logger.info(f"查询指定会话 {session_id} 的失败批次...")
                        query_sql = """
                        SELECT
                            sfl.lot_id,
                            sfl.device,
                            sfl.stage,
                            sfl.good_qty,
                            sfl.failure_reason,
                            sfl.failure_details,
                            sfl.suggestion,
                            sfl.session_id,
                            sfl.timestamp,
                            ewl.LOT_TYPE,
                            ewl.PKG_PN
                        FROM scheduling_failed_lots sfl
                        LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                        WHERE sfl.session_id = %s
                        ORDER BY sfl.timestamp DESC
                        LIMIT 1000
                        """
                        cursor.execute(query_sql, (session_id,))
                    else:
                        # 🔥 核心优化：获取最新调度会话的失败批次
                        logger.info("查询最新调度会话的失败批次...")
                        
                        # 先获取最新的会话ID
                        latest_session_sql = """
                        SELECT session_id 
                        FROM scheduling_failed_lots 
                        WHERE session_id IS NOT NULL AND session_id != ''
                        ORDER BY timestamp DESC 
                        LIMIT 1
                        """
                        cursor.execute(latest_session_sql)
                        latest_session_result = cursor.fetchone()
                        
                        if latest_session_result:
                            if isinstance(latest_session_result, dict):
                                latest_session_id = list(latest_session_result.values())[0]
                            else:
                                latest_session_id = latest_session_result[0]
                            
                            logger.info(f"找到最新调度会话: {latest_session_id}")
                            
                            # 获取该会话的所有失败批次
                            query_sql = """
                            SELECT
                                sfl.lot_id,
                                sfl.device,
                                sfl.stage,
                                sfl.good_qty,
                                sfl.failure_reason,
                                sfl.failure_details,
                                sfl.suggestion,
                                sfl.session_id,
                                sfl.timestamp,
                                ewl.LOT_TYPE,
                                ewl.PKG_PN
                            FROM scheduling_failed_lots sfl
                            LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                            WHERE sfl.session_id = %s
                            ORDER BY sfl.timestamp DESC
                            LIMIT 1000
                            """
                            cursor.execute(query_sql, (latest_session_id,))
                        else:
                            logger.warning("未找到有效的调度会话")
                            cursor.execute("SELECT NULL WHERE 1=0")  # 返回空结果
                else:
                    # 获取所有历史失败记录
                    logger.info("查询所有历史失败批次...")
                    query_sql = """
                    SELECT
                        sfl.lot_id,
                        sfl.device,
                        sfl.stage,
                        sfl.good_qty,
                        sfl.failure_reason,
                        sfl.failure_details,
                        sfl.suggestion,
                        sfl.session_id,
                        sfl.timestamp,
                        ewl.LOT_TYPE,
                        ewl.PKG_PN
                    FROM scheduling_failed_lots sfl
                    LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                    ORDER BY sfl.timestamp DESC
                    LIMIT 1000
                    """
                    cursor.execute(query_sql)
                
                results = cursor.fetchall()
                logger.info(f"查询到 {len(results)} 条失败批次记录")
                
                if not results:
                    return jsonify({
                        'success': True,
                        'data': {
                            'failed_lots': [],
                            'total_count': 0,
                            'summary': {
                                'total_failed': 0,
                                'config_missing': 0,
                                'equipment_incompatible': 0,
                                'other_reasons': 0
                            }
                        },
                        'debug_info': {
                            'data_source': 'database_empty_current_mode' if current_only else 'database_empty',
                            'table_name': 'scheduling_failed_lots',
                            'total_records': 0
                        },
                        'message': '未找到失败批次数据'
                    })
                
                # 处理查询结果
                config_missing_count = 0
                equipment_incompatible_count = 0
                other_reasons_count = 0
                
                for row in results:
                    if isinstance(row, dict):
                        lot_id = row['lot_id'] or ''
                        device = row['device'] or ''
                        stage = row['stage'] or ''
                        good_qty = row['good_qty'] or 0
                        failure_reason = row['failure_reason'] or '未知原因'
                        failure_details = row['failure_details'] or ''
                        suggestion = row.get('suggestion', '') or generate_suggestion(failure_reason, failure_details, device, stage)
                        session_id = row.get('session_id', '')
                        timestamp = row['timestamp']
                        lot_type = row.get('LOT_TYPE', '') or ''
                        pkg_pn = row.get('PKG_PN', '') or ''
                    else:
                        # 处理元组格式
                        lot_id = row[0] or ''
                        device = row[1] or ''
                        stage = row[2] or ''
                        good_qty = row[3] or 0
                        failure_reason = row[4] or '未知原因'
                        failure_details = row[5] or ''
                        suggestion = row[6] or generate_suggestion(failure_reason, failure_details, device, stage)
                        session_id = row[7] or ''
                        timestamp = row[8]
                        lot_type = row[9] or ''
                        pkg_pn = row[10] or ''
                    
                    # 统计失败原因类型
                    if any(keyword in failure_reason for keyword in ['配置', '规范', 'config', 'spec']):
                        config_missing_count += 1
                    elif any(keyword in failure_reason for keyword in ['设备', '不兼容', 'equipment', 'incompatible', '无合适']):
                        equipment_incompatible_count += 1
                    else:
                        other_reasons_count += 1
                    
                    failure_info = {
                        'LOT_ID': lot_id,
                        'DEVICE': device,
                        'STAGE': stage,
                        'LOT_TYPE': lot_type,
                        'PKG_PN': pkg_pn,
                        'GOOD_QTY': good_qty,
                        'failure_reason': failure_reason,
                        'suggestion': suggestion,
                        'timestamp': timestamp.isoformat() if timestamp else datetime.now().isoformat(),
                        'session_id': session_id
                    }
                    failed_lots.append(failure_info)
                
                # 构建响应
                return jsonify({
                    'success': True,
                    'data': {
                        'failed_lots': failed_lots,
                        'total_count': len(failed_lots),
                        'summary': {
                            'total_failed': len(failed_lots),
                            'config_missing': config_missing_count,
                            'equipment_incompatible': equipment_incompatible_count,
                            'other_reasons': other_reasons_count
                        }
                    },
                    'debug_info': {
                        'data_source': 'database',
                        'table_name': 'scheduling_failed_lots',
                        'total_records': len(failed_lots),
                        'query_mode': 'latest_session' if current_only else 'all_history'
                    },
                    'message': f'成功获取失败批次数据（{"最新调度会话" if current_only else "所有历史"}）'
                })
                
        except Exception as db_error:
            logger.error(f"数据库查询失败: {db_error}")
            return jsonify({
                'success': False,
                'error': str(db_error),
                'message': '数据库查询失败'
            }), 500
            
    except Exception as e:
        logger.error(f"❌ 获取失败批次数据出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取失败批次数据失败'
        }), 500'''
        
        # 替换原函数
        start_marker = 'def get_failed_lots_from_logs():'
        end_marker = '\n@done_lots_bp.route'  # 下一个函数开始
        
        start_pos = content.find(start_marker)
        if start_pos == -1:
            logger.error("未找到函数开始位置")
            return False
            
        end_pos = content.find(end_marker, start_pos)
        if end_pos == -1:
            # 如果没有下一个函数，找到文件末尾
            end_pos = len(content)
        
        new_content = content[:start_pos] + new_api_implementation.strip() + '\n\n' + content[end_pos:]
        
        # 写回文件
        with open(api_file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info("✅ 成功优化失败批次API")
        return True
        
    except Exception as e:
        logger.error(f"❌ 优化API失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def optimize_failed_lots_frontend():
    """优化失败批次前端页面逻辑"""
    try:
        template_file_path = "app/templates/production/failed_lots.html"
        
        with open(template_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 简化全局变量
        old_globals = """let allFailedLots = [];
let filteredLots = [];
let currentPage = 1;
let pageSize = 50;
let isCurrentOnly = true; // 默认查看当前排产会话
let sortColumn = 'timestamp';
let sortDirection = 'desc';"""
        
        new_globals = """let allFailedLots = [];
let filteredLots = [];
let currentPage = 1;
let pageSize = 50;
let viewMode = 'current'; // 'current' = 最新调度会话, 'history' = 所有历史
let sortColumn = 'timestamp';
let sortDirection = 'desc';"""
        
        # 2. 简化loadFailedLots函数
        old_load_function = """// 🔥 获取查询参数
        current_only = request.args.get('current_only', 'false').lower() == 'true'
        hours_limit = int(request.args.get('hours', '24'))  # 默认24小时内的失败记录"""
        
        # 查找并替换loadFailedLots函数
        load_function_start = 'async function loadFailedLots(forceRefresh = false) {'
        load_function_end = 'function updateStats(summary) {'
        
        start_pos = content.find(load_function_start)
        end_pos = content.find(load_function_end)
        
        if start_pos != -1 and end_pos != -1:
            new_load_function = '''async function loadFailedLots(forceRefresh = false) {
    try {
        showLoading();
        
        // 🚀 基于视图模式构建URL
        let url = `/api/v2/production/get-failed-lots-from-logs`;
        
        if (viewMode === 'current') {
            url += `?current_only=true`;
        } else {
            url += `?current_only=false`;
        }
        
        // 🔥 强制刷新时添加缓存破坏参数
        if (forceRefresh) {
            url += `&_cache_bust=${Date.now()}&_force=true`;
        }
        
        console.log(`🔍 请求失败批次数据: ${url} (视图模式: ${viewMode})`);
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('📊 失败批次API响应:', result);
        
        if (result.success) {
            allFailedLots = result.data.failed_lots || [];
            
            // 更新统计信息
            if (result.data.summary) {
                updateStats(result.data.summary);
            }
            
            // 更新数据源信息
            if (result.debug_info) {
                updateDataSourceInfo(result.debug_info);
            }
            
            // 生成筛选选项并应用筛选
            updateFilterOptionsFromData(allFailedLots);
            filterLots();
            
            console.log(`✅ 成功加载 ${allFailedLots.length} 条失败批次记录 (${viewMode}模式)`);
        } else {
            throw new Error(result.message || '获取失败批次数据失败');
        }
    } catch (error) {
        console.error('❌ 加载失败批次数据出错:', error);
        showError('加载失败批次数据失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

'''
            
            # 替换函数
            content = content[:start_pos] + new_load_function + content[end_pos:]
        
        # 3. 简化按钮函数
        old_retry_function = """function retryScheduling() {
    // 🔥 "当前失败" - 查看最近24小时内的失败批次（清空后应为空）
    console.log('🎯 切换到当前失败模式（24小时内）');
    isCurrentOnly = true;
    
    // 更新按钮状态
    updateViewModeButtons();
    
    // 强制刷新确保获取最新数据
    loadFailedLots(true);
}"""
        
        new_retry_function = """function retryScheduling() {
    console.log('🎯 切换到当前失败模式（最新调度会话）');
    viewMode = 'current';
    updateViewModeButtons();
    loadFailedLots(true);
}"""
        
        old_history_function = """function viewHistory() {
    // 🔥 "历史记录" - 查看所有历史失败批次（清空后应为空，不会从日志恢复）
    console.log('📊 切换到历史记录模式（所有时间）');
    isCurrentOnly = false;
    
    // 更新按钮状态
    updateViewModeButtons();
    
    // 强制刷新确保获取最新数据
    loadFailedLots(true);
}"""
        
        new_history_function = """function viewHistory() {
    console.log('📊 切换到历史记录模式（所有调度会话）');
    viewMode = 'history';
    updateViewModeButtons();
    loadFailedLots(true);
}"""
        
        # 4. 简化按钮状态更新函数
        old_update_buttons = """function updateViewModeButtons() {
    const retryBtn = document.querySelector('button[onclick="retryScheduling()"]');
    const historyBtn = document.querySelector('button[onclick="viewHistory()"]');
    const indicator = document.getElementById('viewModeIndicator');
    
    if (retryBtn && historyBtn) {
        if (isCurrentOnly) {
            // 当前失败模式
            retryBtn.classList.add('btn-primary');
            retryBtn.classList.remove('btn-outline-primary');
            historyBtn.classList.remove('btn-primary');
            historyBtn.classList.add('btn-outline-primary');
            
            // 更新状态指示器
            if (indicator) {
                indicator.textContent = '当前失败';
                indicator.className = 'badge bg-primary ms-2';
            }
        } else {
            // 历史记录模式
            historyBtn.classList.add('btn-primary');  
            historyBtn.classList.remove('btn-outline-primary');
            retryBtn.classList.remove('btn-primary');
            retryBtn.classList.add('btn-outline-primary');
            
            // 更新状态指示器
            if (indicator) {
                indicator.textContent = '历史记录';
                indicator.className = 'badge bg-secondary ms-2';
            }
        }
    }
}"""
        
        new_update_buttons = """function updateViewModeButtons() {
    const retryBtn = document.querySelector('button[onclick="retryScheduling()"]');
    const historyBtn = document.querySelector('button[onclick="viewHistory()"]');
    const indicator = document.getElementById('viewModeIndicator');
    
    if (retryBtn && historyBtn) {
        // 重置所有按钮样式
        retryBtn.className = 'btn btn-console ' + (viewMode === 'current' ? 'btn-primary' : 'btn-outline-primary');
        historyBtn.className = 'btn btn-console ' + (viewMode === 'history' ? 'btn-primary' : 'btn-outline-primary');
        
        // 更新状态指示器
        if (indicator) {
            if (viewMode === 'current') {
                indicator.textContent = '最新调度';
                indicator.className = 'badge bg-primary ms-2';
            } else {
                indicator.textContent = '历史记录';
                indicator.className = 'badge bg-secondary ms-2';
            }
        }
    }
}"""
        
        # 应用所有替换
        content = content.replace(old_globals, new_globals)
        content = content.replace(old_retry_function, new_retry_function)
        content = content.replace(old_history_function, new_history_function)
        content = content.replace(old_update_buttons, new_update_buttons)
        
        # 5. 删除重复的监控代码
        monitor_code_start = '<!-- 失败批次自动刷新监控 -->'
        monitor_code_end = '</script>\n\n<script>'
        
        monitor_start = content.find(monitor_code_start)
        monitor_end = content.find(monitor_code_end, monitor_start)
        
        if monitor_start != -1 and monitor_end != -1:
            # 删除重复的监控代码
            content = content[:monitor_start] + content[monitor_end + len('</script>\n\n'):]
            logger.info("删除了重复的监控代码")
        
        # 写回文件
        with open(template_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ 成功优化失败批次前端页面")
        return True
        
    except Exception as e:
        logger.error(f"❌ 优化前端页面失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    print("🚀 开始优化failed-lots页面显示逻辑...")
    
    # 1. 优化API
    print("\n1. 优化API逻辑...")
    api_success = optimize_failed_lots_api()
    
    # 2. 优化前端
    print("\n2. 优化前端逻辑...")
    frontend_success = optimize_failed_lots_frontend()
    
    success = api_success and frontend_success
    print(f"\n🎉 优化: {'成功' if success else '失败'}")
    
    if success:
        print("\n✅ 优化完成！主要改进:")
        print("  1. 🎯 当前失败 = 基于最新调度会话ID，不再基于24小时时间")
        print("  2. 📊 历史记录 = 显示所有调度会话的失败批次")
        print("  3. 🗑️ 删除了重复和冗余的代码")
        print("  4. 💡 简化了前端状态管理逻辑")

if __name__ == "__main__":
    main()