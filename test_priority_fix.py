#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优先级分配修复效果
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)-8s | %(message)s')
logger = logging.getLogger('PriorityFixTest')

def test_priority_assignment_sorting():
    """测试优先级分配排序逻辑"""
    print("\n=== 测试排序逻辑修复效果 ===")
    
    # 模拟HCHC-C-012-6800机台的数据
    test_data = [
        {
            'LOT_ID': 'YX0125HB0400',
            'comprehensive_score': 141.70,  # 小写，最高分
            'HANDLER_ID': 'HCHC-C-012-6800'
        },
        {
            'LOT_ID': 'YX0125GB0367',
            'comprehensive_score': 74.20,   # 小写，次高分
            'HANDLER_ID': 'HCHC-C-012-6800'
        },
        {
            'LOT_ID': 'YX0125GB0206',
            'comprehensive_score': 73.50,   # 小写，第三高分
            'HANDLER_ID': 'HCHC-C-012-6800'
        },
        {
            'LOT_ID': 'YX2500001077',
            'comprehensive_score': 72.00,   # 小写，第四高分
            'HANDLER_ID': 'HCHC-C-012-6800'
        }
    ]
    
    print("原始数据（未排序）：")
    for i, item in enumerate(test_data, 1):
        print(f"  {i}. {item['LOT_ID']} - 评分: {item['comprehensive_score']}")
    
    # 应用修复后的排序逻辑
    test_data.sort(key=lambda x: (
        -x.get('comprehensive_score', 0),  # 修复后：小写comprehensive_score
        x.get('LOT_ID', ''),
        x.get('HANDLER_ID', ''),
    ))
    
    print(f"\n修复后排序结果（按comprehensive_score降序）：")
    for priority_order, item in enumerate(test_data, 1):
        print(f"  优先级{priority_order}: {item['LOT_ID']} - 评分: {item['comprehensive_score']}")
    
    # 验证排序是否正确
    expected_order = ['YX0125HB0400', 'YX0125GB0367', 'YX0125GB0206', 'YX2500001077']
    actual_order = [item['LOT_ID'] for item in test_data]
    
    print(f"\n🔍 验证结果：")
    print(f"  期望顺序: {expected_order}")
    print(f"  实际顺序: {actual_order}")
    
    if actual_order == expected_order:
        print(f"  ✅ 排序正确！高评分批次获得优先级1")
        return True
    else:
        print(f"  ❌ 排序仍有问题")
        return False

def simulate_old_bug():
    """模拟修复前的bug情况"""
    print(f"\n=== 模拟修复前的bug情况 ===")
    
    test_data = [
        {'LOT_ID': 'YX0125HB0400', 'comprehensive_score': 141.70},
        {'LOT_ID': 'YX0125GB0367', 'comprehensive_score': 74.20},
        {'LOT_ID': 'YX0125GB0206', 'comprehensive_score': 73.50},
        {'LOT_ID': 'YX2500001077', 'comprehensive_score': 72.00}
    ]
    
    # 修复前的错误排序逻辑（大写COMPREHENSIVE_SCORE）
    test_data.sort(key=lambda x: (
        -x.get('COMPREHENSIVE_SCORE', 0),  # Bug：查找大写字段，总是得到0
        x.get('LOT_ID', ''),
    ))
    
    print("Bug情况下的排序结果（实际按LOT_ID排序）：")
    for priority_order, item in enumerate(test_data, 1):
        print(f"  优先级{priority_order}: {item['LOT_ID']} - 评分: {item['comprehensive_score']}")
    
    print("❌ 这就解释了为什么YX0125GB0206（评分73.50）获得了优先级1")
    print("   而YX0125HB0400（评分141.70）却获得了优先级4")

def main():
    print("🔧 测试优先级分配修复效果")
    
    # 模拟修复前的bug
    simulate_old_bug()
    
    # 测试修复后的效果
    success = test_priority_assignment_sorting()
    
    print(f"\n📋 修复总结：")
    print(f"1. 发现问题：排序时使用大写'COMPREHENSIVE_SCORE'，但数据中是小写'comprehensive_score'")  
    print(f"2. 修复方案：将排序键从'COMPREHENSIVE_SCORE'改为'comprehensive_score'")
    print(f"3. 修复效果：{'✅ 成功' if success else '❌ 失败'}")
    print(f"4. 现在HCHC-C-012-6800机台的批次将按评分正确分配优先级1,2,3...")

if __name__ == "__main__":
    main()