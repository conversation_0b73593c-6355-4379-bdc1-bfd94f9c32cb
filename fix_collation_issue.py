#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复失败批次API的字符集冲突问题
"""

# 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
from datetime import datetime

# 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_collation.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('CollationFix')

def check_table_collations():
    """检查表的字符集设置"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 检查表的字符集
                query = """
                SELECT 
                    table_name, 
                    table_collation,
                    table_schema
                FROM information_schema.tables 
                WHERE table_schema = 'aps' 
                AND table_name IN ('scheduling_failed_lots', 'et_wait_lot')
                ORDER BY table_name
                """
                
                cursor.execute(query)
                results = cursor.fetchall()
                
                logger.info("📋 表字符集信息:")
                for row in results:
                    # 统一处理为tuple格式
                    if isinstance(row, dict):
                        table_name = row.get('table_name') or row.get('TABLE_NAME')
                        table_collation = row.get('table_collation') or row.get('TABLE_COLLATION')
                    else:
                        table_name = row[0] if len(row) > 0 else 'unknown'
                        table_collation = row[1] if len(row) > 1 else 'unknown'
                    logger.info(f"  {table_name}: {table_collation}")
                
                # 检查列的字符集
                column_query = """
                SELECT 
                    table_name,
                    column_name, 
                    collation_name
                FROM information_schema.columns 
                WHERE table_schema = 'aps' 
                AND table_name IN ('scheduling_failed_lots', 'et_wait_lot')
                AND column_name IN ('lot_id', 'LOT_ID')
                ORDER BY table_name, column_name
                """
                
                cursor.execute(column_query)
                column_results = cursor.fetchall()
                
                logger.info("📋 关键列字符集信息:")
                for row in column_results:
                    # 统一处理为tuple格式
                    if isinstance(row, dict):
                        table_name = row.get('table_name') or row.get('TABLE_NAME')
                        column_name = row.get('column_name') or row.get('COLUMN_NAME') 
                        collation_name = row.get('collation_name') or row.get('COLLATION_NAME')
                    else:
                        table_name = row[0] if len(row) > 0 else 'unknown'
                        column_name = row[1] if len(row) > 1 else 'unknown'
                        collation_name = row[2] if len(row) > 2 else 'unknown'
                    logger.info(f"  {table_name}.{column_name}: {collation_name}")
                
                return True
                
    except Exception as e:
        logger.error(f"❌ 检查表字符集失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def fix_collation_in_api():
    """修复API中的字符集冲突"""
    try:
        api_file_path = r"D:\cursor\AEC-FT-Intelligent-Commander-Platform-1.3.6.2\app\api_v2\production\done_lots_api.py"
        
        # 读取当前文件内容
        with open(api_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找需要修复的SQL查询
        old_join_pattern = "LEFT JOIN et_wait_lot ewl ON sfl.lot_id = ewl.LOT_ID"
        new_join_pattern = "LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)"
        
        if old_join_pattern in content:
            # 替换所有JOIN语句，强制字符类型转换
            content = content.replace(old_join_pattern, new_join_pattern)
            logger.info("✅ 已修复JOIN语句的字符集冲突")
            
            # 写回文件
            with open(api_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ API文件已更新")
            return True
        else:
            logger.info("ℹ️ 未发现需要修复的JOIN语句")
            return True
            
    except Exception as e:
        logger.error(f"❌ 修复API字符集冲突失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_fixed_api():
    """测试修复后的API"""
    try:
        import requests
        import json
        
        # 测试缓存版本API
        url = "http://localhost:5000/api/v2/production/get-failed-lots-cached?current_only=false"
        logger.info(f"🔍 测试修复后的API: {url}")
        
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                failed_lots = result.get('data', {}).get('failed_lots', [])
                logger.info(f"✅ API测试成功，获取到 {len(failed_lots)} 条失败批次记录")
                
                # 显示部分数据作为验证
                if failed_lots:
                    logger.info("📋 前3条记录:")
                    for i, lot in enumerate(failed_lots[:3], 1):
                        logger.info(f"  {i}. LOT_ID: {lot.get('lot_id')}, 设备: {lot.get('device')}, 原因: {lot.get('failure_reason')}")
                
                return True
            else:
                logger.error(f"❌ API返回失败: {result.get('error', 'Unknown error')}")
                return False
        else:
            logger.error(f"❌ API请求失败，状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试API失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("🔧 开始修复失败批次API的字符集冲突问题...")
    
    # 步骤1: 检查表字符集
    logger.info("📋 步骤1: 检查数据库表字符集设置")
    if not check_table_collations():
        logger.error("❌ 字符集检查失败")
        return False
    
    # 步骤2: 修复API中的字符集冲突
    logger.info("🔧 步骤2: 修复API中的字符集冲突")
    if not fix_collation_in_api():
        logger.error("❌ API修复失败")
        return False
    
    # 步骤3: 测试修复后的API
    logger.info("🧪 步骤3: 测试修复后的API")
    if not test_fixed_api():
        logger.error("❌ API测试失败")
        return False
    
    logger.info("🎉 字符集冲突修复完成！")
    return True

if __name__ == "__main__":
    success = main()
    print("🎉 修复: 成功" if success else "❌ 修复: 失败")