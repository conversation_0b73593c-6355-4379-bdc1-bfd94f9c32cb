#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复权重数据一致性问题
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
from datetime import datetime

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_weight_data_consistency.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('WeightConsistencyFixer')

def fix_weight_data_consistency():
    """修复权重数据一致性问题"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.models import SchedulingConfig
            from app import db
            
            logger.info("🔍 检查数据库中的配置重复问题...")
            
            # 按照 (user_id, strategy_name) 分组查找重复的活跃配置
            strategies = ['intelligent', 'deadline', 'product', 'value']
            
            for strategy in strategies:
                logger.info(f"\n--- 检查策略 '{strategy}' ---")
                
                # 检查默认配置 (user_id=None)
                default_configs = SchedulingConfig.query.filter_by(
                    user_id=None,
                    strategy_name=strategy,
                    is_active=True
                ).all()
                
                logger.info(f"默认配置数量: {len(default_configs)}")
                
                if len(default_configs) > 1:
                    logger.warning(f"⚠️ 发现多个默认配置，需要清理")
                    # 保留最新的，禁用其他的
                    latest_config = max(default_configs, key=lambda x: x.updated_at or x.created_at)
                    for config in default_configs:
                        if config.id != latest_config.id:
                            logger.info(f"禁用重复配置 ID={config.id}")
                            config.is_active = False
                
                # 检查用户特定配置
                user_configs = SchedulingConfig.query.filter(
                    SchedulingConfig.user_id.isnot(None),
                    SchedulingConfig.strategy_name == strategy,
                    SchedulingConfig.is_active == True
                ).all()
                
                user_config_map = {}
                for config in user_configs:
                    if config.user_id not in user_config_map:
                        user_config_map[config.user_id] = []
                    user_config_map[config.user_id].append(config)
                
                for user_id, configs in user_config_map.items():
                    if len(configs) > 1:
                        logger.warning(f"⚠️ 用户 {user_id} 有多个 {strategy} 策略配置")
                        # 保留最新的，禁用其他的
                        latest_config = max(configs, key=lambda x: x.updated_at or x.created_at)
                        for config in configs:
                            if config.id != latest_config.id:
                                logger.info(f"禁用用户 {user_id} 的重复配置 ID={config.id}")
                                config.is_active = False
            
            # 提交更改
            try:
                db.session.commit()
                logger.info("✅ 数据库清理完成")
            except Exception as e:
                db.session.rollback()
                logger.error(f"❌ 数据库清理失败: {e}")
                return False
            
            # 验证修复结果
            logger.info("\n🔍 验证修复结果...")
            for strategy in strategies:
                # 检查默认配置
                default_count = SchedulingConfig.query.filter_by(
                    user_id=None,
                    strategy_name=strategy,
                    is_active=True
                ).count()
                
                logger.info(f"策略 '{strategy}' 默认配置数量: {default_count}")
                
                if default_count != 1:
                    logger.error(f"❌ 策略 '{strategy}' 默认配置数量异常: {default_count}")
                else:
                    logger.info(f"✅ 策略 '{strategy}' 默认配置正常")
                
                # 检查用户配置
                users_with_config = db.session.execute(
                    db.text("""
                    SELECT user_id, COUNT(*) as config_count 
                    FROM scheduling_config 
                    WHERE strategy_name = :strategy AND is_active = 1 AND user_id IS NOT NULL
                    GROUP BY user_id
                    HAVING config_count > 1
                    """),
                    {'strategy': strategy}
                ).fetchall()
                
                if users_with_config:
                    logger.error(f"❌ 策略 '{strategy}' 仍有用户配置重复: {users_with_config}")
                else:
                    logger.info(f"✅ 策略 '{strategy}' 用户配置正常")
            
            # 测试配置获取逻辑
            logger.info("\n🔍 测试修复后的配置获取逻辑...")
            
            # 测试admin用户的intelligent策略配置获取
            admin_intelligent_config = SchedulingConfig.get_active_config(
                user_id='admin', 
                strategy_name='intelligent'
            )
            
            if admin_intelligent_config:
                logger.info(f"✅ admin用户intelligent策略配置: ID={admin_intelligent_config.id}")
                logger.info(f"   负载均衡权重: {admin_intelligent_config.load_balance_weight}%")
                logger.info(f"   截止时间权重: {admin_intelligent_config.deadline_weight}%")
            else:
                logger.warning("❌ 未找到admin用户的intelligent策略配置")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 修复执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = fix_weight_data_consistency()
    print("🎉 修复: 通过" if success else "❌ 修复: 失败")

if __name__ == "__main__":
    main()