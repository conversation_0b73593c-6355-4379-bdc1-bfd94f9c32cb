#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排产匹配逻辑深度分析 - 找出为什么有HANDLER_CONFIG的设备也不能匹配
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict
import json

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('matching_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('MatchingAnalyzer')

def analyze_matching_logic():
    """深度分析匹配逻辑"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            from app.services.real_scheduling_service import RealSchedulingService
            
            data_manager = DataSourceManager()
            scheduling_service = RealSchedulingService()
            
            print("="*80)
            print("🔍 排产匹配逻辑深度分析")
            print("="*80)
            
            # ==================== 数据准备 ====================
            # 获取所有设备数据
            equipment_result = data_manager.get_table_data('EQP_STATUS')
            all_equipment = equipment_result.get('data', [])
            
            # 获取待排产数据
            wait_lots_result = data_manager.get_table_data('et_wait_lot')
            wait_lots = wait_lots_result.get('data', [])
            
            # 获取测试规格数据
            test_spec_result = data_manager.get_table_data('et_ft_test_spec')
            test_specs = test_spec_result.get('data', [])
            
            # 获取已排产数据用于对比
            scheduled_result = data_manager.get_table_data('lotprioritydone')
            scheduled_lots = scheduled_result.get('data', [])
            used_equipment_ids = set(lot.get('HANDLER_ID', '').strip() for lot in scheduled_lots if lot.get('HANDLER_ID'))
            
            print(f"📊 数据概览:")
            print(f"   设备总数: {len(all_equipment)}")
            print(f"   待排产批次: {len(wait_lots)}")
            print(f"   测试规格: {len(test_specs)}")
            print(f"   已使用设备: {len(used_equipment_ids)}")
            print(f"   未使用设备: {len(all_equipment) - len(used_equipment_ids)}")
            
            # ==================== 分析HANDLER_CONFIG字段分布 ====================
            print(f"\n🔍 第一步：HANDLER_CONFIG字段数据分析")
            
            # 设备HANDLER_CONFIG分布
            eqp_handler_configs = defaultdict(int)
            eqp_no_config_count = 0
            
            for eqp in all_equipment:
                handler_config = eqp.get('HANDLER_CONFIG', '').strip()
                if handler_config:
                    eqp_handler_configs[handler_config] += 1
                else:
                    eqp_no_config_count += 1
            
            print(f"   设备HANDLER_CONFIG分布:")
            print(f"   - 有配置的设备: {len(all_equipment) - eqp_no_config_count} 台")
            print(f"   - 无配置的设备: {eqp_no_config_count} 台")
            print(f"   - 配置类型总数: {len(eqp_handler_configs)}")
            
            print(f"   Top 10设备配置类型:")
            for i, (config, count) in enumerate(sorted(eqp_handler_configs.items(), key=lambda x: x[1], reverse=True)[:10], 1):
                print(f"     {i:2d}. {config:<20} : {count} 台设备")
            
            # 测试规格HANDLER字段分布  
            spec_handlers = defaultdict(int)
            spec_no_handler_count = 0
            
            for spec in test_specs:
                handler = spec.get('HANDLER', '').strip()
                if handler:
                    spec_handlers[handler] += 1
                else:
                    spec_no_handler_count += 1
            
            print(f"\n   测试规格HANDLER字段分布:")
            print(f"   - 有HANDLER的规格: {len(test_specs) - spec_no_handler_count} 条")
            print(f"   - 无HANDLER的规格: {spec_no_handler_count} 条")
            print(f"   - HANDLER类型总数: {len(spec_handlers)}")
            
            print(f"   Top 10测试规格HANDLER类型:")
            for i, (handler, count) in enumerate(sorted(spec_handlers.items(), key=lambda x: x[1], reverse=True)[:10], 1):
                print(f"     {i:2d}. {handler:<20} : {count} 条规格")
            
            # ==================== 分析字段匹配情况 ====================
            print(f"\n🔍 第二步：字段匹配分析")
            
            # 检查设备配置与测试规格的匹配情况
            eqp_config_set = set(eqp_handler_configs.keys())
            spec_handler_set = set(spec_handlers.keys())
            
            matched_configs = eqp_config_set & spec_handler_set
            eqp_only_configs = eqp_config_set - spec_handler_set
            spec_only_handlers = spec_handler_set - eqp_config_set
            
            print(f"   字段匹配统计:")
            print(f"   - 能匹配的配置: {len(matched_configs)} 种")
            print(f"   - 设备独有配置: {len(eqp_only_configs)} 种")  
            print(f"   - 规格独有HANDLER: {len(spec_only_handlers)} 种")
            
            if matched_configs:
                print(f"   能匹配的配置类型 (前10个):")
                for i, config in enumerate(list(matched_configs)[:10], 1):
                    eqp_count = eqp_handler_configs[config]
                    spec_count = spec_handlers[config]
                    print(f"     {i:2d}. {config:<20} : {eqp_count}台设备, {spec_count}条规格")
            
            if eqp_only_configs:
                print(f"   设备独有配置 (前10个):")
                for i, config in enumerate(list(eqp_only_configs)[:10], 1):
                    count = eqp_handler_configs[config]
                    print(f"     {i:2d}. {config:<20} : {count}台设备 (无对应测试规格)")
            
            # ==================== 实际匹配测试 ====================
            print(f"\n🔍 第三步：实际匹配逻辑测试")
            
            # 选择一些有HANDLER_CONFIG但未被使用的设备进行测试
            test_equipment = []
            for eqp in all_equipment:
                handler_id = eqp.get('HANDLER_ID', '').strip()
                handler_config = eqp.get('HANDLER_CONFIG', '').strip()
                
                # 选择有HANDLER_CONFIG但未被使用的设备
                if handler_config and handler_id not in used_equipment_ids:
                    test_equipment.append(eqp)
                    if len(test_equipment) >= 5:  # 只测试前5台
                        break
            
            if not test_equipment:
                print("   ⚠️ 没有找到有HANDLER_CONFIG但未被使用的设备")
                return True
            
            print(f"   选择 {len(test_equipment)} 台有配置但未使用的设备进行测试:")
            
            # 预加载数据
            print("   获取预加载数据...")
            preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
            if not preloaded_data:
                print("   ❌ 无法获取预加载数据")
                return False
            
            for i, eqp in enumerate(test_equipment, 1):
                handler_id = eqp.get('HANDLER_ID', '').strip()
                handler_config = eqp.get('HANDLER_CONFIG', '').strip()
                device = eqp.get('DEVICE', '').strip()
                stage = eqp.get('STAGE', '').strip()
                status = eqp.get('STATUS', '').strip()
                
                print(f"\n   测试设备 {i}: {handler_id}")
                print(f"     配置: CONFIG={handler_config}, DEVICE={device or '无'}, STAGE={stage or '无'}, 状态={status}")
                
                # 测试与待排产批次的匹配
                matching_lots = []
                failed_reasons = defaultdict(int)
                
                for lot in wait_lots[:20]:  # 测试前20个批次
                    lot_id = lot.get('LOT_ID', '')
                    
                    # 获取批次配置需求
                    lot_requirements = scheduling_service.get_lot_configuration_requirements_optimized(lot, preloaded_data)
                    if not lot_requirements:
                        failed_reasons['无法获取配置需求'] += 1
                        continue
                    
                    # 测试设备匹配评分
                    try:
                        match_score, match_type, changeover_time = scheduling_service.calculate_equipment_match_score_optimized(
                            lot_requirements, eqp, preloaded_data)
                        
                        if match_score > 0:
                            matching_lots.append({
                                'lot_id': lot_id,
                                'device': lot.get('DEVICE', ''),
                                'stage': lot.get('STAGE', ''),
                                'score': match_score,
                                'type': match_type,
                                'time': changeover_time
                            })
                        else:
                            failed_reasons['匹配评分为0'] += 1
                            
                    except Exception as e:
                        failed_reasons[f'匹配异常: {str(e)[:50]}'] += 1
                
                print(f"     匹配结果:")
                print(f"       成功匹配: {len(matching_lots)} 个批次")
                if matching_lots:
                    print(f"       匹配示例 (前3个):")
                    for match in matching_lots[:3]:
                        print(f"         - {match['lot_id']}: {match['device']}-{match['stage']} ({match['type']}, {match['score']}分)")
                else:
                    print(f"       无匹配批次")
                
                if failed_reasons:
                    print(f"       失败原因统计:")
                    for reason, count in failed_reasons.items():
                        print(f"         - {reason}: {count} 次")
                
                # 如果设备有配置但没有匹配，深入分析原因
                if handler_config and not matching_lots:
                    print(f"       🚨 深度分析: 设备有HANDLER_CONFIG({handler_config})但无法匹配")
                    
                    # 检查是否有对应的测试规格
                    matching_specs = [spec for spec in test_specs if spec.get('HANDLER', '').strip().upper() == handler_config.upper()]
                    print(f"         对应测试规格数量: {len(matching_specs)}")
                    
                    if matching_specs:
                        print(f"         对应规格示例:")
                        for spec in matching_specs[:3]:
                            print(f"           - {spec.get('DEVICE', '')}-{spec.get('STAGE', '')}")
                        
                        # 检查是否有对应的待排产批次
                        matching_wait_lots = []
                        for spec in matching_specs:
                            spec_device = spec.get('DEVICE', '')
                            spec_stage = spec.get('STAGE', '')
                            for lot in wait_lots:
                                if lot.get('DEVICE', '') == spec_device and lot.get('STAGE', '') == spec_stage:
                                    matching_wait_lots.append(lot)
                        
                        print(f"         对应待排产批次: {len(matching_wait_lots)} 个")
                        if matching_wait_lots:
                            print(f"         ❌ 配置数据匹配但排产算法仍然无法匹配，存在其他筛选条件！")
                        else:
                            print(f"         ⚠️ 有测试规格但无对应的待排产批次")
                    else:
                        print(f"         ❌ HANDLER_CONFIG({handler_config})在测试规格中找不到对应项")
            
            # ==================== 总结分析 ====================
            print(f"\n📊 分析总结:")
            
            total_eqp_with_config = len(all_equipment) - eqp_no_config_count
            unused_eqp_with_config = sum(1 for eqp in all_equipment 
                                       if eqp.get('HANDLER_CONFIG', '').strip() 
                                       and eqp.get('HANDLER_ID', '').strip() not in used_equipment_ids)
            
            print(f"   1. 数据匹配度:")
            print(f"      - 有HANDLER_CONFIG的设备: {total_eqp_with_config} 台")
            print(f"      - 其中未被使用的: {unused_eqp_with_config} 台")
            print(f"      - 设备配置与测试规格匹配率: {len(matched_configs)/len(eqp_config_set)*100:.1f}%")
            
            print(f"   2. 潜在问题:")
            if len(eqp_only_configs) > len(matched_configs):
                print(f"      - 设备配置数据与测试规格不同步 ({len(eqp_only_configs)} > {len(matched_configs)})")
            if unused_eqp_with_config > 0:
                print(f"      - 有配置但未使用的设备较多 ({unused_eqp_with_config} 台)")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_matching_logic()
    if success:
        print("\n🎉 匹配逻辑深度分析完成！")
    else:
        print("\n❌ 匹配逻辑分析失败！")

if __name__ == "__main__":
    main()