#!/usr/bin/env python3
"""
排产系统配置文件
"""

class SchedulingConfig:
    """排产系统配置"""
    
    # 重复执行检测配置
    DUPLICATE_CHECK_CONFIG = {
        # 手动排产重复检测间隔（秒）
        'MANUAL_SCHEDULING_TOLERANCE': 30,  # 从60秒改为30秒
        
        # 定时任务排产重复检测间隔（秒）
        'SCHEDULED_TASK_TOLERANCE': 30,
        
        # 是否启用重复检测
        'ENABLE_DUPLICATE_CHECK': True,
        
        # 是否启用严格模式（严格模式下会检查数据库记录）
        'STRICT_MODE': True
    }
    
    # 排产锁配置
    LOCK_CONFIG = {
        # 锁超时时间（秒）
        'LOCK_TIMEOUT': 300,  # 5分钟
        
        # 是否优先使用内存锁（Windows系统）
        'USE_MEMORY_LOCK_ON_WINDOWS': True,
        
        # Redis锁配置
        'REDIS_LOCK_PREFIX': 'aps:scheduling:lock:',
        'REDIS_LOCK_EXPIRE': 300
    }
    
    # 排产算法配置
    ALGORITHM_CONFIG = {
        # 默认算法
        'DEFAULT_ALGORITHM': 'intelligent',
        
        # 默认优化目标
        'DEFAULT_OPTIMIZATION_TARGET': 'balanced',
        
        # 算法超时时间（秒）
        'ALGORITHM_TIMEOUT': 300,
        
        # 并行计算配置
        'PARALLEL_COMPUTING': {
            'ENABLE': True,
            'MAX_WORKERS': None,  # None表示自动检测CPU核心数
            'CHUNK_SIZE': 100
        }
    }
    
    # 历史记录配置
    HISTORY_CONFIG = {
        # 历史记录保留天数
        'RETENTION_DAYS': 90,
        
        # 是否自动清理过期记录
        'AUTO_CLEANUP': True,
        
        # 清理任务执行时间（小时）
        'CLEANUP_HOUR': 2
    }
    
    @classmethod
    def get_manual_scheduling_tolerance(cls) -> int:
        """获取手动排产重复检测间隔"""
        return cls.DUPLICATE_CHECK_CONFIG['MANUAL_SCHEDULING_TOLERANCE']
    
    @classmethod
    def get_scheduled_task_tolerance(cls) -> int:
        """获取定时任务排产重复检测间隔"""
        return cls.DUPLICATE_CHECK_CONFIG['SCHEDULED_TASK_TOLERANCE']
    
    @classmethod
    def is_duplicate_check_enabled(cls) -> bool:
        """是否启用重复检测"""
        return cls.DUPLICATE_CHECK_CONFIG['ENABLE_DUPLICATE_CHECK']
    
    @classmethod
    def is_strict_mode_enabled(cls) -> bool:
        """是否启用严格模式"""
        return cls.DUPLICATE_CHECK_CONFIG['STRICT_MODE']
    
    @classmethod
    def get_lock_timeout(cls) -> int:
        """获取锁超时时间"""
        return cls.LOCK_CONFIG['LOCK_TIMEOUT']

# 为了向后兼容，提供一些常量
MANUAL_SCHEDULING_TOLERANCE_SECONDS = SchedulingConfig.get_manual_scheduling_tolerance()
SCHEDULED_TASK_TOLERANCE_SECONDS = SchedulingConfig.get_scheduled_task_tolerance()
LOCK_TIMEOUT_SECONDS = SchedulingConfig.get_lock_timeout()
