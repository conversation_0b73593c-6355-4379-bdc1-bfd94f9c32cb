/**
 * 失败批次页面自动刷新监控器
 * 用于在执行调度后自动刷新失败批次数据
 */

// 全局变量
let lastSchedulingTime = null;
let refreshMonitorInterval = null;
let failedLotsRefreshCallback = null;

// 启动监控器
function startFailedLotsRefreshMonitor() {
    console.log('🔍 启动失败批次刷新监控器...');
    
    // 每5秒检查一次是否有新的调度执行
    if (refreshMonitorInterval) {
        clearInterval(refreshMonitorInterval);
    }
    
    refreshMonitorInterval = setInterval(checkForNewScheduling, 5000);
    
    // 初始化最后调度时间
    updateLastSchedulingTime();
}

// 停止监控器
function stopFailedLotsRefreshMonitor() {
    if (refreshMonitorInterval) {
        clearInterval(refreshMonitorInterval);
        refreshMonitorInterval = null;
        console.log('⏹️ 已停止失败批次刷新监控器');
    }
}

// 设置刷新回调函数
function setFailedLotsRefreshCallback(callback) {
    failedLotsRefreshCallback = callback;
}

// 检查是否有新的调度执行
async function checkForNewScheduling() {
    try {
        // 检查最新的调度会话
        const response = await fetch('/api/v2/production/get-latest-scheduling-session');
        if (!response.ok) {
            return;
        }
        
        const result = await response.json();
        if (!result.success || !result.data) {
            return;
        }
        
        const latestTime = result.data.latest_session_time;
        
        // 如果有新的调度执行
        if (latestTime && latestTime !== lastSchedulingTime) {
            console.log('🔄 检测到新的调度执行，准备刷新失败批次数据...');
            lastSchedulingTime = latestTime;
            
            // 延迟1秒后刷新，确保数据已写入数据库
            setTimeout(() => {
                if (failedLotsRefreshCallback && typeof failedLotsRefreshCallback === 'function') {
                    failedLotsRefreshCallback();
                } else if (typeof refreshData === 'function') {
                    // 如果在failed-lots页面，直接调用刷新函数
                    refreshData();
                } else {
                    console.log('⚠️ 无法找到刷新函数');
                }
            }, 1000);
        }
    } catch (error) {
        console.warn('⚠️ 监控调度执行时出错:', error.message);
    }
}

// 更新最后调度时间
async function updateLastSchedulingTime() {
    try {
        const response = await fetch('/api/v2/production/get-latest-scheduling-session');
        if (response.ok) {
            const result = await response.json();
            if (result.success && result.data) {
                lastSchedulingTime = result.data.latest_session_time;
                console.log('📊 已更新最后调度时间:', lastSchedulingTime);
            }
        }
    } catch (error) {
        console.warn('⚠️ 获取最后调度时间失败:', error.message);
    }
}

// 手动触发刷新（用于"执行调度"按钮）
function triggerFailedLotsRefresh() {
    console.log('🔄 手动触发失败批次数据刷新...');
    
    // 立即更新调度时间
    updateLastSchedulingTime();
    
    // 延迟2秒后刷新失败批次数据
    setTimeout(() => {
        if (failedLotsRefreshCallback && typeof failedLotsRefreshCallback === 'function') {
            failedLotsRefreshCallback();
        } else if (typeof refreshData === 'function') {
            refreshData();
        }
    }, 2000);
}

// 在failed-lots页面加载时自动启动监控
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在failed-lots页面
    if (window.location.pathname.includes('/production/failed-lots') || 
        document.getElementById('failedLotsTable')) {
        
        console.log('🔍 检测到失败批次页面，启动自动刷新监控...');
        
        // 设置刷新回调
        if (typeof refreshData === 'function') {
            setFailedLotsRefreshCallback(refreshData);
        }
        
        // 启动监控
        startFailedLotsRefreshMonitor();
        
        // 页面卸载时停止监控
        window.addEventListener('beforeunload', stopFailedLotsRefreshMonitor);
    }
});

// 全局导出（方便其他页面使用）
window.failedLotsRefreshMonitor = {
    start: startFailedLotsRefreshMonitor,
    stop: stopFailedLotsRefreshMonitor,
    setCallback: setFailedLotsRefreshCallback,
    trigger: triggerFailedLotsRefresh
};