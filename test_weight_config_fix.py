#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试权重配置修复效果
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
import requests
import time
import json
from datetime import datetime

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_weight_config_fix.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('WeightConfigTester')

def test_weight_config_functionality():
    """测试权重配置功能"""
    try:
        base_url = "http://localhost:5000"
        
        # 创建会话用于保持登录状态
        session = requests.Session()
        
        logger.info("🔐 开始登录测试...")
        
        # 模拟登录
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        
        login_response = session.post(f"{base_url}/auth/login", data=login_data)
        if login_response.status_code != 200:
            logger.error(f"❌ 登录失败: {login_response.status_code}")
            return False
            
        logger.info("✅ 登录成功")
        
        # 测试1: 读取当前intelligent策略的权重配置
        logger.info("\n🔍 测试1: 读取intelligent策略权重配置...")
        
        get_response = session.get(f"{base_url}/api/production/algorithm-weights?strategy=intelligent")
        if get_response.status_code == 200:
            data = get_response.json()
            if data.get('success'):
                weights = data.get('weights', {})
                logger.info(f"✅ 读取成功: {json.dumps(weights, indent=2, ensure_ascii=False)}")
                
                # 记录当前配置用于后续比较
                original_tech_match = weights.get('tech_match_weight', 0)
                original_load_balance = weights.get('load_balance_weight', 0)
                original_deadline = weights.get('deadline_weight', 0)
                
                logger.info(f"原始配置 - 技术匹配: {original_tech_match}%, 负载均衡: {original_load_balance}%, 截止时间: {original_deadline}%")
            else:
                logger.error(f"❌ 读取失败: {data.get('message')}")
                return False
        else:
            logger.error(f"❌ 读取请求失败: {get_response.status_code}")
            return False
        
        # 测试2: 修改并保存权重配置
        logger.info("\n💾 测试2: 修改并保存权重配置...")
        
        # 创建新的权重配置（确保总和为100%）
        new_weights = {
            'strategy': 'intelligent',
            'tech_match_weight': 30.0,  # 修改为30%
            'load_balance_weight': 35.0,  # 修改为35%
            'deadline_weight': 15.0,   # 修改为15%
            'value_efficiency_weight': 15.0,
            'business_priority_weight': 5.0
        }
        
        logger.info(f"新权重配置: {json.dumps(new_weights, indent=2, ensure_ascii=False)}")
        
        save_response = session.post(
            f"{base_url}/api/production/algorithm-weights",
            json=new_weights,
            headers={'Content-Type': 'application/json'}
        )
        
        if save_response.status_code == 200:
            save_data = save_response.json()
            if save_data.get('success'):
                logger.info("✅ 保存成功")
            else:
                logger.error(f"❌ 保存失败: {save_data.get('message')}")
                return False
        else:
            logger.error(f"❌ 保存请求失败: {save_response.status_code} - {save_response.text}")
            return False
        
        # 测试3: 等待一段时间后重新读取，验证数据持久性
        logger.info("\n⏰ 测试3: 等待1秒后重新读取验证数据持久性...")
        time.sleep(1)
        
        verify_response = session.get(f"{base_url}/api/production/algorithm-weights?strategy=intelligent")
        if verify_response.status_code == 200:
            verify_data = verify_response.json()
            if verify_data.get('success'):
                verify_weights = verify_data.get('weights', {})
                
                logger.info(f"验证读取结果: {json.dumps(verify_weights, indent=2, ensure_ascii=False)}")
                
                # 检查关键字段是否与保存的值一致
                verify_tech_match = verify_weights.get('tech_match_weight', 0)
                verify_load_balance = verify_weights.get('load_balance_weight', 0)
                verify_deadline = verify_weights.get('deadline_weight', 0)
                
                if (abs(verify_tech_match - new_weights['tech_match_weight']) < 0.01 and
                    abs(verify_load_balance - new_weights['load_balance_weight']) < 0.01 and
                    abs(verify_deadline - new_weights['deadline_weight']) < 0.01):
                    logger.info("✅ 数据一致性验证通过 - 保存的数据正确读取")
                else:
                    logger.error(f"❌ 数据不一致!")
                    logger.error(f"   预期: 技术={new_weights['tech_match_weight']}%, 负载={new_weights['load_balance_weight']}%, 截止={new_weights['deadline_weight']}%")
                    logger.error(f"   实际: 技术={verify_tech_match}%, 负载={verify_load_balance}%, 截止={verify_deadline}%")
                    return False
                
            else:
                logger.error(f"❌ 验证读取失败: {verify_data.get('message')}")
                return False
        else:
            logger.error(f"❌ 验证读取请求失败: {verify_response.status_code}")
            return False
        
        # 测试4: 测试不同策略的配置独立性
        logger.info("\n🎯 测试4: 测试不同策略配置的独立性...")
        
        # 读取deadline策略配置
        deadline_response = session.get(f"{base_url}/api/production/algorithm-weights?strategy=deadline")
        if deadline_response.status_code == 200:
            deadline_data = deadline_response.json()
            if deadline_data.get('success'):
                deadline_weights = deadline_data.get('weights', {})
                logger.info(f"deadline策略配置: 截止时间权重={deadline_weights.get('deadline_weight', 0)}%")
                
                # deadline策略应该有不同的权重配置
                if deadline_weights.get('deadline_weight', 0) != verify_deadline:
                    logger.info("✅ 不同策略配置独立性验证通过")
                else:
                    logger.warning("⚠️ 不同策略配置可能存在混淆")
            else:
                logger.error(f"❌ deadline策略读取失败: {deadline_data.get('message')}")
        
        # 测试5: 恢复原始配置
        logger.info("\n🔄 测试5: 恢复原始配置...")
        
        restore_weights = {
            'strategy': 'intelligent',
            'tech_match_weight': original_tech_match,
            'load_balance_weight': original_load_balance,
            'deadline_weight': original_deadline,
            'value_efficiency_weight': weights.get('value_efficiency_weight', 20.0),
            'business_priority_weight': weights.get('business_priority_weight', 10.0)
        }
        
        restore_response = session.post(
            f"{base_url}/api/production/algorithm-weights",
            json=restore_weights,
            headers={'Content-Type': 'application/json'}
        )
        
        if restore_response.status_code == 200:
            restore_data = restore_response.json()
            if restore_data.get('success'):
                logger.info("✅ 原始配置恢复成功")
            else:
                logger.error(f"❌ 原始配置恢复失败: {restore_data.get('message')}")
        
        logger.info("\n🎉 所有测试完成，权重配置功能正常")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = test_weight_config_functionality()
    print("🎉 测试: 通过" if success else "❌ 测试: 失败")

if __name__ == "__main__":
    main()