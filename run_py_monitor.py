#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
run.py进程监控器 - 每10分钟汇报状态
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
import time
import json
import psutil
import pymysql
from datetime import datetime
import threading

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('run_py_monitor.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('RunPyMonitor')

class RunPyMonitor:
    def __init__(self):
        self.monitoring = True
        self.report_interval = 600  # 10分钟
        
    def get_mysql_connections(self):
        """获取MySQL连接数"""
        try:
            connection = pymysql.connect(
                host='localhost',
                port=3306,
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                result = cursor.fetchone()
                connected = int(result[1]) if result else 0
                
                cursor.execute("SHOW VARIABLES LIKE 'max_connections'")
                result = cursor.fetchone()
                max_conn = int(result[1]) if result else 0
                
            connection.close()
            return connected, max_conn
            
        except Exception as e:
            logger.error(f"获取MySQL连接信息失败: {e}")
            return 0, 0
    
    def find_run_py_processes(self):
        """查找run.py相关进程"""
        run_py_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'memory_info', 'cpu_percent']):
                try:
                    cmdline = proc.info['cmdline']
                    if cmdline and any('run.py' in str(cmd) for cmd in cmdline):
                        # 获取详细信息
                        proc_info = {
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': ' '.join(cmdline),
                            'create_time': datetime.fromtimestamp(proc.info['create_time']).strftime('%H:%M:%S'),
                            'memory_mb': proc.info['memory_info'].rss / 1024 / 1024,
                            'cpu_percent': proc.cpu_percent()
                        }
                        run_py_processes.append(proc_info)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            logger.error(f"查找run.py进程失败: {e}")
            
        return run_py_processes
    
    def check_flask_port(self):
        """检查Flask端口状态"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', 5000))
            sock.close()
            return result == 0
        except:
            return False
    
    def generate_status_report(self):
        """生成状态报告"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 获取系统信息
        mysql_connected, mysql_max = self.get_mysql_connections()
        run_py_processes = self.find_run_py_processes()
        flask_running = self.check_flask_port()
        
        # 系统资源
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        report = {
            'timestamp': timestamp,
            'flask_service': {
                'port_5000_active': flask_running,
                'process_count': len(run_py_processes),
                'processes': run_py_processes
            },
            'mysql_connections': {
                'current': mysql_connected,
                'max': mysql_max,
                'usage_percent': (mysql_connected / mysql_max * 100) if mysql_max > 0 else 0,
                'safety_margin': mysql_max - mysql_connected
            },
            'system_resources': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': memory.used / 1024 / 1024 / 1024,
                'memory_available_gb': memory.available / 1024 / 1024 / 1024
            }
        }
        
        return report
    
    def print_status_report(self, report):
        """打印状态报告"""
        print(f"\n{'='*60}")
        print(f"🕐 run.py监控报告 - {report['timestamp']}")
        print(f"{'='*60}")
        
        # Flask服务状态
        flask = report['flask_service']
        status_icon = "🟢" if flask['port_5000_active'] else "🔴"
        print(f"🌐 Flask服务: {status_icon} {'运行中' if flask['port_5000_active'] else '未运行'}")
        print(f"📊 run.py进程数: {flask['process_count']}")
        
        if flask['processes']:
            print("   进程详情:")
            for proc in flask['processes']:
                print(f"     PID {proc['pid']}: {proc['memory_mb']:.1f}MB, CPU {proc['cpu_percent']:.1f}%, 启动于 {proc['create_time']}")
        
        # MySQL连接池状态
        mysql = report['mysql_connections']
        mysql_icon = "🟢" if mysql['usage_percent'] < 50 else "🟡" if mysql['usage_percent'] < 80 else "🔴"
        print(f"🗄️  MySQL连接: {mysql_icon} {mysql['current']}/{mysql['max']} ({mysql['usage_percent']:.1f}%)")
        print(f"   安全边际: {mysql['safety_margin']} 连接")
        
        # 系统资源
        sys_res = report['system_resources']
        cpu_icon = "🟢" if sys_res['cpu_percent'] < 50 else "🟡" if sys_res['cpu_percent'] < 80 else "🔴"
        mem_icon = "🟢" if sys_res['memory_percent'] < 50 else "🟡" if sys_res['memory_percent'] < 80 else "🔴"
        
        print(f"🖥️  系统CPU: {cpu_icon} {sys_res['cpu_percent']:.1f}%")
        print(f"💾 系统内存: {mem_icon} {sys_res['memory_percent']:.1f}% ({sys_res['memory_used_gb']:.1f}GB 已用)")
        
        print(f"{'='*60}")
    
    def save_report(self, report):
        """保存报告到文件"""
        filename = f"run_py_monitor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
    
    def start_monitoring(self):
        """开始监控循环"""
        logger.info("🚀 开始run.py进程监控，每10分钟汇报一次")
        
        report_count = 0
        while self.monitoring:
            try:
                report = self.generate_status_report()
                self.print_status_report(report)
                self.save_report(report)
                
                report_count += 1
                logger.info(f"📋 已生成第{report_count}份监控报告")
                
                # 等待10分钟
                time.sleep(self.report_interval)
                
            except KeyboardInterrupt:
                logger.info("⏹️  收到中断信号，停止监控")
                break
            except Exception as e:
                logger.error(f"❌ 监控过程出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

def main():
    """监控入口"""
    monitor = RunPyMonitor()
    
    try:
        monitor.start_monitoring()
    except KeyboardInterrupt:
        logger.info("🛑 监控被用户中断")
    finally:
        monitor.stop_monitoring()
        print("✅ run.py监控已停止")

if __name__ == "__main__":
    main()