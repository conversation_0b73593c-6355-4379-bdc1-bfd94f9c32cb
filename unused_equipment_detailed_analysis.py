#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
未使用设备详细分析 - 分析45台未使用设备的具体原因
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict, Counter
import pandas as pd
import json

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('unused_equipment_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('UnusedEquipmentAnalyzer')

def analyze_unused_equipment():
    """详细分析未使用设备的原因"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入数据管理器和排产服务
            from app.services.data_source_manager import DataSourceManager
            from app.services.real_scheduling_service import RealSchedulingService
            
            data_manager = DataSourceManager()
            scheduling_service = RealSchedulingService()
            
            print("="*80)
            print("🔍 未使用设备详细分析报告")
            print("="*80)
            
            # 获取所有设备数据
            equipment_result = data_manager.get_table_data('EQP_STATUS')
            all_equipment = equipment_result.get('data', [])
            
            # 获取已排产数据
            scheduled_result = data_manager.get_table_data('lotprioritydone')
            scheduled_lots = scheduled_result.get('data', [])
            
            # 获取待排产数据
            wait_lots_result = data_manager.get_table_data('et_wait_lot')
            wait_lots = wait_lots_result.get('data', [])
            
            # 统计已使用设备
            used_equipment_ids = set()
            for lot in scheduled_lots:
                handler_id = lot.get('HANDLER_ID', '').strip()
                if handler_id:
                    used_equipment_ids.add(handler_id)
            
            print(f"📊 基本统计:")
            print(f"   总设备数量: {len(all_equipment)} 台")
            print(f"   已使用设备: {len(used_equipment_ids)} 台")
            print(f"   未使用设备: {len(all_equipment) - len(used_equipment_ids)} 台")
            print(f"   设备利用率: {len(used_equipment_ids)/len(all_equipment)*100:.1f}%")
            
            # 分析未使用设备
            unused_equipment = []
            for eqp in all_equipment:
                handler_id = eqp.get('HANDLER_ID', '').strip()
                if handler_id not in used_equipment_ids:
                    unused_equipment.append(eqp)
            
            print(f"\n🔍 未使用设备详细分析 ({len(unused_equipment)} 台):")
            
            # 按问题类型分类未使用设备
            issues_analysis = {
                'missing_device_stage': [],      # 缺少DEVICE/STAGE信息
                'missing_kit_info': [],          # 缺少KIT信息  
                'missing_handler_config': [],    # 缺少HANDLER_CONFIG
                'config_complete_but_no_match': [], # 配置完整但无匹配批次
                'status_issues': [],             # 设备状态问题
                'other_issues': []               # 其他问题
            }
            
            # 逐台分析未使用设备
            for i, eqp in enumerate(unused_equipment, 1):
                handler_id = eqp.get('HANDLER_ID', '').strip()
                device = eqp.get('DEVICE', '').strip()
                stage = eqp.get('STAGE', '').strip()
                kit_pn = eqp.get('KIT_PN', '').strip()
                handler_config = eqp.get('HANDLER_CONFIG', '').strip()
                status = eqp.get('STATUS', '').strip()
                eqp_class = eqp.get('EQP_CLASS', '').strip()
                
                # 分析具体问题
                issues = []
                
                # 检查配置完整性
                if not device or not stage:
                    issues.append("缺少DEVICE/STAGE配置")
                    issues_analysis['missing_device_stage'].append(eqp)
                
                if not kit_pn:
                    issues.append("缺少KIT_PN配置")
                    issues_analysis['missing_kit_info'].append(eqp)
                
                if not handler_config:
                    issues.append("缺少HANDLER_CONFIG配置")  
                    issues_analysis['missing_handler_config'].append(eqp)
                
                # 检查设备状态
                if status in ['DOWN']:
                    issues.append(f"设备状态异常({status})")
                    issues_analysis['status_issues'].append(eqp)
                
                # 如果配置完整但仍未使用，需要进一步分析
                if device and stage and kit_pn and handler_config and status not in ['DOWN']:
                    issues.append("配置完整但无匹配批次")
                    issues_analysis['config_complete_but_no_match'].append(eqp)
                
                if not issues:
                    issues.append("原因待查")
                    issues_analysis['other_issues'].append(eqp)
                
                print(f"   {i:2d}. {handler_id:<20} | 状态:{status:<8} | 问题: {', '.join(issues)}")
                if device and stage:
                    print(f"       配置: {device}-{stage} | KIT: {kit_pn or '无'} | CONFIG: {handler_config or '无'}")
                else:
                    print(f"       配置: 不完整 | KIT: {kit_pn or '无'} | CONFIG: {handler_config or '无'}")
            
            # 问题分类统计
            print(f"\n📋 问题分类统计:")
            print(f"   1. 缺少DEVICE/STAGE配置: {len(issues_analysis['missing_device_stage'])} 台")
            print(f"   2. 缺少KIT_PN配置: {len(issues_analysis['missing_kit_info'])} 台")  
            print(f"   3. 缺少HANDLER_CONFIG配置: {len(issues_analysis['missing_handler_config'])} 台")
            print(f"   4. 设备状态异常: {len(issues_analysis['status_issues'])} 台")
            print(f"   5. 配置完整但无匹配批次: {len(issues_analysis['config_complete_but_no_match'])} 台")
            print(f"   6. 其他原因: {len(issues_analysis['other_issues'])} 台")
            
            # 详细分析配置完整但无匹配的设备
            if issues_analysis['config_complete_but_no_match']:
                print(f"\n🔍 配置完整但无匹配批次的设备详细分析:")
                
                # 获取测试规格数据
                test_spec_result = data_manager.get_table_data('et_ft_test_spec')
                test_specs = test_spec_result.get('data', [])
                
                for eqp in issues_analysis['config_complete_but_no_match']:
                    handler_id = eqp.get('HANDLER_ID', '').strip()
                    eqp_device = eqp.get('DEVICE', '').strip()
                    eqp_stage = eqp.get('STAGE', '').strip()
                    eqp_kit = eqp.get('KIT_PN', '').strip()
                    eqp_handler_config = eqp.get('HANDLER_CONFIG', '').strip()
                    
                    print(f"\n   📱 设备 {handler_id}:")
                    print(f"       当前配置: {eqp_device}-{eqp_stage}, KIT: {eqp_kit}, CONFIG: {eqp_handler_config}")
                    
                    # 检查是否有批次需要这种配置
                    matching_lots = []
                    for lot in wait_lots:
                        lot_device = lot.get('DEVICE', '').strip()
                        lot_stage = lot.get('STAGE', '').strip()
                        
                        # 同设置匹配检查
                        if lot_device == eqp_device and lot_stage == eqp_stage:
                            matching_lots.append(f"{lot.get('LOT_ID', '')} (同设置匹配)")
                    
                    if matching_lots:
                        print(f"       🎯 有 {len(matching_lots)} 个批次可能匹配:")
                        for match in matching_lots[:3]:  # 只显示前3个
                            print(f"         - {match}")
                        if len(matching_lots) > 3:
                            print(f"         - ... 还有 {len(matching_lots)-3} 个批次")
                        print(f"       ⚠️ 该设备配置正确但仍未被排产，可能是排产算法的问题！")
                    else:
                        print(f"       ❌ 没有找到匹配的待排产批次")
                        print(f"       💡 建议：检查该设备配置是否需要更新或者是否为备用设备")
            
            # 分析待排产批次的需求分布
            print(f"\n📊 待排产批次需求分布分析:")
            
            device_stage_needs = defaultdict(int)
            for lot in wait_lots:
                device = lot.get('DEVICE', '').strip()
                stage = lot.get('STAGE', '').strip()
                if device and stage:
                    device_stage_needs[f"{device}-{stage}"] += 1
            
            print(f"   待排产批次总数: {len(wait_lots)}")
            print(f"   涉及的产品-工序组合: {len(device_stage_needs)} 种")
            print(f"   Top 10 需求最多的组合:")
            
            sorted_needs = sorted(device_stage_needs.items(), key=lambda x: x[1], reverse=True)
            for i, (combo, count) in enumerate(sorted_needs[:10], 1):
                print(f"     {i:2d}. {combo:<30} : {count} 个批次")
                
                # 检查有多少设备能处理这种组合
                capable_count = 0
                for eqp in all_equipment:
                    eqp_device = eqp.get('DEVICE', '').strip()
                    eqp_stage = eqp.get('STAGE', '').strip()
                    if eqp_device == combo.split('-')[0] and eqp_stage == combo.split('-')[1]:
                        capable_count += 1
                
                if capable_count == 0:
                    print(f"         ⚠️ 没有设备能直接处理这种组合！")
                else:
                    print(f"         ✅ 有 {capable_count} 台设备能处理")
            
            # 生成优化建议
            print(f"\n💡 优化建议:")
            
            if issues_analysis['missing_device_stage']:
                print(f"   🔥 紧急建议1：补全 {len(issues_analysis['missing_device_stage'])} 台设备的DEVICE/STAGE配置")
                print("      这些设备因为缺少基本配置信息无法参与排产")
            
            if issues_analysis['missing_kit_info']:
                print(f"   🔥 紧急建议2：补全 {len(issues_analysis['missing_kit_info'])} 台设备的KIT_PN配置")
                print("      这些设备无法进行同设置和小改机匹配")
            
            if issues_analysis['config_complete_but_no_match']:
                print(f"   ⚖️ 中期建议：深入分析 {len(issues_analysis['config_complete_but_no_match'])} 台配置完整但未使用的设备")
                print("      这些设备可能需要调整配置或优化排产算法")
            
            # 计算理论改进空间
            potential_recoverable = len(issues_analysis['missing_device_stage']) + \
                                   len(issues_analysis['missing_kit_info']) + \
                                   len(issues_analysis['config_complete_but_no_match'])
            
            current_utilization = len(used_equipment_ids) / len(all_equipment) * 100
            potential_utilization = (len(used_equipment_ids) + potential_recoverable) / len(all_equipment) * 100
            
            print(f"\n📈 改进空间预估:")
            print(f"   当前利用率: {current_utilization:.1f}%")
            print(f"   修复配置问题后理论利用率: {potential_utilization:.1f}%")
            print(f"   改进空间: +{potential_utilization - current_utilization:.1f}%")
            
            print("="*80)
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_unused_equipment()
    if success:
        print("\n🎉 未使用设备详细分析完成！")
    else:
        print("\n❌ 未使用设备分析失败！")

if __name__ == "__main__":
    main()