#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查排产结果脚本
"""

# 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 基础导入
import os
import logging
from datetime import datetime

# 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)-8s | %(message)s')
logger = logging.getLogger('SchedulingResultChecker')

def main():
    """主函数"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from sqlalchemy import text
            db = app.extensions['sqlalchemy']
            
            # 检查排产结果
            result = db.session.execute(text('''
                SELECT 
                    COUNT(*) as scheduled_lots,
                    COUNT(DISTINCT HANDLER_ID) as scheduled_devices
                FROM lotprioritydone
            ''')).fetchone()
            
            # 检查设备总数
            total_devices = db.session.execute(text('''
                SELECT COUNT(DISTINCT HANDLER_ID) FROM eqp_status
            ''')).scalar()
            
            # 检查等待批次
            waiting_lots = db.session.execute(text('''
                SELECT COUNT(*) FROM et_wait_lot
            ''')).scalar()
            
            utilization = result[1] / total_devices * 100 if total_devices > 0 else 0
            success_rate = result[0] / waiting_lots * 100 if waiting_lots > 0 else 0
            
            print("="*50)
            print("🎯 排产结果汇总:")
            print(f"   已排产批次: {result[0]}")
            print(f"   参与设备数: {result[1]} / {total_devices}")
            print(f"   设备利用率: {utilization:.1f}%")
            print(f"   等待批次数: {waiting_lots}")
            print(f"   排产成功率: {success_rate:.1f}%")
            print("="*50)
            
            # 判断效果
            if result[1] > 30:
                print(f"🎉 改进成功！设备利用数从约30台增加到{result[1]}台")
            else:
                print(f"⚠️  设备利用数仍然只有{result[1]}台，未达到改进目标")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    main()