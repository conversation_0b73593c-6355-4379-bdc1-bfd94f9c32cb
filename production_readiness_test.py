#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产就绪验证测试
验证系统是否完全准备好投入生产使用
"""

# 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import time
import json
import requests
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('production_readiness_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('ProductionReadinessTest')

def test_system_startup():
    """测试系统启动状态"""
    
    print("🚀 测试系统启动状态...")
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        print("✅ Flask应用创建成功")
        
        with app.app_context():
            from app import db
            
            # 数据库连接测试
            result = db.session.execute(db.text("SELECT 1")).fetchone()
            if result:
                print("✅ 数据库连接正常")
            else:
                print("❌ 数据库连接异常")
                return False
            
        return True
        
    except Exception as e:
        print(f"❌ 系统启动测试失败: {e}")
        return False

def test_core_functionality():
    """测试核心功能"""
    
    print("\n🔧 测试核心功能...")
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app import db
            from app.models import User, EQP_STATUS, ET_WAIT_LOT, ET_UPH_EQP
            from app.services.real_scheduling_service import RealSchedulingService
            
            # 测试数据库查询
            user_count = User.query.count()
            equipment_count = EQP_STATUS.query.count()
            lot_count = ET_WAIT_LOT.query.count()
            uph_count = ET_UPH_EQP.query.count()
            
            print(f"✅ 数据统计: 用户{user_count}个, 设备{equipment_count}个, 批次{lot_count}个, UPH配置{uph_count}个")
            
            # 测试排产服务
            scheduling_service = RealSchedulingService()
            preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
            
            if preloaded_data and len(preloaded_data) > 0:
                print("✅ 排产服务数据预加载成功")
                print(f"   预加载数据项: {len(preloaded_data)} 个")
            else:
                print("❌ 排产服务数据预加载失败")
                return False
            
        return True
        
    except Exception as e:
        print(f"❌ 核心功能测试失败: {e}")
        return False

def test_performance_benchmarks():
    """测试性能基准"""
    
    print("\n📊 运行性能基准测试...")
    
    try:
        from performance_benchmark import PerformanceBenchmark
        
        benchmark = PerformanceBenchmark()
        
        # 运行轻量级基准测试
        print("  API性能测试...")
        api_result = benchmark.benchmark_api_performance(20)  # 减少迭代次数
        
        print("  数据库性能测试...")
        db_result = benchmark.benchmark_database_performance(10)  # 减少迭代次数
        
        print("  排产算法性能测试...")
        algo_result = benchmark.benchmark_scheduling_algorithm(3)  # 减少迭代次数
        
        # 输出结果
        if api_result and 'response_time_ms' in api_result:
            avg_api_time = api_result['response_time_ms']['mean']
            print(f"✅ API平均响应时间: {avg_api_time:.2f}ms")
        
        if db_result and 'query_time_ms' in db_result:
            avg_db_time = db_result['query_time_ms']['mean']
            print(f"✅ 数据库平均查询时间: {avg_db_time:.2f}ms")
        
        if algo_result and 'execution_time_ms' in algo_result:
            avg_algo_time = algo_result['execution_time_ms']['mean']
            print(f"✅ 排产算法平均执行时间: {avg_algo_time:.2f}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}")
        return False

def test_stability_framework():
    """测试稳定性框架"""
    
    print("\n🏗️ 测试稳定性框架...")
    
    try:
        from stability_test_runner import StabilityTestRunner
        
        runner = StabilityTestRunner()
        
        # 运行基础功能测试
        basic_test = runner.run_basic_functionality_test()
        
        if basic_test and 'tests' in basic_test:
            passed_tests = sum(1 for test in basic_test['tests'].values() if test.get('status') == 'pass')
            total_tests = len(basic_test['tests'])
            
            print(f"✅ 基础功能测试: {passed_tests}/{total_tests} 通过")
            
            if passed_tests >= total_tests * 0.8:  # 80%通过率认为成功
                return True
            else:
                print("⚠️ 基础功能测试通过率不足80%")
                return False
        else:
            print("❌ 基础功能测试返回异常")
            return False
        
    except Exception as e:
        print(f"❌ 稳定性框架测试失败: {e}")
        return False

def test_monitoring_system():
    """测试监控系统"""
    
    print("\n📈 测试监控系统...")
    
    try:
        from app.utils.performance_monitor import PerformanceMonitor
        
        monitor = PerformanceMonitor()
        
        # 收集性能指标
        metrics = monitor.collect_system_metrics()
        
        if metrics and 'cpu_percent' in metrics:
            print(f"✅ 系统监控正常: CPU {metrics['cpu_percent']:.1f}%, 内存 {metrics.get('memory_mb', 0):.1f}MB")
            return True
        else:
            print("❌ 监控系统指标收集失败")
            return False
        
    except Exception as e:
        print(f"❌ 监控系统测试失败: {e}")
        return False

def run_production_readiness_test():
    """运行完整的生产就绪测试"""
    
    print("🎯 AEC-FT 智能调度平台生产就绪验证")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("系统启动测试", test_system_startup),
        ("核心功能测试", test_core_functionality),
        ("性能基准测试", test_performance_benchmarks),
        ("稳定性框架测试", test_stability_framework),
        ("监控系统测试", test_monitoring_system)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append({
                'name': test_name,
                'status': 'pass' if result else 'fail',
                'timestamp': datetime.now().isoformat()
            })
            
            if result:
                passed_tests += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
                
        except Exception as e:
            test_results.append({
                'name': test_name,
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            print(f"❌ {test_name}: 异常 - {e}")
    
    # 生成测试报告
    success_rate = (passed_tests / total_tests) * 100
    
    print("\n" + "=" * 60)
    print("📊 生产就绪验证结果:")
    print(f"  测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 系统已通过生产就绪验证!")
        print("✅ 系统完全准备好投入生产使用")
        production_ready = True
    else:
        print("⚠️ 系统未完全通过生产就绪验证")
        print("❌ 建议修复失败的测试项目后再投产")
        production_ready = False
    
    # 保存测试报告
    report = {
        'test_summary': {
            'timestamp': datetime.now().isoformat(),
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'production_ready': production_ready
        },
        'test_results': test_results
    }
    
    report_file = f"production_readiness_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📋 测试报告已保存: {report_file}")
    print("=" * 60)
    
    return production_ready

if __name__ == "__main__":
    success = run_production_readiness_test()
    
    if success:
        print("\n🚀 系统投产: 准备就绪 ✅")
        print("   可以安全地投入生产环境使用")
    else:
        print("\n⚠️ 系统投产: 需要进一步检查")
        print("   建议解决测试中发现的问题")