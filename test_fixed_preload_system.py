#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的数据预加载系统
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_fixed_preload.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('FixedPreloadTester')

def test_fixed_preload_system():
    """测试修复后的预加载系统"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.real_scheduling_service import RealSchedulingService
            
            scheduling_service = RealSchedulingService()
            
            print("="*80)
            print("🔍 测试修复后的数据预加载系统")
            print("="*80)
            
            # ==================== 第一步：测试预加载功能 ====================
            print(f"\n📊 第一步：测试修复后的预加载功能...")
            
            try:
                print(f"   调用 _preload_all_data_with_deterministic_cache()...")
                preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
                
                print(f"   预加载结果分析:")
                print(f"   - 预加载数据类型: {type(preloaded_data)}")
                if preloaded_data:
                    print(f"   - 预加载数据键: {list(preloaded_data.keys())}")
                    
                    for key, value in preloaded_data.items():
                        if isinstance(value, (list, tuple)):
                            print(f"     - {key}: {len(value)} 条记录 (类型: {type(value).__name__})")
                            if len(value) > 0:
                                print(f"       样本: {type(value[0]).__name__}")
                        elif isinstance(value, dict):
                            print(f"     - {key}: {len(value)} 个条目 (类型: {type(value).__name__})")
                        else:
                            print(f"     - {key}: {type(value).__name__}")
                else:
                    print(f"   ❌ 预加载数据为空!")
                    
            except Exception as e:
                print(f"   ❌ 预加载测试失败: {e}")
                import traceback
                print(f"   错误详情: {traceback.format_exc()}")
                return False
            
            # ==================== 第二步：重点检查recipe_files ====================
            print(f"\n🔍 第二步：重点检查recipe_files修复效果...")
            
            recipe_files = preloaded_data.get('recipe_files', [])
            print(f"   recipe_files 分析:")
            print(f"   - 类型: {type(recipe_files)}")
            print(f"   - 长度: {len(recipe_files)}")
            
            if isinstance(recipe_files, list) and len(recipe_files) > 0:
                print(f"   ✅ recipe_files 格式正确且有数据!")
                
                # 显示前几条记录
                print(f"   前3条记录示例:")
                for i, recipe in enumerate(recipe_files[:3], 1):
                    device = (recipe.get('DEVICE') or '').strip()
                    stage = (recipe.get('STAGE') or '').strip()
                    handler_config = (recipe.get('HANDLER_CONFIG') or '').strip()
                    approval = (recipe.get('APPROVAL_STATE') or '').strip()
                    
                    print(f"     {i}. DEVICE='{device}', STAGE='{stage}', HANDLER_CONFIG='{handler_config}', APPROVAL='{approval}'")
                    
                # 统计Released状态的配方
                released_count = sum(1 for r in recipe_files if (r.get('APPROVAL_STATE') or '').strip() == 'Released')
                print(f"   - Released状态配方: {released_count} 条")
                
            elif isinstance(recipe_files, dict):
                print(f"   ⚠️ recipe_files 仍然是字典格式，需要进一步修复")
                print(f"   字典键: {list(recipe_files.keys())[:5]}...")
                
            else:
                print(f"   ❌ recipe_files 为空或格式错误!")
            
            # ==================== 第三步：测试优先级配置修复 ====================
            print(f"\n🔍 第三步：测试优先级配置修复...")
            
            device_priority = preloaded_data.get('device_priority', {})
            lot_priority = preloaded_data.get('lot_priority', {})
            
            print(f"   优先级配置分析:")
            print(f"   - device_priority: {len(device_priority)} 条配置")
            print(f"   - lot_priority: {len(lot_priority)} 条配置")
            
            if device_priority:
                print(f"   ✅ 设备优先级配置加载成功!")
                sample_devices = list(device_priority.items())[:3]
                for device, priority in sample_devices:
                    print(f"     - {device}: 优先级 {priority}")
            else:
                print(f"   ❌ 设备优先级配置为空")
                
            if lot_priority:
                print(f"   ✅ 批次优先级配置加载成功!")
                sample_lots = list(lot_priority.items())[:3]
                for lot_id, priority in sample_lots:
                    print(f"     - {lot_id}: 优先级 {priority}")
            else:
                print(f"   ❌ 批次优先级配置为空")
            
            # ==================== 第四步：测试实际排产匹配 ====================
            print(f"\n🔍 第四步：测试修复后的实际排产匹配...")
            
            wait_lots = preloaded_data.get('wait_lots', [])
            print(f"   待排产批次: {len(wait_lots)} 个")
            
            if wait_lots and recipe_files:
                # 测试前5个批次的匹配能力
                success_count = 0
                test_count = min(5, len(wait_lots))
                
                for i, lot in enumerate(wait_lots[:test_count], 1):
                    lot_id = lot.get('LOT_ID', '')
                    device = (lot.get('DEVICE') or '').strip()
                    stage = (lot.get('STAGE') or '').strip()
                    
                    print(f"\n   测试批次 {i}: {lot_id} ({device}-{stage})")
                    
                    try:
                        config = scheduling_service.get_lot_configuration_requirements_optimized(lot, preloaded_data)
                        
                        if config:
                            success_count += 1
                            source = config.get('SOURCE', 'Unknown')
                            print(f"     ✅ 匹配成功: 来源={source}")
                        else:
                            print(f"     ❌ 匹配失败")
                            
                    except Exception as e:
                        print(f"     ❌ 匹配异常: {str(e)[:50]}")
                
                match_rate = success_count / test_count * 100 if test_count > 0 else 0
                print(f"\n   🎯 修复后匹配成功率: {match_rate:.1f}% ({success_count}/{test_count})")
                
                if match_rate > 0:
                    print(f"   ✅ 修复成功！预加载系统已恢复正常功能")
                else:
                    print(f"   ⚠️ 仍有其他问题需要解决")
            
            print("="*80)
            return True
            
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = test_fixed_preload_system()
    if success:
        print("\n🎉 修复后的预加载系统测试完成！")
    else:
        print("\n❌ 测试失败！")

if __name__ == "__main__":
    main()