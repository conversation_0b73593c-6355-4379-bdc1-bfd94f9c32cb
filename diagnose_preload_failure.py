#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断数据预加载失败的根本原因
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.DEBUG,  # 使用DEBUG级别获取更多信息
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('preload_diagnosis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('PreloadDiagnosis')

def diagnose_preload_failure():
    """诊断预加载失败的原因"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            from app.services.real_scheduling_service import RealSchedulingService
            
            data_manager = DataSourceManager()
            scheduling_service = RealSchedulingService()
            
            print("="*80)
            print("🔍 数据预加载失败诊断")
            print("="*80)
            
            # ==================== 第一步：直接测试数据获取 ====================
            print(f"\n📊 第一步：直接测试数据获取")
            
            try:
                print(f"   测试 get_table_data('et_recipe_file')...")
                recipe_result = data_manager.get_table_data('et_recipe_file')
                recipe_data = recipe_result.get('data', [])
                print(f"   ✅ 直接获取成功: {len(recipe_data)} 条记录")
                
                if recipe_data:
                    sample = recipe_data[0]
                    print(f"   样本数据类型: {type(sample)}")
                    if hasattr(sample, 'keys'):
                        print(f"   样本字段: {list(sample.keys())[:5]}...")
                    else:
                        print(f"   样本内容: {str(sample)[:100]}...")
                        
            except Exception as e:
                print(f"   ❌ 直接获取失败: {e}")
                import traceback
                print(f"   详细错误: {traceback.format_exc()}")
            
            # ==================== 第二步：测试预加载函数 ====================
            print(f"\n🔍 第二步：测试预加载函数")
            
            try:
                print(f"   调用 _preload_all_data_with_deterministic_cache()...")
                preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
                
                print(f"   预加载结果类型: {type(preloaded_data)}")
                print(f"   预加载数据键值: {list(preloaded_data.keys()) if preloaded_data else 'None'}")
                
                if preloaded_data:
                    for key, value in preloaded_data.items():
                        if isinstance(value, (list, tuple)):
                            print(f"     - {key}: {len(value)} 条记录")
                        else:
                            print(f"     - {key}: {type(value)}")
                
                # 重点检查recipe_files
                recipe_files = preloaded_data.get('recipe_files', [])
                print(f"\n   recipe_files详细分析:")
                print(f"     - 类型: {type(recipe_files)}")
                print(f"     - 长度: {len(recipe_files)}")
                
                if recipe_files:
                    first_item = recipe_files[0]
                    print(f"     - 第一项类型: {type(first_item)}")
                    print(f"     - 第一项内容: {str(first_item)[:200]}...")
                else:
                    print(f"     - ❌ recipe_files 为空!")
                    
            except Exception as e:
                print(f"   ❌ 预加载失败: {e}")
                import traceback
                print(f"   详细错误: {traceback.format_exc()}")
            
            # ==================== 第三步：查看预加载源码逻辑 ====================
            print(f"\n🔍 第三步：分析预加载源码逻辑")
            
            # 检查DataSourceManager中的方法
            print(f"   检查DataSourceManager中的预加载相关方法:")
            methods = [method for method in dir(data_manager) if 'preload' in method.lower() or 'recipe' in method.lower()]
            print(f"   相关方法: {methods}")
            
            # 检查RealSchedulingService中的预加载方法
            print(f"\n   检查RealSchedulingService中的预加载方法:")
            try:
                # 直接查看方法的源码定义
                import inspect
                source_lines = inspect.getsource(scheduling_service._preload_all_data_with_deterministic_cache)
                print(f"   方法定义长度: {len(source_lines.splitlines())} 行")
                
                # 查找关键词
                if 'recipe' in source_lines.lower():
                    print(f"   ✅ 方法中包含recipe相关逻辑")
                else:
                    print(f"   ❌ 方法中未找到recipe相关逻辑")
                    
                if 'et_recipe_file' in source_lines.lower():
                    print(f"   ✅ 方法中包含et_recipe_file")
                elif 'recipe_file' in source_lines.lower():
                    print(f"   ⚠️ 方法中只有recipe_file，可能是表名错误")
                else:
                    print(f"   ❌ 方法中未找到recipe表名")
                    
            except Exception as e:
                print(f"   无法获取源码: {e}")
            
            # ==================== 第四步：手动模拟预加载过程 ====================
            print(f"\n🔍 第四步：手动模拟预加载过程")
            
            try:
                print(f"   手动获取各个数据源...")
                
                # 模拟预加载过程
                manual_preload = {}
                
                # 获取wait_lots
                wait_lots_result = data_manager.get_table_data('et_wait_lot')
                manual_preload['wait_lots'] = wait_lots_result.get('data', [])
                print(f"     wait_lots: {len(manual_preload['wait_lots'])} 条")
                
                # 获取equipment_status
                equipment_result = data_manager.get_table_data('EQP_STATUS')
                manual_preload['equipment_status'] = equipment_result.get('data', [])
                print(f"     equipment_status: {len(manual_preload['equipment_status'])} 条")
                
                # 获取test_specs
                test_spec_result = data_manager.get_table_data('et_ft_test_spec')
                manual_preload['test_specs'] = test_spec_result.get('data', [])
                print(f"     test_specs: {len(manual_preload['test_specs'])} 条")
                
                # 关键：获取recipe_files
                recipe_result = data_manager.get_table_data('et_recipe_file')
                manual_preload['recipe_files'] = recipe_result.get('data', [])
                print(f"     recipe_files (et_recipe_file): {len(manual_preload['recipe_files'])} 条")
                
                # 尝试用错误的表名
                try:
                    wrong_recipe_result = data_manager.get_table_data('recipe_file')
                    wrong_recipe_data = wrong_recipe_result.get('data', [])
                    print(f"     recipe_files (recipe_file): {len(wrong_recipe_data)} 条")
                except:
                    print(f"     recipe_files (recipe_file): 获取失败")
                
                print(f"\n   手动预加载总结:")
                for key, value in manual_preload.items():
                    print(f"     - {key}: {len(value)} 条记录")
                
            except Exception as e:
                print(f"   ❌ 手动预加载失败: {e}")
                import traceback
                print(f"   详细错误: {traceback.format_exc()}")
            
            # ==================== 第五步：检查配置和环境问题 ====================
            print(f"\n🔍 第五步：检查配置和环境问题")
            
            # 检查数据库连接配置
            try:
                print(f"   检查数据库连接状态...")
                # 测试直接SQL查询
                from sqlalchemy import text
                
                # 直接查询recipe表
                try:
                    # 这里需要获取数据库连接
                    print(f"   尝试直接SQL查询...")
                    
                    # 使用DataSourceManager的内部连接
                    recipe_count = len(data_manager.get_table_data('et_recipe_file').get('data', []))
                    print(f"   ✅ 直接查询et_recipe_file成功: {recipe_count} 条")
                    
                except Exception as e:
                    print(f"   ❌ 直接SQL查询失败: {e}")
            
            except Exception as e:
                print(f"   配置检查失败: {e}")
            
            # ==================== 第六步：查看实际的预加载调用栈 ====================
            print(f"\n🔍 第六步：深入分析预加载调用栈")
            
            try:
                # 设置更详细的日志级别
                logging.getLogger('app.services.real_scheduling_service').setLevel(logging.DEBUG)
                logging.getLogger('app.services.data_source_manager').setLevel(logging.DEBUG)
                
                print(f"   重新调用预加载（详细日志模式）...")
                preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
                
                print(f"   预加载完成，检查结果...")
                if preloaded_data:
                    recipe_files = preloaded_data.get('recipe_files', [])
                    if not recipe_files:
                        print(f"   🚨 确认：recipe_files确实为空！")
                        
                        # 检查其他可能的键名
                        possible_keys = [key for key in preloaded_data.keys() if 'recipe' in key.lower()]
                        print(f"   可能的recipe相关键: {possible_keys}")
                        
                        # 检查是否有其他形式的数据
                        for key, value in preloaded_data.items():
                            if isinstance(value, list) and len(value) > 1000:  # recipe表通常有较多记录
                                print(f"   可能的recipe数据（键名错误）: {key} = {len(value)} 条")
                
            except Exception as e:
                print(f"   详细预加载分析失败: {e}")
                import traceback
                print(f"   错误详情: {traceback.format_exc()}")
            
            print("="*80)
            return True
            
    except Exception as e:
        logger.error(f"❌ 诊断执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = diagnose_preload_failure()
    if success:
        print("\n🎉 预加载失败诊断完成！")
    else:
        print("\n❌ 诊断失败！")

if __name__ == "__main__":
    main()