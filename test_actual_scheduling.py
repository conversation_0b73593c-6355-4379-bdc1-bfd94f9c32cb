#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际排产测试脚本 - 验证修复效果

测试修复后的实际排产效果，统计真实参与排产的设备数量
"""

# 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 基础导入
import os
import logging
from datetime import datetime

# 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_actual_scheduling.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('ActualSchedulingTest')

def test_scheduling_with_real_service():
    """使用真实排产服务进行测试"""
    logger.info("🧪 开始实际排产测试...")
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.real_scheduling_service import RealSchedulingService
            from sqlalchemy import text
            db = app.extensions['sqlalchemy']
            
            # 检查测试前状态
            before_stats = db.session.execute(text('''
                SELECT 
                    COUNT(*) as scheduled_lots,
                    COUNT(DISTINCT HANDLER_ID) as scheduled_devices
                FROM lotprioritydone
            ''')).fetchone()
            
            waiting_lots_count = db.session.execute(text('''
                SELECT COUNT(*) FROM et_wait_lot
            ''')).scalar()
            
            logger.info(f"📊 测试前状态:")
            logger.info(f"   已排产批次: {before_stats[0]}")
            logger.info(f"   参与设备数: {before_stats[1]}")
            logger.info(f"   等待批次数: {waiting_lots_count}")
            
            # 创建排产服务实例
            scheduling_service = RealSchedulingService()
            
            # 执行排产（使用智能综合策略）
            logger.info("🚀 开始执行排产...")
            
            result = scheduling_service.execute_optimized_scheduling(
                algorithm='intelligent',
                optimization_target='balanced'
            )
            
            logger.info(f"✅ 排产执行完成")
            logger.info(f"   排产结果: {result}")
            
            # 检查测试后状态
            after_stats = db.session.execute(text('''
                SELECT 
                    COUNT(*) as scheduled_lots,
                    COUNT(DISTINCT HANDLER_ID) as scheduled_devices
                FROM lotprioritydone
            ''')).fetchone()
            
            logger.info(f"📈 测试后状态:")
            logger.info(f"   已排产批次: {after_stats[0]}")
            logger.info(f"   参与设备数: {after_stats[1]}")
            
            # 计算改善效果
            device_improvement = after_stats[1] - before_stats[1]
            lot_improvement = after_stats[0] - before_stats[0]
            
            logger.info(f"🎯 排产效果:")
            logger.info(f"   新增排产批次: +{lot_improvement}")
            logger.info(f"   新增参与设备: +{device_improvement}")
            
            if waiting_lots_count > 0:
                success_rate = after_stats[0] / waiting_lots_count * 100
                logger.info(f"   排产成功率: {success_rate:.1f}%")
            
            return {
                'scheduled_lots': after_stats[0],
                'scheduled_devices': after_stats[1],
                'success_rate': success_rate if waiting_lots_count > 0 else 0,
                'device_improvement': device_improvement,
                'lot_improvement': lot_improvement
            }
            
    except Exception as e:
        logger.error(f"❌ 排产测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def analyze_device_utilization():
    """分析设备利用情况"""
    logger.info("🔍 分析设备利用情况...")
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from sqlalchemy import text
            db = app.extensions['sqlalchemy']
            
            # 设备总数
            total_devices = db.session.execute(text('''
                SELECT COUNT(DISTINCT HANDLER_ID) FROM eqp_status
            ''')).scalar()
            
            # 参与排产的设备
            active_devices = db.session.execute(text('''
                SELECT COUNT(DISTINCT HANDLER_ID) FROM lotprioritydone
            ''')).scalar()
            
            utilization = active_devices / total_devices * 100 if total_devices > 0 else 0
            
            # 各设备的批次分布
            device_distribution = db.session.execute(text('''
                SELECT 
                    HANDLER_ID,
                    COUNT(*) as lot_count,
                    GROUP_CONCAT(DISTINCT DEVICE) as devices
                FROM lotprioritydone 
                GROUP BY HANDLER_ID 
                ORDER BY lot_count DESC
                LIMIT 20
            ''')).fetchall()
            
            logger.info(f"📊 设备利用分析:")
            logger.info(f"   设备总数: {total_devices}")
            logger.info(f"   参与设备: {active_devices}")
            logger.info(f"   利用率: {utilization:.1f}%")
            
            logger.info("前20台活跃设备:")
            for device_id, lot_count, devices in device_distribution:
                devices_str = devices[:50] + "..." if len(devices) > 50 else devices
                logger.info(f"   {device_id}: {lot_count}批次 [{devices_str}]")
            
            # 检查未参与排产的设备
            idle_devices = db.session.execute(text('''
                SELECT e.HANDLER_ID, e.DEVICE, e.STAGE, e.STATUS
                FROM eqp_status e
                LEFT JOIN lotprioritydone l ON e.HANDLER_ID = l.HANDLER_ID
                WHERE l.HANDLER_ID IS NULL
                GROUP BY e.HANDLER_ID
                LIMIT 10
            ''')).fetchall()
            
            logger.info(f"未参与排产的设备样本（前10）:")
            for handler_id, device, stage, status in idle_devices:
                logger.info(f"   {handler_id}: {device} [{stage}] ({status})")
            
            return {
                'total_devices': total_devices,
                'active_devices': active_devices,
                'utilization': utilization,
                'idle_count': total_devices - active_devices
            }
            
    except Exception as e:
        logger.error(f"❌ 设备利用分析失败: {e}")
        return None

def main():
    """主函数"""
    logger.info("🧪 开始实际排产效果测试")
    logger.info("=" * 50)
    
    # 执行排产测试
    test_result = test_scheduling_with_real_service()
    
    if not test_result:
        logger.error("❌ 排产测试失败")
        return False
    
    # 分析设备利用情况
    utilization_result = analyze_device_utilization()
    
    if not utilization_result:
        logger.error("❌ 设备利用分析失败")
        return False
    
    # 汇总结果
    logger.info("\n" + "=" * 50)
    logger.info("🎯 实际测试结果汇总:")
    logger.info(f"   排产批次数: {test_result['scheduled_lots']}")
    logger.info(f"   参与设备数: {test_result['scheduled_devices']} / {utilization_result['total_devices']}")
    logger.info(f"   设备利用率: {utilization_result['utilization']:.1f}%")
    logger.info(f"   排产成功率: {test_result['success_rate']:.1f}%")
    
    # 判断是否达到预期
    expected_devices = 55  # 预期设备数
    if test_result['scheduled_devices'] >= expected_devices:
        logger.info(f"🎉 成功！设备利用率达到预期（{expected_devices}+台）")
    else:
        gap = expected_devices - test_result['scheduled_devices']
        logger.warning(f"⚠️  未达预期，还缺少{gap}台设备参与排产")
    
    return True

if __name__ == "__main__":
    success = main()
    print("🎉 测试完成" if success else "❌ 测试失败")