{"name": "aec-ft-intelligent-commander-platform-*******-claude", "version": "1.0.0", "description": "aps", "main": "frontend_cache_optimization.js", "directories": {"doc": "docs", "test": "tests"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Oliverfang-2025/AEC-FT-Intelligent-Commander-Platform-1.0.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Oliverfang-2025/AEC-FT-Intelligent-Commander-Platform-1.0/issues"}, "homepage": "https://github.com/Oliverfang-2025/AEC-FT-Intelligent-Commander-Platform-1.0#readme"}