#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LSTR工序配置修复脚本 - 为YX0125GA0419批次添加必要的配置数据
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_lstr_configuration.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('LSLRConfigFix')

def check_current_configuration():
    """检查当前LSTR相关配置状态"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.services.data_source_manager import DataSourceManager
            data_manager = DataSourceManager()
            
            target_device = "JWQ85113CS-C293QFND-SJA1_TR1"
            target_stage = "LSTR"
            
            logger.info(f"🔍 检查当前配置状态")
            
            # 1. 检查测试规范
            logger.info(f"\n📋 Step 1: 检查ET_FT_TEST_SPEC表配置")
            test_specs = data_manager.get_table_data('ET_FT_TEST_SPEC')
            
            lstr_specs = []
            device_specs = []
            
            for spec in test_specs.get('data', []):
                if spec.get('DEVICE') == target_device:
                    device_specs.append({
                        'DEVICE': spec.get('DEVICE'),
                        'STAGE': spec.get('STAGE'),
                        'KIT_PN': spec.get('KIT_PN'),
                        'HB_PN': spec.get('HB_PN'),
                        'TB_PN': spec.get('TB_PN'),
                        'HANDLER_CONFIG': spec.get('HANDLER_CONFIG'),
                        'EQP_CLASS': spec.get('EQP_CLASS'),
                        'APPROVAL_STATE': spec.get('APPROVAL_STATE')
                    })
                    
                if spec.get('STAGE') == target_stage:
                    lstr_specs.append(spec)
            
            logger.info(f"目标设备 {target_device} 的现有STAGE配置:")
            for spec in device_specs:
                logger.info(f"   STAGE: {spec['STAGE']}, EQP_CLASS: {spec['EQP_CLASS']}, APPROVAL: {spec['APPROVAL_STATE']}")
            
            logger.info(f"系统中所有LSTR工序配置: {len(lstr_specs)}个")
            for spec in lstr_specs:
                logger.info(f"   DEVICE: {spec.get('DEVICE')}, KIT_PN: {spec.get('KIT_PN')}")
            
            # 2. 检查设备配置
            logger.info(f"\n📋 Step 2: 检查EQP_STATUS表LSTR设备")
            equipment_status = data_manager.get_table_data('EQP_STATUS')
            
            lstr_equipment = []
            all_classes = set()
            all_stages = set()
            
            for equipment in equipment_status.get('data', []):
                eqp_class = equipment.get('EQP_CLASS', '')
                stage = equipment.get('STAGE', '')
                handler_id = equipment.get('HANDLER_ID', '')
                
                all_classes.add(eqp_class)
                all_stages.add(stage)
                
                # 查找LSTR相关设备
                if ('LSTR' in stage.upper() or 'LSTR' in eqp_class.upper() or 
                    'TAPE' in stage.upper() or 'REEL' in stage.upper() or
                    'TAPE' in eqp_class.upper() or 'REEL' in eqp_class.upper()):
                    lstr_equipment.append(equipment)
            
            logger.info(f"现有EQP_CLASS类型: {sorted(all_classes)}")
            logger.info(f"现有STAGE类型: {sorted([s for s in all_stages if s])}")
            logger.info(f"LSTR相关设备: {len(lstr_equipment)}台")
            
            # 3. 配置缺失分析
            logger.info(f"\n📋 Step 3: 配置缺失分析")
            
            missing_test_spec = not any(spec['STAGE'] == target_stage for spec in device_specs)
            missing_equipment = len(lstr_equipment) == 0
            
            if missing_test_spec:
                logger.error(f"❌ 缺失测试规范: {target_device} + {target_stage}")
            else:
                logger.info(f"✅ 测试规范存在: {target_device} + {target_stage}")
                
            if missing_equipment:
                logger.error(f"❌ 缺失LSTR设备: 系统中无任何LSTR/TAPE/REEL设备")
            else:
                logger.info(f"✅ LSTR设备存在: {len(lstr_equipment)}台")
            
            return {
                'missing_test_spec': missing_test_spec,
                'missing_equipment': missing_equipment,
                'device_specs': device_specs,
                'lstr_equipment': lstr_equipment,
                'all_classes': all_classes,
                'all_stages': all_stages
            }
            
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def generate_fix_sql():
    """生成修复SQL语句"""
    logger.info(f"\n🔧 生成配置修复SQL语句")
    
    # 测试规范SQL
    test_spec_sql = """
-- 添加LSTR工序测试规范
INSERT INTO ET_FT_TEST_SPEC (
    DEVICE, STAGE, KIT_PN, HB_PN, TB_PN, 
    HANDLER_CONFIG, EQP_CLASS, APPROVAL_STATE,
    CREATE_TIME, UPDATE_TIME
) VALUES (
    'JWQ85113CS-C293QFND-SJA1_TR1', 
    'LSTR', 
    'KIT_LSTR_QFN_001', 
    'HB_LSTR_QFN_001', 
    'TB_LSTR_QFN_001',
    'LSTR_HANDLER', 
    'TAPE', 
    'Released',
    NOW(),
    NOW()
);
"""
    
    # 设备配置SQL
    equipment_sql = """
-- 添加LSTR编带设备
INSERT INTO EQP_STATUS (
    HANDLER_ID, DEVICE, STAGE, EQP_CLASS, 
    HANDLER_CONFIG, STATUS, TEMPERATURE_RANGE,
    KIT_PN, HB_PN, TB_PN,
    CREATE_TIME, UPDATE_TIME
) VALUES 
('LSTR-TAPE-001', 'JWQ85113CS-C293QFND-SJA1_TR1', 'LSTR', 'TAPE', 'LSTR_HANDLER', 'IDLE', '', 
 'KIT_LSTR_QFN_001', 'HB_LSTR_QFN_001', 'TB_LSTR_QFN_001', NOW(), NOW()),
('LSTR-TAPE-002', 'JWQ85113CS-C293QFND-SJA1_TR1', 'LSTR', 'TAPE', 'LSTR_HANDLER', 'IDLE', '', 
 'KIT_LSTR_QFN_001', 'HB_LSTR_QFN_001', 'TB_LSTR_QFN_001', NOW(), NOW());
"""
    
    # 验证查询SQL
    verify_sql = """
-- 验证配置是否添加成功
SELECT 'TEST_SPEC' as TYPE, DEVICE, STAGE, KIT_PN, HANDLER_CONFIG, EQP_CLASS, APPROVAL_STATE 
FROM ET_FT_TEST_SPEC 
WHERE DEVICE = 'JWQ85113CS-C293QFND-SJA1_TR1' AND STAGE = 'LSTR'
UNION ALL
SELECT 'EQUIPMENT' as TYPE, DEVICE, STAGE, HANDLER_ID as KIT_PN, HANDLER_CONFIG, EQP_CLASS, STATUS as APPROVAL_STATE
FROM EQP_STATUS 
WHERE STAGE = 'LSTR';
"""
    
    # 保存SQL文件
    sql_content = f"""-- LSTR工序配置修复SQL脚本
-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
-- 目的: 为批次YX0125GA0419添加LSTR工序所需的配置

{test_spec_sql}

{equipment_sql}

{verify_sql}
"""
    
    with open('fix_lstr_configuration.sql', 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    logger.info(f"✅ SQL脚本已保存到: fix_lstr_configuration.sql")
    
    return {
        'test_spec_sql': test_spec_sql,
        'equipment_sql': equipment_sql,
        'verify_sql': verify_sql
    }

def suggest_configuration_values():
    """基于现有配置建议合理的LSTR配置值"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.data_source_manager import DataSourceManager
            data_manager = DataSourceManager()
            
            logger.info(f"\n💡 基于现有配置分析合理配置建议")
            
            # 分析现有配置模式
            test_specs = data_manager.get_table_data('ET_FT_TEST_SPEC')
            equipment_status = data_manager.get_table_data('EQP_STATUS')
            
            target_device = "JWQ85113CS-C293QFND-SJA1_TR1"
            device_specs = [spec for spec in test_specs.get('data', []) if spec.get('DEVICE') == target_device]
            
            # 分析KIT_PN模式
            kit_patterns = []
            handler_configs = []
            eqp_classes = set()
            
            for spec in device_specs:
                kit_pn = spec.get('KIT_PN', '')
                handler_config = spec.get('HANDLER_CONFIG', '')
                eqp_class = spec.get('EQP_CLASS', '')
                
                if kit_pn:
                    kit_patterns.append(kit_pn)
                if handler_config:
                    handler_configs.append(handler_config)
                if eqp_class:
                    eqp_classes.add(eqp_class)
            
            logger.info(f"该设备现有配置模式分析:")
            logger.info(f"   KIT_PN模式: {kit_patterns}")
            logger.info(f"   HANDLER_CONFIG模式: {list(set(handler_configs))}")
            logger.info(f"   EQP_CLASS模式: {list(eqp_classes)}")
            
            # 建议LSTR配置
            suggested_config = {
                'KIT_PN': 'KIT_LSTR_QFN_001',  # 基于QFN4*4-24封装
                'HB_PN': 'HB_LSTR_QFN_001',
                'TB_PN': 'TB_LSTR_QFN_001',
                'HANDLER_CONFIG': 'LSTR_HANDLER',  # 编带机专用配置
                'EQP_CLASS': 'TAPE',  # 编带设备类型
                'TEMPERATURE_RANGE': '',  # LSTR通常不需要温度控制
                'HANDLER_ID_PREFIX': 'LSTR-TAPE'
            }
            
            logger.info(f"建议的LSTR配置:")
            for key, value in suggested_config.items():
                logger.info(f"   {key}: {value}")
            
            return suggested_config
            
    except Exception as e:
        logger.error(f"❌ 配置建议生成失败: {e}")
        return None

def main():
    """主函数"""
    logger.info("🚀 开始LSTR工序配置修复")
    
    # 1. 检查当前配置状态
    logger.info(f"\n=== 阶段1: 检查当前配置状态 ===")
    config_status = check_current_configuration()
    
    if not config_status:
        logger.error("❌ 无法检查配置状态，修复中断")
        return False
    
    # 2. 生成配置建议
    logger.info(f"\n=== 阶段2: 生成配置建议 ===")
    suggested_config = suggest_configuration_values()
    
    # 3. 生成修复SQL
    logger.info(f"\n=== 阶段3: 生成修复SQL ===")
    sql_scripts = generate_fix_sql()
    
    # 4. 总结和建议
    logger.info(f"\n=== 修复总结 ===")
    
    if config_status['missing_test_spec']:
        logger.error("❌ 需要添加测试规范: ET_FT_TEST_SPEC表")
    if config_status['missing_equipment']:
        logger.error("❌ 需要添加LSTR设备: EQP_STATUS表")
    
    logger.info(f"\n📋 修复步骤:")
    logger.info(f"1. ✅ 已生成SQL修复脚本: fix_lstr_configuration.sql")
    logger.info(f"2. 🔧 执行SQL脚本添加必要配置")
    logger.info(f"3. 🧪 重新测试YX0125GA0419批次匹配")
    logger.info(f"4. ✅ 验证排产功能正常")
    
    logger.info(f"\n⚠️ 重要提醒:")
    logger.info(f"   - 请在执行SQL前备份相关表")
    logger.info(f"   - 确认LSTR设备的实际配置参数")
    logger.info(f"   - 验证HANDLER_CONFIG是否与实际设备匹配")
    
    return True

if __name__ == "__main__":
    success = main()
    print("🎉 配置修复脚本执行完成" if success else "❌ 配置修复脚本执行失败")