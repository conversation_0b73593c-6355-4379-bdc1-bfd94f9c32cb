#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化失败批次性能 - 添加数据库索引和查询优化
提升排产失败批次功能的性能表现

使用标准化测试脚本模板，确保Flask上下文正确管理
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('optimize_failed_lots_performance.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('OptimizeFailedLotsPerformance')

def check_existing_indexes():
    """检查现有索引"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("🔍 检查scheduling_failed_lots表的现有索引...")
                
                # 检查现有索引
                show_indexes_sql = "SHOW INDEX FROM scheduling_failed_lots"
                cursor.execute(show_indexes_sql)
                indexes = cursor.fetchall()
                
                logger.info(f"📋 现有索引信息 (共{len(indexes)}个):")
                existing_indexes = {}
                for idx in indexes:
                    if isinstance(idx, dict):
                        key_name = idx['Key_name']
                        column_name = idx['Column_name']
                        index_type = idx['Index_type']
                        logger.info(f"   - {key_name}: {column_name} ({index_type})")
                        if key_name not in existing_indexes:
                            existing_indexes[key_name] = []
                        existing_indexes[key_name].append(column_name)
                    else:
                        key_name = idx[2]
                        column_name = idx[4]
                        index_type = idx[10]
                        logger.info(f"   - {key_name}: {column_name} ({index_type})")
                        if key_name not in existing_indexes:
                            existing_indexes[key_name] = []
                        existing_indexes[key_name].append(column_name)
                
                cursor.close()
                return existing_indexes
                
    except Exception as e:
        logger.error(f"❌ 检查现有索引失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

def add_performance_indexes():
    """添加性能优化索引"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 检查现有索引
                existing_indexes = check_existing_indexes()
                
                logger.info("🔧 添加性能优化索引...")
                
                # 定义需要添加的索引
                indexes_to_add = [
                    {
                        'name': 'idx_timestamp_desc',
                        'columns': ['timestamp'],
                        'type': 'DESC',
                        'description': '时间倒序索引，优化按时间查询'
                    },
                    {
                        'name': 'idx_device_stage',
                        'columns': ['device', 'stage'],
                        'type': 'NORMAL',
                        'description': '产品和工序复合索引，优化筛选查询'
                    },
                    {
                        'name': 'idx_session_timestamp',
                        'columns': ['session_id', 'timestamp'],
                        'type': 'DESC',
                        'description': '会话时间复合索引，优化当前会话查询'
                    },
                    {
                        'name': 'idx_lot_id_unique',
                        'columns': ['lot_id'],
                        'type': 'NORMAL', 
                        'description': '批次ID索引，优化精确查找'
                    },
                    {
                        'name': 'idx_failure_reason',
                        'columns': ['failure_reason(100)'],  # 前缀索引
                        'type': 'NORMAL',
                        'description': '失败原因前缀索引，优化失败类型查询'
                    }
                ]
                
                added_count = 0
                for index_info in indexes_to_add:
                    index_name = index_info['name']
                    columns = index_info['columns']
                    index_type = index_info['type']
                    description = index_info['description']
                    
                    # 检查索引是否已存在
                    if index_name in existing_indexes:
                        logger.info(f"⏭️  索引 {index_name} 已存在，跳过创建")
                        continue
                    
                    try:
                        # 构建创建索引的SQL
                        if index_type == 'DESC':
                            # 倒序索引
                            columns_str = ', '.join([f"{col} DESC" for col in columns])
                        else:
                            # 普通索引
                            columns_str = ', '.join(columns)
                        
                        create_index_sql = f"CREATE INDEX {index_name} ON scheduling_failed_lots ({columns_str})"
                        
                        logger.info(f"🔨 创建索引: {index_name} ({description})")
                        cursor.execute(create_index_sql)
                        logger.info(f"✅ 索引 {index_name} 创建成功")
                        added_count += 1
                        
                    except Exception as idx_error:
                        if "Duplicate key name" in str(idx_error):
                            logger.info(f"⏭️  索引 {index_name} 已存在，跳过创建")
                        else:
                            logger.error(f"❌ 创建索引 {index_name} 失败: {idx_error}")
                
                logger.info(f"📈 性能优化完成，新增 {added_count} 个索引")
                cursor.close()
                return added_count > 0
                
    except Exception as e:
        logger.error(f"❌ 添加性能索引失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def analyze_query_performance():
    """分析查询性能"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("📊 分析查询性能...")
                
                # 测试查询性能
                test_queries = [
                    {
                        'name': '按时间倒序查询',
                        'sql': 'SELECT * FROM scheduling_failed_lots ORDER BY timestamp DESC LIMIT 50'
                    },
                    {
                        'name': '按设备和工序筛选',
                        'sql': "SELECT * FROM scheduling_failed_lots WHERE device LIKE '%Test%' AND stage = 'FT'"
                    },
                    {
                        'name': '按会话ID查询',
                        'sql': "SELECT * FROM scheduling_failed_lots WHERE session_id = 'TEST_SESSION' ORDER BY timestamp DESC"
                    },
                    {
                        'name': '按失败原因统计',
                        'sql': 'SELECT failure_reason, COUNT(*) as count FROM scheduling_failed_lots GROUP BY failure_reason'
                    }
                ]
                
                for query in test_queries:
                    try:
                        # 使用EXPLAIN分析查询执行计划
                        explain_sql = f"EXPLAIN {query['sql']}"
                        cursor.execute(explain_sql)
                        explain_result = cursor.fetchall()
                        
                        logger.info(f"🔍 {query['name']}:")
                        for row in explain_result:
                            if isinstance(row, dict):
                                logger.info(f"   类型: {row.get('type', 'N/A')}, 索引: {row.get('key', 'N/A')}, 行数: {row.get('rows', 'N/A')}")
                            else:
                                logger.info(f"   类型: {row[1] if len(row) > 1 else 'N/A'}, 索引: {row[4] if len(row) > 4 else 'N/A'}, 行数: {row[8] if len(row) > 8 else 'N/A'}")
                    
                    except Exception as query_error:
                        logger.warning(f"⚠️ 查询分析失败: {query['name']} - {query_error}")
                
                cursor.close()
                return True
                
    except Exception as e:
        logger.error(f"❌ 查询性能分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def optimize_table_structure():
    """优化表结构"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("🔧 优化表结构...")
                
                # 优化表引擎和字符集（如果需要）
                optimize_sql = """
                ALTER TABLE scheduling_failed_lots 
                ENGINE=InnoDB 
                DEFAULT CHARSET=utf8mb4 
                COLLATE=utf8mb4_unicode_ci
                """
                
                try:
                    cursor.execute(optimize_sql)
                    logger.info("✅ 表结构优化完成")
                except Exception as opt_error:
                    if "not supported by" not in str(opt_error):
                        logger.warning(f"⚠️ 表结构优化警告: {opt_error}")
                
                # 分析表以更新统计信息
                analyze_sql = "ANALYZE TABLE scheduling_failed_lots"
                cursor.execute(analyze_sql)
                logger.info("✅ 表统计信息更新完成")
                
                cursor.close()
                return True
                
    except Exception as e:
        logger.error(f"❌ 表结构优化失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_performance():
    """性能测试"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            import time
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("⚡ 执行性能测试...")
                
                # 测试查询
                test_queries = [
                    "SELECT COUNT(*) FROM scheduling_failed_lots",
                    "SELECT * FROM scheduling_failed_lots ORDER BY timestamp DESC LIMIT 50",
                    "SELECT device, COUNT(*) FROM scheduling_failed_lots GROUP BY device",
                    "SELECT failure_reason, COUNT(*) FROM scheduling_failed_lots GROUP BY failure_reason"
                ]
                
                for query in test_queries:
                    start_time = time.time()
                    cursor.execute(query)
                    result = cursor.fetchall()
                    end_time = time.time()
                    
                    execution_time = (end_time - start_time) * 1000  # 转换为毫秒
                    logger.info(f"⏱️  查询执行时间: {execution_time:.2f}ms (返回{len(result)}行)")
                
                cursor.close()
                return True
                
    except Exception as e:
        logger.error(f"❌ 性能测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("🚀 开始失败批次性能优化...")
    
    # 测试1: 检查现有索引
    logger.info("=" * 50)
    logger.info("阶段1: 检查现有索引")
    existing_indexes = check_existing_indexes()
    
    # 测试2: 添加性能索引
    logger.info("=" * 50) 
    logger.info("阶段2: 添加性能索引")
    success2 = add_performance_indexes()
    if not success2:
        logger.error("❌ 添加性能索引失败")
        return False
    
    # 测试3: 优化表结构
    logger.info("=" * 50)
    logger.info("阶段3: 优化表结构")
    success3 = optimize_table_structure()
    if not success3:
        logger.error("❌ 表结构优化失败")
        return False
    
    # 测试4: 分析查询性能
    logger.info("=" * 50)
    logger.info("阶段4: 分析查询性能")
    success4 = analyze_query_performance()
    if not success4:
        logger.error("❌ 查询性能分析失败")
        return False
    
    # 测试5: 性能测试
    logger.info("=" * 50)
    logger.info("阶段5: 性能测试")
    success5 = test_performance()
    if not success5:
        logger.error("❌ 性能测试失败")
        return False
    
    logger.info("🎉 性能优化完成!")
    return True

if __name__ == "__main__":
    success = main()
    print("🎉 性能优化: 成功" if success else "❌ 性能优化: 失败")