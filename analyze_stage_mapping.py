#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析STAGE映射配置和排产算法中的使用方式
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('stage_mapping_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('StageMappingAnalyzer')

def analyze_stage_mapping():
    """分析STAGE映射配置和使用"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            from app import db
            from sqlalchemy import text
            
            data_manager = DataSourceManager()
            
            print("="*80)
            print("🔍 分析STAGE映射配置和BAKING工序问题")
            print("="*80)
            
            # ==================== 第一步：检查stage_mapping_config表 ====================
            print(f"\n📊 第一步：检查stage_mapping_config表结构和数据...")
            
            with db.engine.connect() as conn:
                # 检查表结构
                result = conn.execute(text("DESCRIBE stage_mapping_config"))
                columns = result.fetchall()
                print(f"   stage_mapping_config表字段:")
                for col in columns:
                    print(f"     - {col[0]} ({col[1]})")
                
                # 获取所有映射数据
                result = conn.execute(text("SELECT * FROM stage_mapping_config WHERE is_active = true ORDER BY target_stage, priority"))
                mappings = result.fetchall()
                print(f"\n   活跃的STAGE映射: {len(mappings)} 条")
                
                # 按target_stage分组显示
                mappings_by_target = defaultdict(list)
                for mapping in mappings:
                    mapping_dict = dict(mapping._mapping)
                    target_stage = mapping_dict.get('target_stage', '')
                    mappings_by_target[target_stage].append(mapping_dict)
                
                print(f"\n   映射规则按目标STAGE分组:")
                for target_stage, rules in mappings_by_target.items():
                    print(f"     {target_stage}: {len(rules)} 条规则")
                    for i, rule in enumerate(rules[:3], 1):  # 显示前3条
                        source_stage = rule.get('source_stage', '')
                        equipment_type = rule.get('equipment_type', '')
                        priority = rule.get('priority', '')
                        print(f"       {i}. {source_stage} → {target_stage} (设备类型:{equipment_type}, 优先级:{priority})")
                
                # 重点检查BAKING相关映射
                print(f"\n   🔍 BAKING工序相关映射:")
                baking_mappings = [mapping for mapping in mappings 
                                 if 'BAKING' in str(mapping._mapping).upper()]
                
                if baking_mappings:
                    print(f"   找到 {len(baking_mappings)} 条BAKING相关映射:")
                    for i, mapping in enumerate(baking_mappings, 1):
                        mapping_dict = dict(mapping._mapping)
                        print(f"     {i}. {mapping_dict}")
                else:
                    print(f"   ❌ 没有找到BAKING相关的映射规则！")
            
            # ==================== 第二步：分析待排产批次的STAGE需求 ====================
            print(f"\n📊 第二步：分析待排产批次的STAGE需求...")
            
            wait_lots_result = data_manager.get_table_data('et_wait_lot')
            wait_lots = wait_lots_result.get('data', [])
            
            stage_demand = defaultdict(int)
            device_stage_demand = defaultdict(int)
            
            for lot in wait_lots:
                stage = (lot.get('STAGE') or '').strip()
                device = (lot.get('DEVICE') or '').strip()
                if stage:
                    stage_demand[stage] += 1
                if device and stage:
                    device_stage_demand[f"{device}-{stage}"] += 1
            
            print(f"   待排产批次STAGE需求分布:")
            for stage, count in sorted(stage_demand.items(), key=lambda x: x[1], reverse=True):
                print(f"     - {stage}: {count} 个批次")
                
            baking_lots = stage_demand.get('BAKING', 0)
            print(f"\n   🎯 需要BAKING工序的批次: {baking_lots} 个")
            
            # ==================== 第三步：分析设备的STAGE能力 ====================
            print(f"\n📊 第三步：分析设备的STAGE能力...")
            
            equipment_result = data_manager.get_table_data('EQP_STATUS')
            equipment_data = equipment_result.get('data', [])
            
            equipment_stage_capability = defaultdict(int)
            equipment_device_stage = defaultdict(int)
            
            for eqp in equipment_data:
                stage = (eqp.get('STAGE') or '').strip()
                device = (eqp.get('DEVICE') or '').strip()
                handler_id = (eqp.get('HANDLER_ID') or '').strip()
                
                if stage and handler_id:
                    equipment_stage_capability[stage] += 1
                if device and stage:
                    equipment_device_stage[f"{device}-{stage}"] += 1
            
            print(f"   设备STAGE能力分布:")
            for stage, count in sorted(equipment_stage_capability.items(), key=lambda x: x[1], reverse=True):
                print(f"     - {stage}: {count} 台设备")
                
            baking_equipment = equipment_stage_capability.get('BAKING', 0)
            print(f"\n   🎯 能处理BAKING工序的设备: {baking_equipment} 台")
            
            if baking_equipment == 0:
                print(f"   ❌ 关键问题：没有设备配置为BAKING工序！")
            
            # ==================== 第四步：检查排产算法是否使用映射 ====================
            print(f"\n🔍 第四步：检查排产算法中的STAGE映射使用...")
            
            # 导入排产服务
            from app.services.real_scheduling_service import RealSchedulingService
            scheduling_service = RealSchedulingService()
            
            # 获取预加载数据
            preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
            stage_mappings = preloaded_data.get('stage_mappings', [])
            
            print(f"   预加载的STAGE映射: {len(stage_mappings)} 条")
            
            if stage_mappings:
                print(f"   映射数据样本:")
                for i, mapping in enumerate(stage_mappings[:3], 1):
                    print(f"     {i}. {mapping}")
                
                # 检查是否有BAKING相关映射
                baking_stage_mappings = [m for m in stage_mappings 
                                       if 'BAKING' in str(m).upper()]
                print(f"   BAKING相关映射: {len(baking_stage_mappings)} 条")
                for mapping in baking_stage_mappings:
                    print(f"     - {mapping}")
            
            # ==================== 第五步：测试映射算法 ====================
            print(f"\n🔍 第五步：测试STAGE映射算法...")
            
            # 测试一个需要BAKING的批次
            baking_lots = [lot for lot in wait_lots if (lot.get('STAGE') or '').strip() == 'BAKING']
            if baking_lots:
                test_lot = baking_lots[0]
                lot_id = test_lot.get('LOT_ID', '')
                device = (test_lot.get('DEVICE') or '').strip()
                stage = (test_lot.get('STAGE') or '').strip()
                
                print(f"   测试批次: {lot_id} (需要 {device} - {stage})")
                
                # 尝试获取配置需求
                try:
                    config = scheduling_service.get_lot_configuration_requirements_optimized(test_lot, preloaded_data)
                    
                    if config:
                        print(f"   ✅ 获取到配置: {config}")
                    else:
                        print(f"   ❌ 未能获取配置")
                        
                        # 手动检查是否有可替代的STAGE
                        print(f"   检查可能的STAGE替代方案:")
                        
                        # 查看是否有其他STAGE的设备可以兼容
                        compatible_stages = []
                        for eqp in equipment_data:
                            eqp_device = (eqp.get('DEVICE') or '').strip()
                            eqp_stage = (eqp.get('STAGE') or '').strip()
                            
                            if eqp_device == device and eqp_stage != stage and eqp_stage:
                                compatible_stages.append(eqp_stage)
                        
                        unique_stages = list(set(compatible_stages))
                        print(f"     同DEVICE的其他STAGE: {unique_stages}")
                        
                        # 检查映射规则是否支持转换
                        if stage_mappings:
                            possible_mappings = []
                            for mapping in stage_mappings:
                                source_stage = mapping.get('source_stage', '')
                                target_stage = mapping.get('target_stage', '')
                                
                                if target_stage == stage:  # 可以映射到BAKING
                                    if source_stage in unique_stages:  # 设备有这个能力
                                        possible_mappings.append(mapping)
                            
                            print(f"     可用的映射规则: {len(possible_mappings)} 条")
                            for mapping in possible_mappings:
                                print(f"       - {mapping}")
                        
                except Exception as e:
                    print(f"   配置获取异常: {e}")
            else:
                print(f"   没有找到需要BAKING工序的批次进行测试")
            
            # ==================== 第六步：解决方案建议 ====================
            print(f"\n💡 第六步：解决方案建议...")
            
            print(f"   🎯 问题总结:")
            print(f"   - 需要BAKING工序的批次: {baking_lots} 个")
            print(f"   - 能处理BAKING的设备: {baking_equipment} 台")
            print(f"   - STAGE映射规则: {len(stage_mappings)} 条")
            
            if baking_lots > 0 and baking_equipment == 0:
                print(f"\n   🚀 解决方案:")
                print(f"   1. 方案一：配置设备支持BAKING工序")
                print(f"      - 将现有设备的STAGE字段设置为BAKING")
                print(f"      - 优点：直接解决问题")
                print(f"      - 缺点：可能影响现有工序")
                
                print(f"   2. 方案二：增加STAGE映射规则")
                print(f"      - 在stage_mapping_config表中添加映射规则")
                print(f"      - 例如：HOT → BAKING, COLD → BAKING")
                print(f"      - 优点：不影响现有配置，灵活性高")
                
                print(f"   3. 方案三：修改排产算法")
                print(f"      - 在设备匹配时启用STAGE映射查找")
                print(f"      - 优点：充分利用现有映射配置")
                
                # 检查哪些设备可能适合做BAKING
                print(f"\n   🔍 可能适合BAKING工序的设备分析:")
                suitable_for_baking = []
                
                for eqp in equipment_data:
                    stage = (eqp.get('STAGE') or '').strip()
                    handler_id = (eqp.get('HANDLER_ID') or '').strip()
                    status = (eqp.get('STATUS') or '').strip()
                    device = (eqp.get('DEVICE') or '').strip()
                    
                    # 检查是否是高温设备（BAKING通常需要高温）
                    if stage in ['HOT', 'BURN'] and status != 'DOWN':
                        suitable_for_baking.append({
                            'HANDLER_ID': handler_id,
                            'DEVICE': device,
                            'CURRENT_STAGE': stage,
                            'STATUS': status
                        })
                
                print(f"   高温设备（可能适合BAKING）: {len(suitable_for_baking)} 台")
                for i, eqp in enumerate(suitable_for_baking[:5], 1):
                    print(f"     {i}. {eqp['HANDLER_ID']} | {eqp['DEVICE'][:30]}{'...' if len(eqp['DEVICE'])>30 else ''} | {eqp['CURRENT_STAGE']} → BAKING")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_stage_mapping()
    if success:
        print("\n🎉 STAGE映射分析完成！")
    else:
        print("\n❌ 分析失败！")

if __name__ == "__main__":
    main()