#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多用户并发压力测试 - 验证邮件处理器连接池优化效果
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
import time
import threading
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('multi_user_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('MultiUserTest')

class MultiUserConcurrentTest:
    def __init__(self):
        self.baseline_connections = None
        self.test_results = []
        self.concurrent_users = 4  # 模拟4个用户
        
    def simulate_user_session(self, user_id, duration_minutes=10):
        """模拟单个用户会话"""
        try:
            from app import create_app
            app, socketio = create_app()
            
            with app.app_context():
                logger.info(f"👤 用户{user_id}: 开始会话")
                
                # 模拟用户操作
                from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
                from app.models import EmailConfig
                
                # 获取或创建邮件配置
                email_config = EmailConfig.query.first()
                if not email_config:
                    # 创建测试配置
                    email_config = EmailConfig(
                        name=f"test_config_{user_id}",
                        host="test.com",
                        username="test",
                        password="test"
                    )
                    db.session.add(email_config)
                    db.session.commit()
                
                # 创建邮件处理器实例
                email_processor = HighPerformanceEmailProcessor(email_config)
                
                start_time = time.time()
                end_time = start_time + (duration_minutes * 60)
                operation_count = 0
                
                while time.time() < end_time:
                    try:
                        # 模拟邮件处理操作
                        test_data = b"test email content"
                        is_dup = email_processor._is_duplicate_file(test_data, f"test_{user_id}_{operation_count}.txt")
                        
                        operation_count += 1
                        
                        if operation_count % 50 == 0:
                            logger.info(f"👤 用户{user_id}: 已执行{operation_count}次操作")
                            
                        time.sleep(0.1)  # 100ms间隔
                        
                    except Exception as e:
                        logger.error(f"👤 用户{user_id}: 操作失败 - {e}")
                        break
                
                logger.info(f"👤 用户{user_id}: 会话结束，总操作: {operation_count}")
                return {
                    'user_id': user_id,
                    'operations': operation_count,
                    'duration': time.time() - start_time,
                    'success': True
                }
                
        except Exception as e:
            logger.error(f"👤 用户{user_id}: 会话失败 - {e}")
            return {
                'user_id': user_id,
                'error': str(e),
                'success': False
            }
    
    def get_connection_metrics(self):
        """获取连接池指标"""
        try:
            from app import create_app
            app, socketio = create_app()
            
            with app.app_context():
                from app.utils.db_connection_pool import get_detailed_connection_metrics
                return get_detailed_connection_metrics()
                
        except Exception as e:
            logger.error(f"获取连接指标失败: {e}")
            return {'error': str(e)}
    
    def run_multi_user_test(self, test_duration_minutes=10):
        """运行多用户并发测试"""
        logger.info(f"🚀 启动多用户并发测试 - {self.concurrent_users}用户 × {test_duration_minutes}分钟")
        
        # 获取基线连接数
        logger.info("📊 获取基线连接池指标...")
        self.baseline_connections = self.get_connection_metrics()
        baseline_mysql_conns = self.baseline_connections.get('metrics', {}).get('global_mysql_connections', 0)
        logger.info(f"📌 基线MySQL连接数: {baseline_mysql_conns}")
        
        # 启动并发用户会话
        with ThreadPoolExecutor(max_workers=self.concurrent_users) as executor:
            logger.info(f"👥 启动{self.concurrent_users}个并发用户会话...")
            
            futures = []
            for user_id in range(1, self.concurrent_users + 1):
                future = executor.submit(self.simulate_user_session, user_id, test_duration_minutes)
                futures.append(future)
            
            # 监控连接池变化
            monitor_start = time.time()
            monitor_end = monitor_start + (test_duration_minutes * 60)
            peak_connections = baseline_mysql_conns
            
            while time.time() < monitor_end:
                try:
                    current_metrics = self.get_connection_metrics()
                    current_conns = current_metrics.get('metrics', {}).get('global_mysql_connections', 0)
                    
                    if current_conns > peak_connections:
                        peak_connections = current_conns
                    
                    logger.info(f"📊 实时监控: MySQL连接 {current_conns} (峰值: {peak_connections})")
                    time.sleep(30)  # 30秒检查一次
                    
                except Exception as e:
                    logger.error(f"监控连接失败: {e}")
                    time.sleep(30)
            
            # 等待所有用户会话完成
            logger.info("⏳ 等待所有用户会话完成...")
            results = []
            for future in futures:
                try:
                    result = future.result(timeout=test_duration_minutes * 60 + 60)
                    results.append(result)
                except Exception as e:
                    logger.error(f"用户会话超时或失败: {e}")
        
        # 获取最终连接池指标
        final_metrics = self.get_connection_metrics()
        final_mysql_conns = final_metrics.get('metrics', {}).get('global_mysql_connections', 0)
        
        # 生成测试报告
        test_report = {
            'test_timestamp': datetime.now().isoformat(),
            'concurrent_users': self.concurrent_users,
            'test_duration_minutes': test_duration_minutes,
            'baseline_connections': baseline_mysql_conns,
            'peak_connections': peak_connections,
            'final_connections': final_mysql_conns,
            'connection_increase': peak_connections - baseline_mysql_conns,
            'user_results': results,
            'success_rate': len([r for r in results if r.get('success')]) / len(results) if results else 0
        }
        
        # 保存测试报告
        with open('multi_user_concurrent_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        logger.info("📋 多用户并发测试报告:")
        logger.info(f"  基线连接数: {baseline_mysql_conns}")
        logger.info(f"  峰值连接数: {peak_connections}")
        logger.info(f"  最终连接数: {final_mysql_conns}")
        logger.info(f"  连接增长: {peak_connections - baseline_mysql_conns}")
        logger.info(f"  用户成功率: {test_report['success_rate']:.1%}")
        
        return test_report

def main():
    """多用户并发测试入口"""
    test = MultiUserConcurrentTest()
    report = test.run_multi_user_test(5)  # 5分钟测试
    
    print("🎉 多用户并发测试: 完成" if report['success_rate'] > 0.8 else "❌ 多用户并发测试: 失败")

if __name__ == "__main__":
    main()