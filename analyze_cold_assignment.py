#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析JWQ5103CSFQFNAT-J103_TR1产品Cold工序机台分配问题
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cold_assignment_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('ColdAssignmentAnalysis')

def analyze_cold_assignment():
    """分析Cold工序机台分配问题"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from sqlalchemy import create_engine, text
            from config.aps_config import config
            
            # 获取数据库配置
            engine = create_engine(
                f"mysql+pymysql://{config.DB_USER}:{config.DB_PASSWORD}@{config.DB_HOST}:{config.DB_PORT}/{config.DB_NAME}?charset=utf8mb4"
            )
            
            # 查询该产品的排产结果
            query1 = """
            SELECT 
                LOT_ID, EQP_ID, STAGE_ID, Priority_Score, Rank_in_EQP,
                START_TIME, END_TIME
            FROM lotprioritydone 
            WHERE LOT_ID LIKE '%JWQ5103CSFQFNAT-J103_TR1%' 
            AND STAGE_ID = 'Cold'
            ORDER BY Priority_Score DESC
            """
            
            print("=== JWQ5103CSFQFNAT-J103_TR1产品Cold工序排产结果 ===")
            
            with engine.connect() as conn:
                result = conn.execute(text(query1))
                rows = result.fetchall()
                
                if not rows:
                    print("❌ 没有找到该产品的排产结果")
                    return False
                
                print(f"✅ 总共找到 {len(rows)} 个批次")
                print()
                
                eqp_count = {}
                for row in rows:
                    print(f"批次: {row.LOT_ID}")
                    print(f"机台: {row.EQP_ID}")
                    print(f"优先级评分: {row.Priority_Score}")
                    print(f"机台内排名: {row.Rank_in_EQP}")
                    print(f"开始时间: {row.START_TIME}")
                    print(f"结束时间: {row.END_TIME}")
                    print("---")
                    
                    if row.EQP_ID in eqp_count:
                        eqp_count[row.EQP_ID] += 1
                    else:
                        eqp_count[row.EQP_ID] = 1
                
                print("\n=== 机台分配统计 ===")
                for eqp, count in eqp_count.items():
                    print(f"{eqp}: {count} 个批次")
                    
                # 查询该产品的测试规格
                query2 = """
                SELECT 
                    LOT_ID, STAGE_ID, EQP_ID, BASE_TEST_TIME, MIN_TEST_TIME, MAX_TEST_TIME,
                    SETUP_VALUE, SAME_SETUP_FLAG
                FROM et_ft_test_spec 
                WHERE LOT_ID LIKE '%JWQ5103CSFQFNAT-J103_TR1%' 
                AND STAGE_ID = 'Cold'
                """
                
                print("\n=== 测试规格信息 ===")
                result2 = conn.execute(text(query2))
                spec_rows = result2.fetchall()
                
                if spec_rows:
                    for row in spec_rows:
                        print(f"批次: {row.LOT_ID}")
                        print(f"指定机台: {row.EQP_ID}")
                        print(f"基础测试时间: {row.BASE_TEST_TIME}")
                        print(f"Setup值: {row.SETUP_VALUE}")
                        print(f"相同Setup标记: {row.SAME_SETUP_FLAG}")
                        print("---")
                else:
                    print("⚠️ 没有找到测试规格信息")
                
                # 查询Cold工序的可用机台
                query3 = """
                SELECT DISTINCT EQP_ID, STATUS, CURRENT_LOT_ID
                FROM eqp_status 
                WHERE STAGE_ID = 'Cold' 
                AND STATUS IN ('AVAILABLE', 'BUSY')
                ORDER BY EQP_ID
                """
                
                print("\n=== Cold工序可用机台状态 ===")
                result3 = conn.execute(text(query3))
                eqp_rows = result3.fetchall()
                
                available_eqps = []
                for row in eqp_rows:
                    print(f"机台: {row.EQP_ID}, 状态: {row.STATUS}, 当前批次: {row.CURRENT_LOT_ID}")
                    if row.STATUS == 'AVAILABLE':
                        available_eqps.append(row.EQP_ID)
                
                print(f"\n✅ 可用机台总数: {len(available_eqps)}")
                print(f"可用机台列表: {', '.join(available_eqps)}")
                
                # 分析是否所有批次都分到了同一台机台
                if len(eqp_count) == 1:
                    assigned_eqp = list(eqp_count.keys())[0]
                    print(f"\n⚠️ 发现问题：所有批次都分配到了同一台机台 {assigned_eqp}")
                    
                    # 检查其他机台是否可以运行这个产品
                    query4 = """
                    SELECT DISTINCT EQP_ID, STAGE_ID
                    FROM et_ft_test_spec 
                    WHERE LOT_ID LIKE '%JWQ5103CSFQFNAT%'
                    AND STAGE_ID = 'Cold'
                    AND EQP_ID != %s
                    """
                    
                    result4 = conn.execute(text(query4), (assigned_eqp,))
                    other_eqp_rows = result4.fetchall()
                    
                    if other_eqp_rows:
                        print(f"\n🔍 该产品在其他机台也有测试规格:")
                        for row in other_eqp_rows:
                            print(f"  - {row.EQP_ID}")
                    else:
                        print(f"\n💡 分析结果：该产品可能只有在 {assigned_eqp} 机台上才有测试规格")
                
                return True
                
    except Exception as e:
        logger.error(f"❌ 分析执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = analyze_cold_assignment()
    print("\n🎉 分析完成" if success else "\n❌ 分析失败")

if __name__ == "__main__":
    main()