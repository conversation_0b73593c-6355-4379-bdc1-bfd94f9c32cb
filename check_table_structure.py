#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查表结构
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)-8s | %(message)s')
logger = logging.getLogger('TableStructureChecker')

def main():
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from sqlalchemy import text
            db = app.extensions['sqlalchemy']
            
            # 检查et_wait_lot表结构
            columns = db.session.execute(text('DESCRIBE et_wait_lot')).fetchall()
            
            print("et_wait_lot表结构:")
            for col in columns:
                print(f"  {col[0]}: {col[1]}")
                
            # 检查lotprioritydone表结构  
            columns2 = db.session.execute(text('DESCRIBE lotprioritydone')).fetchall()
            
            print("\nlotprioritydone表结构:")
            for col in columns2:
                print(f"  {col[0]}: {col[1]}")
            
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    main()