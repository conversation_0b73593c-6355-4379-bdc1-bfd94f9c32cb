#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控面板 - 每5秒更新系统关键指标（修复版：避免重复创建Flask应用）
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
import time
import json
import pymysql
from datetime import datetime

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('realtime_monitor.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('RealtimeMonitor')

class RealtimeMonitor:
    def __init__(self):
        self.baseline_metrics = None
        self.alert_thresholds = {
            'mysql_connections': 60,
            'python_processes': 8,
            'error_rate': 5.0,
            'response_time': 2000  # ms
        }
        # 使用持久连接避免Flask应用重复创建
        self._mysql_connection = None
        
    def _get_mysql_connection(self):
        """获取持久MySQL连接"""
        if self._mysql_connection is None:
            try:
                self._mysql_connection = pymysql.connect(
                    host='localhost',
                    port=3306,
                    user='root',
                    password='WWWwww123!',
                    database='aps',
                    charset='utf8mb4'
                )
            except Exception as e:
                logger.error(f"MySQL连接失败: {e}")
                return None
        return self._mysql_connection
    
    def get_mysql_connections_count(self):
        """直接获取MySQL连接数"""
        try:
            conn = self._get_mysql_connection()
            if not conn:
                return 0
                
            with conn.cursor() as cursor:
                cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                result = cursor.fetchone()
                return int(result[1]) if result else 0
                
        except Exception as e:
            logger.error(f"获取MySQL连接数失败: {e}")
            # 重置连接以便下次重试
            self._mysql_connection = None
            return 0
    
    def get_comprehensive_metrics(self):
        """获取综合系统指标（无Flask应用创建）"""
        try:
            # 直接获取MySQL连接数
            mysql_connections = self.get_mysql_connections_count()
            
            # 系统进程指标
            try:
                import subprocess
                # MySQL进程
                mysql_proc = subprocess.run(['powershell', 'Get-Process -Name mysqld -ErrorAction SilentlyContinue | Measure-Object | Select-Object -ExpandProperty Count'], 
                                          capture_output=True, text=True, timeout=5)
                mysql_processes = int(mysql_proc.stdout.strip()) if mysql_proc.stdout.strip().isdigit() else 0
                
                # Python进程
                python_proc = subprocess.run(['powershell', 'Get-Process -Name python -ErrorAction SilentlyContinue | Measure-Object | Select-Object -ExpandProperty Count'], 
                                            capture_output=True, text=True, timeout=5)
                python_processes = int(python_proc.stdout.strip()) if python_proc.stdout.strip().isdigit() else 0
            except:
                mysql_processes = 0
                python_processes = 0
            
            return {
                'timestamp': datetime.now().isoformat(),
                'pool_metrics': {
                    'global_mysql_connections': mysql_connections,
                    'overview': {
                        'connection_reuse_rate': 0.0,
                        'pool_timeouts': 0,
                        'connection_errors': 0
                    }
                },
                'cache_metrics': {'status': 'unavailable'},
                'process_metrics': {
                    'mysql_processes': mysql_processes,
                    'python_processes': python_processes
                },
                'health_status': 'healthy' if mysql_connections < 60 else 'critical'
            }
                
        except Exception as e:
            logger.error(f"获取综合指标失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'status': 'error'
            }
    
    def check_alerts(self, metrics):
        """检查告警条件"""
        alerts = []
        
        if metrics.get('status') == 'error':
            alerts.append(f"🚨 CRITICAL: 系统指标获取失败")
            return alerts
        
        pool_metrics = metrics.get('pool_metrics', {})
        process_metrics = metrics.get('process_metrics', {})
        
        # MySQL连接数告警
        mysql_conns = pool_metrics.get('global_mysql_connections', 0)
        if mysql_conns > self.alert_thresholds['mysql_connections']:
            alerts.append(f"🚨 CRITICAL: MySQL连接数过高 {mysql_conns}")
        elif mysql_conns > self.alert_thresholds['mysql_connections'] * 0.8:
            alerts.append(f"⚠️ WARNING: MySQL连接数接近阈值 {mysql_conns}")
        
        # Python进程数告警
        python_procs = process_metrics.get('python_processes', 0)
        if python_procs > self.alert_thresholds['python_processes']:
            alerts.append(f"🚨 CRITICAL: Python进程数过多 {python_procs}")
        
        # 连接错误告警
        conn_errors = pool_metrics.get('overview', {}).get('connection_errors', 0)
        if conn_errors > 0:
            alerts.append(f"🚨 CRITICAL: 发现连接错误 {conn_errors}")
        
        # 连接池超时告警
        pool_timeouts = pool_metrics.get('overview', {}).get('pool_timeouts', 0)
        if pool_timeouts > 100:
            alerts.append(f"🚨 CRITICAL: 连接池超时过多 {pool_timeouts}")
        elif pool_timeouts > 50:
            alerts.append(f"⚠️ WARNING: 连接池超时较多 {pool_timeouts}")
        
        return alerts
    
    def display_realtime_dashboard(self, metrics):
        """显示实时监控面板"""
        pool_metrics = metrics.get('pool_metrics', {})
        process_metrics = metrics.get('process_metrics', {})
        cache_metrics = metrics.get('cache_metrics', {})
        
        print("\n" + "="*80)
        print(f"🎯 实时系统监控面板 - {datetime.now().strftime('%H:%M:%S')}")
        print("="*80)
        
        # 连接池状态
        print(f"🔗 MySQL连接池:")
        print(f"  📊 活跃连接: {pool_metrics.get('global_mysql_connections', 'N/A')}")
        print(f"  🔄 连接复用率: {pool_metrics.get('overview', {}).get('connection_reuse_rate', 0):.1%}")
        print(f"  ⏰ 超时次数: {pool_metrics.get('overview', {}).get('pool_timeouts', 'N/A')}")
        print(f"  ❌ 连接错误: {pool_metrics.get('overview', {}).get('connection_errors', 'N/A')}")
        
        # 进程状态
        print(f"\n🐍 进程状态:")
        print(f"  Python进程: {process_metrics.get('python_processes', 'N/A')}")
        print(f"  MySQL进程: {process_metrics.get('mysql_processes', 'N/A')}")
        
        # 缓存状态
        print(f"\n💾 缓存系统:")
        if cache_metrics.get('status') == 'unavailable':
            print(f"  状态: 缓存系统不可用")
        else:
            print(f"  命中率: {cache_metrics.get('hit_rate', 'N/A')}")
            print(f"  总请求: {cache_metrics.get('total_requests', 'N/A')}")
        
        # 健康状态
        print(f"\n🏥 系统健康:")
        print(f"  总体状态: {metrics.get('health_status', 'N/A')}")
        
        # 告警信息
        alerts = self.check_alerts(metrics)
        if alerts:
            print(f"\n🚨 告警信息:")
            for alert in alerts:
                print(f"  {alert}")
        else:
            print(f"\n✅ 无告警 - 系统运行正常")
    
    def run_realtime_monitoring(self, duration_hours=2):
        """运行实时监控"""
        logger.info(f"📺 启动实时监控面板 - 持续{duration_hours}小时")
        
        start_time = time.time()
        end_time = start_time + (duration_hours * 3600)
        check_count = 0
        
        try:
            while time.time() < end_time:
                check_count += 1
                
                # 获取指标
                metrics = self.get_comprehensive_metrics()
                
                # 显示监控面板
                self.display_realtime_dashboard(metrics)
                
                # 保存基线指标（第一次检查）
                if check_count == 1:
                    self.baseline_metrics = metrics
                    logger.info("📊 基线指标已记录")
                
                # 与基线对比
                if check_count > 1 and self.baseline_metrics:
                    self.compare_with_baseline(metrics)
                
                # 每分钟保存一次详细数据
                if check_count % 12 == 0:  # 12 * 5秒 = 1分钟
                    self.save_periodic_report(metrics, check_count)
                
                # 等待下次检查
                time.sleep(5)  # 5秒间隔
                
        except KeyboardInterrupt:
            logger.info("🛑 实时监控被用户中断")
        except Exception as e:
            logger.error(f"❌ 实时监控失败: {e}")
        
        logger.info(f"📊 实时监控完成 - 总检查次数: {check_count}")
        
        # 清理连接
        self.cleanup_connections()
    
    def compare_with_baseline(self, current_metrics):
        """与基线指标对比"""
        try:
            baseline_pool = self.baseline_metrics.get('pool_metrics', {})
            current_pool = current_metrics.get('pool_metrics', {})
            
            baseline_conns = baseline_pool.get('global_mysql_connections', 0)
            current_conns = current_pool.get('global_mysql_connections', 0)
            
            conn_change = current_conns - baseline_conns
            if abs(conn_change) > 10:
                logger.warning(f"⚠️ MySQL连接数显著变化: {baseline_conns} → {current_conns} ({conn_change:+d})")
            
            # 检查错误增长
            baseline_errors = baseline_pool.get('overview', {}).get('connection_errors', 0)
            current_errors = current_pool.get('overview', {}).get('connection_errors', 0)
            
            if current_errors > baseline_errors:
                logger.warning(f"🚨 新增连接错误: {baseline_errors} → {current_errors}")
                
        except Exception as e:
            logger.debug(f"基线对比失败: {e}")
    
    def save_periodic_report(self, metrics, check_count):
        """定期保存监控报告"""
        try:
            report = {
                'check_number': check_count,
                'timestamp': datetime.now().isoformat(),
                'metrics': metrics,
                'monitoring_duration_minutes': check_count * 5 / 60
            }
            
            with open(f'realtime_monitor_check_{check_count}.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
                
            logger.info(f"💾 第{check_count}次检查数据已保存")
            
        except Exception as e:
            logger.error(f"保存监控报告失败: {e}")
    
    def cleanup_connections(self):
        """清理数据库连接"""
        try:
            if self._mysql_connection:
                self._mysql_connection.close()
                self._mysql_connection = None
                logger.info("✅ MySQL连接已清理")
        except Exception as e:
            logger.error(f"连接清理失败: {e}")

def main():
    """实时监控入口"""
    monitor = RealtimeMonitor()
    monitor.run_realtime_monitoring(2)  # 2小时监控

if __name__ == "__main__":
    main()