#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析具体的批次设备匹配过程
"""

# 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 基础导入
import os
import logging

# 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)-8s | %(message)s')
logger = logging.getLogger('SpecificMatchingAnalyzer')

def main():
    """主函数"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from sqlalchemy import text
            db = app.extensions['sqlalchemy']
            
            print("="*60)
            print("🔍 具体批次设备匹配分析")
            print("="*60)
            
            # 1. 取一个具体的等待批次进行分析
            sample_lot = db.session.execute(text('''
                SELECT LOT_NUM, DEVICE, STAGE, PKG_PN 
                FROM et_wait_lot 
                LIMIT 1
            ''')).fetchone()
            
            if not sample_lot:
                print("❌ 没有找到等待批次")
                return False
                
            lot_num, lot_device, lot_stage, lot_pkg_pn = sample_lot
            
            print(f"📋 分析样本批次: {lot_num}")
            print(f"   需要设备: {lot_device}")
            print(f"   需要阶段: {lot_stage}")
            print(f"   封装类型: {lot_pkg_pn}")
            
            # 2. 查找该批次对应的配方
            recipes = db.session.execute(text('''
                SELECT HANDLER_CONFIG, DEVICE, STAGE, PKG_PN
                FROM et_recipe_file 
                WHERE PKG_PN = :pkg_pn AND DEVICE = :device
                LIMIT 5
            '''), {'pkg_pn': lot_pkg_pn, 'device': lot_device}).fetchall()
            
            print(f"\n📝 找到的配方:")
            if not recipes:
                print("   ❌ 没有找到匹配的配方")
                return False
                
            recipe_configs = []
            for config, device, stage, pkg_pn in recipes:
                recipe_configs.append(config)
                print(f"   {config}: {device} [{stage}] ({pkg_pn})")
            
            # 3. 查找支持这些HANDLER_CONFIG的设备
            for config in recipe_configs[:2]:  # 只分析前2个配置
                print(f"\n🏭 支持 {config} 的设备:")
                
                devices = db.session.execute(text('''
                    SELECT HANDLER_ID, DEVICE, STAGE, STATUS, HANDLER_CONFIG
                    FROM eqp_status 
                    WHERE HANDLER_CONFIG = :config
                    ORDER BY STATUS
                '''), {'config': config}).fetchall()
                
                if not devices:
                    print(f"   ❌ 没有设备支持 {config}")
                    continue
                
                available_count = 0
                for handler_id, device, stage, status, handler_config in devices:
                    status_symbol = "✅" if status in ['AVAILABLE', 'IDLE', 'READY'] else "❌"
                    stage_match = "✅" if stage == lot_stage else f"❌({stage}≠{lot_stage})"
                    device_match = "✅" if device == lot_device else f"❌({device}≠{lot_device})"
                    
                    if status in ['AVAILABLE', 'IDLE', 'READY']:
                        available_count += 1
                    
                    print(f"   {handler_id}: {status_symbol}{status} {stage_match} {device_match}")
                
                print(f"   📊 可用设备数: {available_count}/{len(devices)}")
                
                # 4. 检查实际是否被排产
                scheduled = db.session.execute(text('''
                    SELECT COUNT(*) FROM lotprioritydone 
                    WHERE HANDLER_ID IN (
                        SELECT HANDLER_ID FROM eqp_status WHERE HANDLER_CONFIG = :config
                    )
                '''), {'config': config}).scalar()
                
                print(f"   📈 实际参与排产的设备: {scheduled}台")
            
            # 5. 分析为什么这个批次没有被排产
            print(f"\n🤔 批次 {lot_num} 排产状态检查:")
            is_scheduled = db.session.execute(text('''
                SELECT COUNT(*) FROM lotprioritydone WHERE LOT_NUM = :lot_num
            '''), {'lot_num': lot_num}).scalar()
            
            if is_scheduled > 0:
                scheduled_device = db.session.execute(text('''
                    SELECT HANDLER_ID, DEVICE FROM lotprioritydone WHERE LOT_NUM = :lot_num
                '''), {'lot_num': lot_num}).fetchone()
                print(f"   ✅ 已被排产到: {scheduled_device[0]} ({scheduled_device[1]})")
            else:
                print(f"   ❌ 未被排产")
                
                # 分析可能的原因
                print(f"   🔍 可能的原因:")
                
                # 检查是否有可用设备
                available_devices = db.session.execute(text('''
                    SELECT COUNT(*) FROM eqp_status e
                    WHERE e.HANDLER_CONFIG IN (
                        SELECT HANDLER_CONFIG FROM et_recipe_file 
                        WHERE PKG_PN = :pkg_pn AND DEVICE = :device
                    )
                    AND e.STATUS IN ('AVAILABLE', 'IDLE', 'READY')
                    AND e.DEVICE = :device
                    AND e.STAGE = :stage
                '''), {
                    'pkg_pn': lot_pkg_pn, 
                    'device': lot_device,
                    'stage': lot_stage
                }).scalar()
                
                print(f"     完全匹配的可用设备: {available_devices}台")
                
                if available_devices == 0:
                    # 检查设备匹配但阶段不匹配
                    device_only = db.session.execute(text('''
                        SELECT COUNT(*) FROM eqp_status e
                        WHERE e.HANDLER_CONFIG IN (
                            SELECT HANDLER_CONFIG FROM et_recipe_file 
                            WHERE PKG_PN = :pkg_pn AND DEVICE = :device
                        )
                        AND e.STATUS IN ('AVAILABLE', 'IDLE', 'READY')
                        AND e.DEVICE = :device
                    '''), {
                        'pkg_pn': lot_pkg_pn, 
                        'device': lot_device
                    }).scalar()
                    
                    print(f"     设备匹配的可用设备: {device_only}台")
                    
                    if device_only == 0:
                        print(f"     ❌ 根本原因：没有支持该DEVICE的设备")
                    else:
                        print(f"     ❌ 根本原因：STAGE不匹配")
            
            print("="*60)
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    main()