#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析HANDLER_CONFIG匹配问题
"""

# 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 基础导入
import os
import logging
from collections import Counter

# 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)-8s | %(message)s')
logger = logging.getLogger('HandlerConfigAnalyzer')

def main():
    """主函数"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from sqlalchemy import text
            db = app.extensions['sqlalchemy']
            
            print("="*60)
            print("🔍 HANDLER_CONFIG匹配分析")
            print("="*60)
            
            # 1. 分析等待批次需要的HANDLER_CONFIG
            wait_recipes = db.session.execute(text('''
                SELECT HANDLER_CONFIG, COUNT(*) as count
                FROM et_recipe_file 
                WHERE PKG_PN IN (SELECT PKG_PN FROM et_wait_lot)
                GROUP BY HANDLER_CONFIG
                ORDER BY count DESC
                LIMIT 20
            ''')).fetchall()
            
            print("📋 等待批次需要的HANDLER_CONFIG（前20）:")
            wait_configs = {}
            for config, count in wait_recipes:
                wait_configs[config] = count
                print(f"   {config}: {count}个配方")
            
            # 2. 分析设备支持的HANDLER_CONFIG
            device_configs = db.session.execute(text('''
                SELECT HANDLER_CONFIG, COUNT(*) as count
                FROM eqp_status
                GROUP BY HANDLER_CONFIG
                ORDER BY count DESC
            ''')).fetchall()
            
            print(f"\n🏭 设备支持的HANDLER_CONFIG:")
            device_config_map = {}
            for config, count in device_configs:
                device_config_map[config] = count
                print(f"   {config}: {count}台设备")
            
            # 3. 分析匹配情况
            print(f"\n🎯 匹配分析:")
            matched_configs = set(wait_configs.keys()) & set(device_config_map.keys())
            unmatched_wait = set(wait_configs.keys()) - set(device_config_map.keys())
            unused_device = set(device_config_map.keys()) - set(wait_configs.keys())
            
            print(f"   完全匹配的配置: {len(matched_configs)}")
            for config in sorted(matched_configs):
                wait_count = wait_configs[config]
                device_count = device_config_map[config]
                print(f"     {config}: {wait_count}配方 ↔ {device_count}设备")
            
            print(f"\n   等待批次无设备支持的配置: {len(unmatched_wait)}")
            for config in sorted(unmatched_wait):
                print(f"     {config}: {wait_configs[config]}配方（无设备）")
            
            print(f"\n   设备无批次使用的配置: {len(unused_device)}")
            for config in sorted(unused_device)[:10]:  # 只显示前10个
                print(f"     {config}: {device_config_map[config]}设备（无批次）")
            
            # 4. 计算匹配率
            total_wait_configs = sum(wait_configs.values())
            matched_wait_configs = sum(wait_configs[config] for config in matched_configs)
            match_rate = matched_wait_configs / total_wait_configs * 100 if total_wait_configs > 0 else 0
            
            total_devices = sum(device_config_map.values())
            matched_devices = sum(device_config_map[config] for config in matched_configs)
            device_util_rate = matched_devices / total_devices * 100 if total_devices > 0 else 0
            
            print(f"\n📊 整体匹配统计:")
            print(f"   配方匹配率: {match_rate:.1f}% ({matched_wait_configs}/{total_wait_configs})")
            print(f"   设备可用率: {device_util_rate:.1f}% ({matched_devices}/{total_devices})")
            
            # 5. 找到最严重的不匹配
            print(f"\n🚨 最严重的不匹配情况（需要修复）:")
            for config in sorted(unmatched_wait, key=lambda x: wait_configs[x], reverse=True)[:5]:
                print(f"   {config}: {wait_configs[config]}个配方等待，但没有设备支持")
                
                # 尝试找到相似的设备配置
                similar_configs = []
                config_base = config.replace('_B', '').replace('_S', '').replace('_G', '').replace('T_', '').replace('H_', '')
                for device_config in device_config_map.keys():
                    if config_base in device_config or device_config in config_base:
                        similar_configs.append((device_config, device_config_map[device_config]))
                
                if similar_configs:
                    print(f"     可能匹配的设备配置:")
                    for sim_config, count in similar_configs[:3]:
                        print(f"       {sim_config}: {count}台设备")
            
            print("="*60)
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    main()