#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速run.py连接检查 - 单次运行版本
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import pymysql
import psutil
import subprocess
from datetime import datetime
from collections import defaultdict

def quick_check():
    """快速检查run.py连接状况"""
    target_pid = 39168
    
    print(f"🎯 run.py应用连接快速检查 - PID {target_pid}")
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 1. 检查进程状态
    try:
        process = psutil.Process(target_pid)
        memory_mb = process.memory_info().rss / 1024 / 1024
        cpu_percent = process.cpu_percent()
        print(f"🐍 进程状态: 运行中 | 内存: {memory_mb:.1f}MB | CPU: {cpu_percent:.1f}%")
        print(f"📅 启动时间: {datetime.fromtimestamp(process.create_time()).strftime('%Y-%m-%d %H:%M:%S')}")
    except psutil.NoSuchProcess:
        print(f"❌ PID {target_pid}进程不存在")
        return
    
    # 2. MySQL连接分析
    try:
        conn = pymysql.connect(
            host='localhost', port=3306, user='root',
            password='WWWwww123!', database='aps', charset='utf8mb4'
        )
        
        with conn.cursor() as cursor:
            # 总连接数
            cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
            total_connected = int(cursor.fetchone()[1])
            
            # 进程列表
            cursor.execute("SHOW PROCESSLIST")
            processes = cursor.fetchall()
            
            aps_connections = 0
            long_connections = []
            state_count = defaultdict(int)
            time_ranges = {'<60s': 0, '60-300s': 0, '300-900s': 0, '>900s': 0}
            
            for proc in processes:
                proc_id, user, host, db, command, time_sec, state = proc[:7]
                
                if user == 'root' and db == 'aps':
                    aps_connections += 1
                    state_count[state or 'None'] += 1
                    
                    # 时间分类
                    if time_sec < 60:
                        time_ranges['<60s'] += 1
                    elif time_sec < 300:
                        time_ranges['60-300s'] += 1
                    elif time_sec < 900:
                        time_ranges['300-900s'] += 1
                    else:
                        time_ranges['>900s'] += 1
                    
                    if time_sec > 60:
                        long_connections.append(f"ID={proc_id}, {time_sec}s, {state}, {command}")
            
            print(f"\n🗄️ MySQL连接分析:")
            print(f"   总连接数: {total_connected}/151 ({total_connected/151*100:.1f}%)")
            print(f"   APS连接数: {aps_connections}")
            print(f"   长连接数: {len(long_connections)}")
            
            print(f"\n📊 连接状态:")
            for state, count in state_count.items():
                print(f"   {state}: {count}")
            
            print(f"\n⏱️ 连接时间分布:")
            for time_range, count in time_ranges.items():
                print(f"   {time_range}: {count}")
            
            if long_connections:
                print(f"\n🔗 长连接详情 (前10个):")
                for long_conn in long_connections[:10]:
                    print(f"   {long_conn}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ MySQL分析失败: {e}")
    
    # 3. Netstat连接检查
    try:
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        target_connections = 0
        for line in lines:
            if ':3306' in line and 'ESTABLISHED' in line:
                parts = line.split()
                if len(parts) >= 5 and parts[4] == str(target_pid):
                    target_connections += 1
        
        print(f"\n🌐 Netstat连接: PID {target_pid}有{target_connections}个3306端口连接")
        
    except Exception as e:
        print(f"❌ Netstat分析失败: {e}")
    
    print("="*60)

if __name__ == "__main__":
    quick_check()