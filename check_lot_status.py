#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查批次YX0125GB0366的状态和位置
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('check_lot_status.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('CheckLotStatus')

def check_lot_status():
    """检查批次YX0125GB0366在各个表中的状态"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            lot_id = "YX0125GB0366"
            print(f"🔍 检查批次 {lot_id} 的状态...")
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 1. 检查在ET_WAIT_LOT表中的状态
                print(f"\n1. 检查在ET_WAIT_LOT表中的状态...")
                cursor.execute("SELECT * FROM et_wait_lot WHERE LOT_ID = %s", (lot_id,))
                wait_lot_result = cursor.fetchall()
                
                if wait_lot_result:
                    print(f"✅ 在ET_WAIT_LOT表中找到: {len(wait_lot_result)}条记录")
                    for record in wait_lot_result:
                        if isinstance(record, dict):
                            device = record.get('DEVICE', 'unknown')
                            stage = record.get('STAGE', 'unknown')
                            lot_type = record.get('LOT_TYPE', 'unknown')
                            good_qty = record.get('GOOD_QTY', 'unknown')
                        else:
                            # 获取列名
                            cursor.execute("DESCRIBE et_wait_lot")
                            columns = [col[0] for col in cursor.fetchall()]
                            record_dict = dict(zip(columns, record))
                            device = record_dict.get('DEVICE', 'unknown')
                            stage = record_dict.get('STAGE', 'unknown')
                            lot_type = record_dict.get('LOT_TYPE', 'unknown')
                            good_qty = record_dict.get('GOOD_QTY', 'unknown')
                        
                        print(f"  - 产品: {device}, 工序: {stage}, 类型: {lot_type}, 数量: {good_qty}")
                else:
                    print(f"❌ 在ET_WAIT_LOT表中未找到该批次")
                
                # 2. 检查在LOTPRIORITYDONE表中的状态
                print(f"\n2. 检查在LOTPRIORITYDONE表中的状态...")
                cursor.execute("SELECT * FROM lotprioritydone WHERE LOT_ID = %s", (lot_id,))
                done_lot_result = cursor.fetchall()
                
                if done_lot_result:
                    print(f"✅ 在LOTPRIORITYDONE表中找到: {len(done_lot_result)}条记录")
                    for record in done_lot_result:
                        if isinstance(record, dict):
                            device = record.get('DEVICE', 'unknown')
                            stage = record.get('STAGE', 'unknown')
                            handler_id = record.get('HANDLER_ID', 'unknown')
                            create_time = record.get('CREATE_TIME', 'unknown')
                        else:
                            cursor.execute("DESCRIBE lotprioritydone")
                            columns = [col[0] for col in cursor.fetchall()]
                            record_dict = dict(zip(columns, record))
                            device = record_dict.get('DEVICE', 'unknown')
                            stage = record_dict.get('STAGE', 'unknown')
                            handler_id = record_dict.get('HANDLER_ID', 'unknown')
                            create_time = record_dict.get('CREATE_TIME', 'unknown')
                        
                        print(f"  - 产品: {device}, 工序: {stage}, 分选机: {handler_id}, 时间: {create_time}")
                else:
                    print(f"❌ 在LOTPRIORITYDONE表中未找到该批次")
                
                # 3. 检查在SCHEDULING_FAILED_LOTS表中的状态
                print(f"\n3. 检查在SCHEDULING_FAILED_LOTS表中的状态...")
                cursor.execute("SELECT * FROM scheduling_failed_lots WHERE lot_id = %s", (lot_id,))
                failed_lot_result = cursor.fetchall()
                
                if failed_lot_result:
                    print(f"✅ 在SCHEDULING_FAILED_LOTS表中找到: {len(failed_lot_result)}条记录")
                    for record in failed_lot_result:
                        if isinstance(record, dict):
                            device = record.get('device', 'unknown')
                            stage = record.get('stage', 'unknown')
                            failure_reason = record.get('failure_reason', 'unknown')
                            timestamp = record.get('timestamp', 'unknown')
                        else:
                            cursor.execute("DESCRIBE scheduling_failed_lots")
                            columns = [col[0] for col in cursor.fetchall()]
                            record_dict = dict(zip(columns, record))
                            device = record_dict.get('device', 'unknown')
                            stage = record_dict.get('stage', 'unknown')
                            failure_reason = record_dict.get('failure_reason', 'unknown')
                            timestamp = record_dict.get('timestamp', 'unknown')
                        
                        print(f"  - 产品: {device}, 工序: {stage}")
                        print(f"  - 失败原因: {failure_reason}")
                        print(f"  - 时间: {timestamp}")
                else:
                    print(f"❌ 在SCHEDULING_FAILED_LOTS表中未找到该批次")
                
                # 4. 检查在FINAL_SCHEDULING_RESULT表中的状态
                print(f"\n4. 检查在FINAL_SCHEDULING_RESULT表中的状态...")
                try:
                    cursor.execute("SELECT * FROM final_scheduling_result WHERE LOT_ID = %s", (lot_id,))
                    final_result = cursor.fetchall()
                    
                    if final_result:
                        print(f"✅ 在FINAL_SCHEDULING_RESULT表中找到: {len(final_result)}条记录")
                        for record in final_result:
                            if isinstance(record, dict):
                                device = record.get('DEVICE', 'unknown')
                                handler_id = record.get('HANDLER_ID', 'unknown')
                                create_time = record.get('CREATE_TIME', 'unknown')
                            else:
                                cursor.execute("DESCRIBE final_scheduling_result")
                                columns = [col[0] for col in cursor.fetchall()]
                                record_dict = dict(zip(columns, record))
                                device = record_dict.get('DEVICE', 'unknown')
                                handler_id = record_dict.get('HANDLER_ID', 'unknown')
                                create_time = record_dict.get('CREATE_TIME', 'unknown')
                            
                            print(f"  - 产品: {device}, 分选机: {handler_id}, 时间: {create_time}")
                    else:
                        print(f"❌ 在FINAL_SCHEDULING_RESULT表中未找到该批次")
                except Exception as e:
                    print(f"⚠️ FINAL_SCHEDULING_RESULT表可能不存在: {e}")
                
                # 5. 检查相似批次
                print(f"\n5. 检查相似批次...")
                cursor.execute("SELECT LOT_ID FROM et_wait_lot WHERE LOT_ID LIKE %s LIMIT 5", (f"{lot_id[:8]}%",))
                similar_lots = cursor.fetchall()
                
                if similar_lots:
                    print(f"找到 {len(similar_lots)} 个相似批次:")
                    for lot in similar_lots:
                        if isinstance(lot, dict):
                            similar_id = list(lot.values())[0]
                        else:
                            similar_id = lot[0]
                        print(f"  - {similar_id}")
                
                # 6. 检查批次数据有效性
                print(f"\n6. 批次数据分析...")
                
                # 检查批次是否在待排产状态
                cursor.execute("""
                    SELECT COUNT(*) as wait_count FROM et_wait_lot 
                    WHERE LOT_ID = %s AND GOOD_QTY > 0
                """, (lot_id,))
                wait_valid = cursor.fetchone()
                wait_count = list(wait_valid.values())[0] if isinstance(wait_valid, dict) else wait_valid[0]
                
                # 检查批次是否已排产
                cursor.execute("""
                    SELECT COUNT(*) as done_count FROM lotprioritydone 
                    WHERE LOT_ID = %s
                """, (lot_id,))
                done_valid = cursor.fetchone()
                done_count = list(done_valid.values())[0] if isinstance(done_valid, dict) else done_valid[0]
                
                # 检查批次是否排产失败
                cursor.execute("""
                    SELECT COUNT(*) as failed_count FROM scheduling_failed_lots 
                    WHERE lot_id = %s
                """, (lot_id,))
                failed_valid = cursor.fetchone()
                failed_count = list(failed_valid.values())[0] if isinstance(failed_valid, dict) else failed_valid[0]
                
                print(f"📊 批次状态统计:")
                print(f"  - 待排产状态: {wait_count}条")
                print(f"  - 已排产状态: {done_count}条") 
                print(f"  - 排产失败状态: {failed_count}条")
                
                # 7. 结论分析
                print(f"\n📋 分析结论:")
                if failed_count > 0:
                    print(f"❌ 批次 {lot_id} 排产失败，因此不会出现在semi-auto页面的排产调度结果表中")
                    print(f"💡 失败的批次应该在failed-lots页面中查看")
                elif done_count > 0:
                    print(f"✅ 批次 {lot_id} 已成功排产，应该出现在semi-auto页面")
                    print(f"⚠️ 如果semi-auto页面看不到，可能是筛选条件问题")
                elif wait_count > 0:
                    print(f"⏳ 批次 {lot_id} 仍在等待排产")
                else:
                    print(f"❓ 批次 {lot_id} 状态不明，可能已被删除或移动")
                
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = check_lot_status()
    print("🎉 检查: 完成" if success else "❌ 检查: 失败")

if __name__ == "__main__":
    main()