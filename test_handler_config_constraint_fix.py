#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HANDLER_CONFIG约束验证测试 - 确保修复后的排产算法严格执行约束
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_handler_config_fix.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('HandlerConfigTest')

def test_handler_config_constraint():
    """测试HANDLER_CONFIG约束是否严格执行"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入排产服务
            from app.services.real_scheduling_service import RealSchedulingService
            scheduling_service = RealSchedulingService()
            
            logger.info("🎯 开始HANDLER_CONFIG约束验证测试")
            
            # 测试用例1: 不同HANDLER_CONFIG的设备不应匹配
            test_cases = [
                {
                    "name": "低温工序vs常温机台",
                    "lot_requirements": {
                        'DEVICE': 'JWH7030QFNAZ',
                        'STAGE': 'LSTR-125',  # 低温工序
                        'KIT_PN': 'KIT001',
                        'TB_PN': 'TB001',
                        'HB_PN': 'HB001',
                        'HANDLER_CONFIG': 'LSTR_CONFIG'  # 低温配置
                    },
                    "equipment": {
                        'HANDLER_ID': 'SKD01',
                        'HANDLER_CONFIG': 'ROOM_CONFIG',  # 常温配置 - 应该不匹配
                        'DEVICE': 'JWH7030QFNAZ',
                        'STAGE': 'ROOM-TTR',
                        'KIT_PN': 'KIT001',
                        'TB_PN': 'TB001', 
                        'HB_PN': 'HB001',
                        'STATUS': 'IDLE',
                        'TEMPERATURE_RANGE': ''  # SKD机台无温度能力
                    },
                    "expected_match": False,
                    "expected_reason": "HANDLER_CONFIG不匹配"
                },
                {
                    "name": "相同HANDLER_CONFIG应正常匹配",
                    "lot_requirements": {
                        'DEVICE': 'JWH7030QFNAZ',
                        'STAGE': 'ROOM-TTR',
                        'KIT_PN': 'KIT001',
                        'TB_PN': 'TB001',
                        'HB_PN': 'HB001', 
                        'HANDLER_CONFIG': 'ROOM_CONFIG'  # 常温配置
                    },
                    "equipment": {
                        'HANDLER_ID': 'SKD02',
                        'HANDLER_CONFIG': 'ROOM_CONFIG',  # 相同配置 - 应该匹配
                        'DEVICE': 'JWH7030QFNAZ',
                        'STAGE': 'ROOM-TTR',
                        'KIT_PN': 'KIT001',
                        'TB_PN': 'TB001',
                        'HB_PN': 'HB001',
                        'STATUS': 'IDLE'
                    },
                    "expected_match": True,
                    "expected_reason": "同设置匹配或大改机匹配"
                }
            ]
            
            # 执行测试用例
            all_passed = True
            for i, test_case in enumerate(test_cases, 1):
                logger.info(f"\n📋 测试用例 {i}: {test_case['name']}")
                
                # 调用设备匹配评分方法
                score, match_type, changeover_time = scheduling_service.calculate_equipment_match_score_optimized(
                    test_case['lot_requirements'],
                    test_case['equipment'],
                    {}  # preloaded_data
                )
                
                # 验证结果
                actual_match = score > 0
                expected_match = test_case['expected_match']
                
                logger.info(f"   设备: {test_case['equipment']['HANDLER_ID']}")
                logger.info(f"   批次HANDLER_CONFIG: {test_case['lot_requirements']['HANDLER_CONFIG']}")
                logger.info(f"   设备HANDLER_CONFIG: {test_case['equipment']['HANDLER_CONFIG']}")
                logger.info(f"   匹配评分: {score}")
                logger.info(f"   匹配类型: {match_type}")
                logger.info(f"   改机时间: {changeover_time}分钟")
                
                if actual_match == expected_match:
                    logger.info(f"   ✅ 测试通过: {test_case['expected_reason']}")
                else:
                    logger.error(f"   ❌ 测试失败: 期望匹配={expected_match}, 实际匹配={actual_match}")
                    logger.error(f"      期望原因: {test_case['expected_reason']}")
                    all_passed = False
            
            # 额外验证: 检查三级匹配逻辑
            logger.info(f"\n🔍 验证三级匹配逻辑完整性")
            
            # 检查是否还有第四级匹配的痕迹
            import inspect
            source_code = inspect.getsource(scheduling_service.calculate_equipment_match_score_optimized)
            
            if "temperature_aware" in source_code.lower() or "第四级" in source_code:
                logger.error("❌ 发现温度感知匹配或第四级匹配的残留代码")
                all_passed = False
            else:
                logger.info("✅ 确认已移除所有温度感知匹配代码")
                
            # 检查三级匹配是否完整
            required_matches = ["同设置匹配", "小改机匹配", "大改机匹配"]
            for match_name in required_matches:
                if match_name in source_code:
                    logger.info(f"✅ 发现{match_name}逻辑")
                else:
                    logger.warning(f"⚠️ 未找到{match_name}逻辑")
            
            return all_passed
            
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_specific_handler_config_scenarios():
    """测试具体的HANDLER_CONFIG场景"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.real_scheduling_service import RealSchedulingService
            scheduling_service = RealSchedulingService()
            
            logger.info(f"\n🎯 测试具体HANDLER_CONFIG场景")
            
            # 模拟真实数据场景
            real_scenarios = [
                {
                    "description": "LSTR低温工序不能分配给SKD常温机台",
                    "lot": {
                        'DEVICE': 'JWH7030QFNAZ',
                        'STAGE': 'LSTR-125', 
                        'HANDLER_CONFIG': 'LSTR_HANDLER',
                        'KIT_PN': 'KIT_LSTR_001',
                        'TB_PN': 'TB_LSTR_001',
                        'HB_PN': 'HB_LSTR_001'
                    },
                    "equipment_list": [
                        {
                            'HANDLER_ID': 'SKD-ROOM-01',
                            'HANDLER_CONFIG': 'SKD_ROOM_HANDLER',  # 不同配置
                            'DEVICE': 'JWH7030QFNAZ',
                            'STAGE': 'ROOM-TTR',
                            'STATUS': 'IDLE',
                            'TEMPERATURE_RANGE': ''
                        },
                        {
                            'HANDLER_ID': 'LSTR-125-01', 
                            'HANDLER_CONFIG': 'LSTR_HANDLER',  # 相同配置
                            'DEVICE': 'JWH7030QFNAZ',
                            'STAGE': 'LSTR-125',
                            'STATUS': 'IDLE',
                            'TEMPERATURE_RANGE': '-40~125'
                        }
                    ]
                }
            ]
            
            for scenario in real_scenarios:
                logger.info(f"\n📊 场景: {scenario['description']}")
                
                for equipment in scenario['equipment_list']:
                    score, match_type, changeover_time = scheduling_service.calculate_equipment_match_score_optimized(
                        scenario['lot'], equipment, {}
                    )
                    
                    handler_match = scenario['lot']['HANDLER_CONFIG'] == equipment['HANDLER_CONFIG']
                    
                    logger.info(f"   设备: {equipment['HANDLER_ID']}")
                    logger.info(f"   HANDLER_CONFIG匹配: {handler_match}")
                    logger.info(f"   评分: {score} ({match_type})")
                    
                    # 关键验证: 如果HANDLER_CONFIG不匹配，评分应该为0或很低
                    if not handler_match and score > 0:
                        logger.error(f"   ❌ 严重问题: HANDLER_CONFIG不匹配但仍给出评分 {score}")
                        return False
                    elif handler_match and score > 0:
                        logger.info(f"   ✅ 正确: HANDLER_CONFIG匹配且给出合理评分")
                    elif not handler_match and score == 0:
                        logger.info(f"   ✅ 正确: HANDLER_CONFIG不匹配被正确拒绝")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 具体场景测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """测试入口函数"""
    logger.info("🚀 开始HANDLER_CONFIG约束验证测试")
    
    # 基础约束测试
    basic_test_passed = test_handler_config_constraint()
    
    # 具体场景测试  
    scenario_test_passed = test_specific_handler_config_scenarios()
    
    # 综合结果
    overall_success = basic_test_passed and scenario_test_passed
    
    logger.info(f"\n📋 测试结果汇总:")
    logger.info(f"   基础约束测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    logger.info(f"   场景测试: {'✅ 通过' if scenario_test_passed else '❌ 失败'}")
    logger.info(f"   总体结果: {'🎉 全部通过' if overall_success else '❌ 存在问题'}")
    
    if overall_success:
        logger.info(f"✅ HANDLER_CONFIG约束已得到严格执行，温度感知匹配问题已彻底修复")
    else:
        logger.error(f"❌ 仍存在HANDLER_CONFIG约束执行问题，需要进一步修复")
        
    print("🎉 测试完成" if overall_success else "❌ 测试失败")

if __name__ == "__main__":
    main()