// 🚀 完整的前端缓存优化脚本
// 用于失败批次页面 (failed_lots.html) 的JavaScript优化

// 缓存管理系统
const FailedLotsCache = {
    cache: new Map(),
    defaultTimeout: 3 * 60 * 1000, // 3分钟缓存
    
    // 获取缓存
    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }
        
        console.log(`✅ 缓存命中: ${key}`);
        return item.data;
    },
    
    // 设置缓存
    set(key, data, timeout = this.defaultTimeout) {
        this.cache.set(key, {
            data: data,
            expiry: Date.now() + timeout,
            timestamp: Date.now()
        });
        console.log(`💾 缓存已设置: ${key}`);
    },
    
    // 清除缓存
    clear() {
        this.cache.clear();
        console.log('🧹 本地缓存已清理');
    },
    
    // 获取缓存状态
    getStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
};

// 🚀 优化的数据加载函数
async function loadFailedLotsOptimized() {
    const startTime = Date.now();
    
    try {
        // 获取当前筛选参数
        const currentOnly = document.getElementById('currentOnlyToggle')?.checked || false;
        const hours = document.getElementById('hoursFilter')?.value || '24';
        
        // 缓存键
        const cacheKey = `failed_lots_${currentOnly}_${hours}`;
        console.log(`🔍 请求失败批次数据 (current_only=${currentOnly}, hours=${hours})`);
        
        // 检查本地缓存
        const cachedData = FailedLotsCache.get(cacheKey);
        if (cachedData) {
            processFailedLotsData(cachedData);
            console.log(`⚡ 本地缓存响应时间: ${Date.now() - startTime}ms`);
            return;
        }
        
        // 显示加载状态
        showLoading();
        
        // 🚀 优先使用缓存版本的API
        const apiUrl = `/api/v2/production/get-failed-lots-cached?current_only=${currentOnly}&hours=${hours}`;
        console.log(`🌐 请求API: ${apiUrl}`);
        
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log(`📊 API响应 (${Date.now() - startTime}ms):`, {
            success: result.success,
            count: result.data?.failed_lots?.length || 0,
            source: result.debug_info?.data_source
        });
        
        if (result.success) {
            // 更新本地缓存
            FailedLotsCache.set(cacheKey, result);
            
            // 处理数据
            processFailedLotsData(result);
            
            const totalTime = Date.now() - startTime;
            console.log(`✅ 数据加载完成 (${totalTime}ms): ${result.data.failed_lots.length} 条记录`);
            
            // 显示性能信息
            if (result.debug_info?.data_source === 'database_cached') {
                showSuccessMessage(`已加载 ${result.data.failed_lots.length} 条失败批次记录 (缓存优化，${totalTime}ms)`);
            }
            
        } else {
            throw new Error(result.error || result.message || '获取失败批次数据失败');
        }
        
    } catch (error) {
        console.error('❌ 加载失败批次数据出错:', error);
        
        // 备用方案：使用原始API
        try {
            console.log('🔄 尝试备用API...');
            await loadFailedLotsOriginal();
        } catch (fallbackError) {
            console.error('❌ 备用API也失败:', fallbackError);
            showErrorMessage(`数据加载失败: ${error.message}`);
        }
    } finally {
        hideLoading();
    }
}

// 备用的原始API加载函数
async function loadFailedLotsOriginal() {
    const currentOnly = document.getElementById('currentOnlyToggle')?.checked || false;
    const hours = document.getElementById('hoursFilter')?.value || '24';
    
    const url = `/api/v2/production/get-failed-lots-from-logs?current_only=${currentOnly}&hours=${hours}`;
    console.log(`🔄 备用API请求: ${url}`);
    
    const response = await fetch(url);
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    if (result.success) {
        processFailedLotsData(result);
        console.log(`✅ 备用API加载成功: ${result.data.failed_lots.length} 条记录`);
    } else {
        throw new Error(result.message || '备用API获取失败');
    }
}

// 🚀 处理失败批次数据
function processFailedLotsData(result) {
    try {
        // 全局变量更新
        window.allFailedLots = result.data.failed_lots || [];
        
        // 更新统计信息
        if (result.data.summary) {
            updateStatsDisplay(result.data.summary);
        }
        
        // 更新数据源信息
        if (result.debug_info) {
            updateDataSourceInfo(result.debug_info);
        }
        
        // 生成筛选选项
        updateFilterOptionsFromData(window.allFailedLots);
        
        // 应用筛选和渲染
        filterAndRenderLots();
        
        console.log(`📋 数据处理完成: ${window.allFailedLots.length} 条记录`);
        
    } catch (error) {
        console.error('❌ 处理数据时出错:', error);
        showErrorMessage(`数据处理失败: ${error.message}`);
    }
}

// 统计信息更新
function updateStatsDisplay(summary) {
    try {
        const statsElements = {
            totalFailed: document.getElementById('totalFailed'),
            configMissing: document.getElementById('configMissing'),
            equipmentIssues: document.getElementById('equipmentIssues'),
            otherReasons: document.getElementById('otherReasons')
        };
        
        if (statsElements.totalFailed) {
            statsElements.totalFailed.textContent = summary.total_failed || 0;
        }
        if (statsElements.configMissing) {
            statsElements.configMissing.textContent = summary.config_missing || 0;
        }
        if (statsElements.equipmentIssues) {
            statsElements.equipmentIssues.textContent = summary.equipment_incompatible || 0;
        }
        if (statsElements.otherReasons) {
            statsElements.otherReasons.textContent = summary.other_reasons || 0;
        }
        
        console.log('📊 统计信息已更新:', summary);
        
    } catch (error) {
        console.warn('⚠️ 更新统计信息失败:', error);
    }
}

// 数据源信息更新
function updateDataSourceInfo(debugInfo) {
    try {
        const dataSourceElement = document.getElementById('dataSourceInfo');
        if (dataSourceElement) {
            const sourceText = debugInfo.data_source === 'database_cached' ? 
                '数据库 (缓存优化)' : 
                debugInfo.data_source === 'database' ? '数据库' : '日志文件';
                
            dataSourceElement.innerHTML = `
                <small class="text-muted">
                    <i class="fas fa-database"></i> 数据源: ${sourceText} | 
                    <i class="fas fa-clock"></i> ${new Date().toLocaleTimeString()} |
                    <i class="fas fa-list-ol"></i> 记录数: ${debugInfo.total_records || 0}
                </small>
            `;
        }
        
        console.log('🔍 数据源信息已更新:', debugInfo);
        
    } catch (error) {
        console.warn('⚠️ 更新数据源信息失败:', error);
    }
}

// 🚀 缓存失效和刷新
async function invalidateCacheAndRefresh() {
    try {
        console.log('🧹 开始清理缓存和刷新数据...');
        
        // 清理本地缓存
        FailedLotsCache.clear();
        
        // 调用服务端缓存清理
        try {
            const response = await fetch('/api/v2/production/invalidate-failed-lots-cache', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log('🧹 服务端缓存清理结果:', result);
                if (result.success) {
                    showSuccessMessage(`缓存已清理，清理了 ${result.cleared_keys || 0} 个缓存键`);
                }
            }
        } catch (cacheError) {
            console.warn('⚠️ 服务端缓存清理失败:', cacheError);
        }
        
        // 重新加载数据
        await loadFailedLotsOptimized();
        
    } catch (error) {
        console.error('❌ 缓存清理和刷新失败:', error);
        // 即使缓存清理失败，也要重新加载数据
        await loadFailedLotsOptimized();
    }
}

// 🚀 增强的筛选和渲染函数
function filterAndRenderLots() {
    try {
        if (!window.allFailedLots || window.allFailedLots.length === 0) {
            renderNoDataMessage();
            return;
        }
        
        // 获取筛选条件
        const filters = getActiveFilters();
        
        // 应用筛选
        let filteredLots = applyFilters(window.allFailedLots, filters);
        
        // 应用排序
        filteredLots = applySorting(filteredLots);
        
        // 渲染结果
        renderFailedLots(filteredLots);
        
        // 更新筛选结果统计
        updateFilterStats(filteredLots.length, window.allFailedLots.length);
        
        console.log(`🔍 筛选完成: ${filteredLots.length}/${window.allFailedLots.length} 条记录`);
        
    } catch (error) {
        console.error('❌ 筛选和渲染失败:', error);
        showErrorMessage(`数据筛选失败: ${error.message}`);
    }
}

// 获取活动筛选条件
function getActiveFilters() {
    return {
        search: document.getElementById('searchInput')?.value?.trim() || '',
        stage: document.getElementById('stageFilter')?.value || '',
        lotType: document.getElementById('lotTypeFilter')?.value || '',
        failureType: document.getElementById('failureTypeFilter')?.value || ''
    };
}

// 应用筛选
function applyFilters(lots, filters) {
    return lots.filter(lot => {
        // 搜索筛选
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            const searchFields = [
                lot.LOT_ID, lot.DEVICE, lot.STAGE, lot.failure_reason, lot.suggestion
            ].join(' ').toLowerCase();
            
            if (!searchFields.includes(searchTerm)) {
                return false;
            }
        }
        
        // 工序筛选
        if (filters.stage && lot.STAGE !== filters.stage) {
            return false;
        }
        
        // 工单类型筛选
        if (filters.lotType && lot.LOT_TYPE !== filters.lotType) {
            return false;
        }
        
        // 失败类型筛选
        if (filters.failureType) {
            const reason = lot.failure_reason || '';
            if (filters.failureType === '配置' && !reason.includes('配置')) {
                return false;
            }
            if (filters.failureType === '设备' && !reason.includes('设备')) {
                return false;
            }
            if (filters.failureType === '其他' && (reason.includes('配置') || reason.includes('设备'))) {
                return false;
            }
        }
        
        return true;
    });
}

// 应用排序
function applySorting(lots) {
    const sortBy = document.getElementById('sortBy')?.value || 'timestamp';
    const sortOrder = document.getElementById('sortOrder')?.value || 'desc';
    
    return lots.sort((a, b) => {
        let aVal, bVal;
        
        switch (sortBy) {
            case 'timestamp':
                aVal = new Date(a.timestamp || 0);
                bVal = new Date(b.timestamp || 0);
                break;
            case 'lot_id':
                aVal = a.LOT_ID || '';
                bVal = b.LOT_ID || '';
                break;
            case 'device':
                aVal = a.DEVICE || '';
                bVal = b.DEVICE || '';
                break;
            case 'good_qty':
                aVal = parseInt(a.GOOD_QTY) || 0;
                bVal = parseInt(b.GOOD_QTY) || 0;
                break;
            default:
                aVal = a.timestamp || '';
                bVal = b.timestamp || '';
        }
        
        if (sortOrder === 'desc') {
            return bVal > aVal ? 1 : -1;
        } else {
            return aVal > bVal ? 1 : -1;
        }
    });
}

// 工具函数
function showLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'block';
    }
}

function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

function showSuccessMessage(message) {
    console.log('✅', message);
    // 可以在这里添加 toast 提示或其他用户界面反馈
}

function showErrorMessage(message) {
    console.error('❌', message);
    // 可以在这里添加错误提示界面
}

// 🚀 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔥 失败批次页面初始化（完整缓存优化版本）...');
    
    // 初始化事件监听器
    initializeEventListeners();
    
    // 使用缓存优化的加载函数
    loadFailedLotsOptimized();
    
    // 设置定时刷新（可选）
    // setInterval(() => {
    //     console.log('⏰ 定时刷新数据...');
    //     loadFailedLotsOptimized();
    // }, 5 * 60 * 1000); // 5分钟自动刷新
});

// 初始化事件监听器
function initializeEventListeners() {
    // 刷新按钮
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', invalidateCacheAndRefresh);
    }
    
    // 筛选器变更
    const filterElements = [
        'currentOnlyToggle', 'hoursFilter', 'searchInput', 
        'stageFilter', 'lotTypeFilter', 'failureTypeFilter'
    ];
    
    filterElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', () => {
                console.log(`🔍 筛选条件变更: ${id}`);
                if (id === 'currentOnlyToggle' || id === 'hoursFilter') {
                    // 这些条件变更需要重新获取数据
                    loadFailedLotsOptimized();
                } else {
                    // 这些条件只需要重新筛选现有数据
                    filterAndRenderLots();
                }
            });
        }
    });
    
    // 排序控件
    const sortControls = ['sortBy', 'sortOrder'];
    sortControls.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', filterAndRenderLots);
        }
    });
    
    console.log('🎛️ 事件监听器初始化完成');
}

// 导出全局函数供其他脚本使用
window.FailedLotsOptimized = {
    loadData: loadFailedLotsOptimized,
    clearCache: invalidateCacheAndRefresh,
    getCacheStats: () => FailedLotsCache.getStats(),
    processData: processFailedLotsData
};