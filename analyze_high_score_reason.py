#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析YX0125HB0400批次高评分原因
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)-8s | %(message)s')
logger = logging.getLogger('HighScoreAnalysis')

def analyze_high_score_batch():
    """分析YX0125HB0400高评分原因"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app import db
            
            print("\n=== 分析YX0125HB0400高评分原因 ===")
            
            # 1. 查看YX0125HB0400的详细数据
            query = """
            SELECT 
                LOT_ID,
                priority_score,
                comprehensive_score,
                PRIORITY,
                DEVICE,
                STAGE,
                GOOD_QTY,
                PKG_PN,
                CREATE_TIME,
                HANDLER_ID,
                match_type,
                processing_time,
                changeover_time,
                algorithm_version
            FROM lotprioritydone 
            WHERE LOT_ID = 'YX0125HB0400'
            """
            
            result = db.session.execute(db.text(query))
            row = result.fetchone()
            
            if row:
                print(f"🔍 YX0125HB0400批次详细信息:")
                print(f"  批次ID: {row.LOT_ID}")
                print(f"  优先级评分: {row.priority_score}")
                print(f"  综合评分: {row.comprehensive_score}")
                print(f"  产品: {row.DEVICE}")
                print(f"  阶段: {row.STAGE}")
                print(f"  数量: {row.GOOD_QTY}")
                print(f"  封装: {row.PKG_PN}")
                print(f"  创建时间: {row.CREATE_TIME}")
                print(f"  分配设备: {row.HANDLER_ID}")
                print(f"  匹配类型: {row.match_type}")
                print(f"  处理时间: {row.processing_time}小时")
                print(f"  换线时间: {row.changeover_time}分钟")
                print(f"  算法版本: {row.algorithm_version}")
            else:
                print("❌ 未找到YX0125HB0400批次数据")
                return False
            
            # 2. 对比其他批次评分
            print(f"\n=== 对比分析：同设备其他批次评分 ===")
            
            query2 = """
            SELECT 
                LOT_ID,
                priority_score,
                DEVICE,
                STAGE,
                GOOD_QTY,
                PKG_PN,
                CREATE_TIME,
                match_type,
                processing_time,
                changeover_time
            FROM lotprioritydone 
            WHERE HANDLER_ID = 'HCHC-C-012-6800'
            ORDER BY priority_score DESC
            """
            
            result2 = db.session.execute(db.text(query2))
            rows2 = result2.fetchall()
            
            print(f"{'批次ID':<15} {'评分':<8} {'产品':<15} {'阶段':<8} {'数量':<8} {'封装':<15} {'匹配':<10}")
            print("-" * 95)
            
            for r in rows2:
                highlight = "👑" if r.LOT_ID == 'YX0125HB0400' else "  "
                print(f"{highlight}{r.LOT_ID:<15} {r.priority_score:<8} {r.DEVICE:<15} {r.STAGE:<8} "
                      f"{r.GOOD_QTY:<8} {r.PKG_PN or 'N/A':<15} {r.match_type or 'N/A':<10}")
            
            # 3. 分析可能的高评分因素
            print(f"\n=== 高评分因素分析 ===")
            
            target_device = row.DEVICE
            target_stage = row.STAGE
            target_pkg_pn = row.PKG_PN
            target_good_qty = row.GOOD_QTY
            target_create_time = row.CREATE_TIME
            
            factors = []
            
            # 检查是否是特殊阶段
            special_stages = ['BTT', 'BAKING', 'LSTR']
            if any(stage in str(target_stage).upper() for stage in special_stages):
                factors.append(f"🔥 特殊阶段: {target_stage} (通常获得1000+级别高评分)")
            
            # 检查产品优先级
            device_priority_query = """
            SELECT DEVICE, PRIORITY 
            FROM device_priority 
            WHERE DEVICE = :device
            """
            device_result = db.session.execute(db.text(device_priority_query), {'device': target_device})
            device_row = device_result.fetchone()
            
            if device_row:
                if device_row.PRIORITY == 0:
                    factors.append(f"🎯 产品优先级=0: {target_device} (绝对优先权，10000+级别评分)")
                elif device_row.PRIORITY <= 5:
                    factors.append(f"⭐ 高产品优先级: {target_device} (优先级={device_row.PRIORITY})")
            
            # 检查批次优先级
            lot_priority_query = """
            SELECT LOT_ID, PRIORITY 
            FROM lot_priority 
            WHERE LOT_ID = :lot_id
            """
            lot_result = db.session.execute(db.text(lot_priority_query), {'lot_id': row.LOT_ID})
            lot_row = lot_result.fetchone()
            
            if lot_row and lot_row.PRIORITY <= 5:
                factors.append(f"🏆 高批次优先级: {row.LOT_ID} (优先级={lot_row.PRIORITY})")
            
            # 检查数量规模
            if target_good_qty and target_good_qty >= 1000:
                factors.append(f"📦 大批次数量: {target_good_qty} (数量越大，产值效率评分越高)")
            
            # 检查FIFO得分 (早期批次)
            if target_create_time:
                # 比较与其他批次的时间差
                avg_time_query = """
                SELECT AVG(UNIX_TIMESTAMP(CREATE_TIME)) as avg_time,
                       MIN(UNIX_TIMESTAMP(CREATE_TIME)) as min_time,
                       MAX(UNIX_TIMESTAMP(CREATE_TIME)) as max_time
                FROM lotprioritydone 
                WHERE HANDLER_ID = 'HCHC-C-012-6800'
                """
                time_result = db.session.execute(db.text(avg_time_query))
                time_row = time_result.fetchone()
                
                if time_row:
                    target_timestamp = target_create_time.timestamp()
                    if target_timestamp <= time_row.min_time + (time_row.max_time - time_row.min_time) * 0.3:
                        factors.append(f"⏰ 早期批次FIFO: {target_create_time} (早期批次获得FIFO高分)")
            
            # 检查设备匹配度
            match_type = row.match_type
            if match_type == 'same_setup':
                factors.append(f"🎯 完美设备匹配: {match_type} (同设置匹配，技术匹配满分100分)")
            elif match_type == 'small_change':
                factors.append(f"🔧 良好设备匹配: {match_type} (小改机匹配，技术匹配80分)")
            
            # 输出分析结果
            if factors:
                print("可能的高评分因素:")
                for i, factor in enumerate(factors, 1):
                    print(f"  {i}. {factor}")
            else:
                print("❓ 未识别到明显的高评分因素，可能是:")
                print("  1. 复合评分效应：多个中等因素叠加")
                print("  2. 业务规则特殊情况")
                print("  3. 算法权重配置影响")
            
            # 4. 检查是否存在异常
            print(f"\n=== 异常检查 ===")
            
            normal_range = [r.priority_score for r in rows2 if r.LOT_ID != 'YX0125HB0400' and r.priority_score]
            if normal_range:
                avg_score = sum(normal_range) / len(normal_range)
                max_normal_score = max(normal_range)
                
                print(f"其他批次平均评分: {avg_score:.2f}")
                print(f"其他批次最高评分: {max_normal_score:.2f}")
                print(f"YX0125HB0400评分: {row.priority_score:.2f}")
                
                if row.priority_score > max_normal_score * 1.5:
                    print(f"⚠️  异常：评分明显偏高，超过正常范围50%以上")
                elif row.priority_score > max_normal_score * 1.2:
                    print(f"📈 注意：评分显著偏高，超过正常范围20%以上")
                else:
                    print(f"✅ 正常：评分在合理范围内")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    success = analyze_high_score_batch()
    print("🎉 分析完成: 成功" if success else "❌ 分析完成: 失败")

if __name__ == "__main__":
    main()