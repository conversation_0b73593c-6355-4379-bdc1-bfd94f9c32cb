#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建排产失败批次表 (scheduling_failed_lots)
修复排产失败清单功能的数据库表创建脚本

使用标准化测试脚本模板，确保Flask上下文正确管理
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('create_failed_lots_table.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('CreateFailedLotsTable')

def create_scheduling_failed_lots_table():
    """创建排产失败批次表"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 获取数据库连接
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 检查表是否已存在
                check_table_sql = """
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
                """
                cursor.execute(check_table_sql)
                result = cursor.fetchone()
                
                # 处理DictCursor返回的字典格式
                if isinstance(result, dict):
                    table_exists = list(result.values())[0] > 0
                else:
                    table_exists = result[0] > 0
                
                if table_exists:
                    logger.info("🔍 scheduling_failed_lots表已存在，跳过创建")
                    logger.info("📊 检查现有表结构...")
                    
                    # 检查表结构
                    describe_sql = "DESCRIBE scheduling_failed_lots"
                    cursor.execute(describe_sql)
                    columns = cursor.fetchall()
                    
                    logger.info(f"📋 现有表包含 {len(columns)} 个字段:")
                    for col in columns:
                        if isinstance(col, dict):
                            logger.info(f"   - {col['Field']}: {col['Type']} {col['Null']} {col['Key']}")
                        else:
                            logger.info(f"   - {col[0]}: {col[1]} {col[2]} {col[3]}")
                    
                    cursor.close()
                    return True
                
                logger.info("🔧 创建scheduling_failed_lots表...")
                
                # 创建表的SQL语句（基于scheduling_failure_fix.py中的定义）
                create_table_sql = """
                CREATE TABLE scheduling_failed_lots (
                    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                    lot_id VARCHAR(50) NOT NULL COMMENT '批次ID',
                    device VARCHAR(100) NOT NULL COMMENT '产品名称',
                    stage VARCHAR(50) NOT NULL COMMENT '工序',
                    good_qty INT DEFAULT 0 COMMENT '良品数量',
                    failure_reason VARCHAR(500) NOT NULL COMMENT '失败原因',
                    failure_details TEXT COMMENT '失败详情',
                    suggestion TEXT COMMENT '智能建议解决方案',
                    algorithm_version VARCHAR(50) DEFAULT 'v2.0' COMMENT '算法版本',
                    session_id VARCHAR(100) COMMENT '排产会话ID',
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    
                    -- 索引定义
                    INDEX idx_lot_id (lot_id),
                    INDEX idx_device_stage (device, stage),
                    INDEX idx_timestamp (timestamp DESC),
                    INDEX idx_session_id (session_id),
                    INDEX idx_failure_reason (failure_reason(100))
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
                COMMENT='排产失败批次记录表';
                """
                
                cursor.execute(create_table_sql)
                logger.info("✅ scheduling_failed_lots表创建成功")
                
                # 验证表创建结果
                cursor.execute(describe_sql)
                columns = cursor.fetchall()
                
                logger.info(f"📋 新表包含 {len(columns)} 个字段:")
                for col in columns:
                    if isinstance(col, dict):
                        logger.info(f"   - {col['Field']}: {col['Type']} {col['Null']} {col['Key']}")
                    else:
                        logger.info(f"   - {col[0]}: {col[1]} {col[2]} {col[3]}")
                
                # 检查索引创建情况
                show_indexes_sql = "SHOW INDEXES FROM scheduling_failed_lots"
                cursor.execute(show_indexes_sql)
                indexes = cursor.fetchall()
                
                logger.info(f"🔗 表索引信息 (共{len(indexes)}个):")
                for idx in indexes:
                    if isinstance(idx, dict):
                        logger.info(f"   - {idx['Key_name']}: {idx['Column_name']} ({idx['Index_type']})")
                    else:
                        logger.info(f"   - {idx[2]}: {idx[4]} ({idx[10]})")
                
                cursor.close()
                logger.info("🎉 数据库表创建完成!")
                return True
                
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_table_operations():
    """测试表基本操作"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("🧪 测试表基本操作...")
                
                # 测试插入
                test_insert_sql = """
                INSERT INTO scheduling_failed_lots 
                (lot_id, device, stage, good_qty, failure_reason, failure_details, suggestion) 
                VALUES 
                (%s, %s, %s, %s, %s, %s, %s)
                """
                
                test_data = (
                    'TEST001', 
                    'TestDevice', 
                    'FT', 
                    1000, 
                    '测试失败原因', 
                    '这是测试失败详情', 
                    '建议检查测试配置'
                )
                
                cursor.execute(test_insert_sql, test_data)
                insert_id = cursor.lastrowid
                logger.info(f"✅ 测试插入成功，ID: {insert_id}")
                
                # 测试查询
                test_select_sql = "SELECT * FROM scheduling_failed_lots WHERE id = %s"
                cursor.execute(test_select_sql, (insert_id,))
                result = cursor.fetchone()
                
                if result:
                    logger.info("✅ 测试查询成功")
                    if isinstance(result, dict):
                        logger.info(f"   批次ID: {result['lot_id']}, 设备: {result['device']}")
                    else:
                        logger.info(f"   批次ID: {result[1]}, 设备: {result[2]}")
                else:
                    logger.error("❌ 测试查询失败")
                
                # 测试删除（清理测试数据）
                test_delete_sql = "DELETE FROM scheduling_failed_lots WHERE id = %s"
                cursor.execute(test_delete_sql, (insert_id,))
                logger.info("✅ 测试数据清理完成")
                
                cursor.close()
                return True
                
    except Exception as e:
        logger.error(f"❌ 表操作测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("🚀 开始创建排产失败批次表...")
    
    success = create_scheduling_failed_lots_table()
    if not success:
        logger.error("❌ 表创建失败")
        return False
    
    logger.info("🧪 开始测试表操作...")
    test_success = test_table_operations()
    if not test_success:
        logger.error("❌ 表操作测试失败")
        return False
    
    logger.info("🎉 所有操作完成!")
    return True

if __name__ == "__main__":
    success = main()
    print("🎉 表创建: 成功" if success else "❌ 表创建: 失败")