#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EQP_CLASS匹配要求删除效果测试 - 对比修改前后的排产结果
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
from collections import defaultdict
import json

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('eqp_class_removal_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('EQPClassRemovalTest')

def test_eqp_class_removal_impact():
    """测试删除EQP_CLASS匹配要求的影响"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入相关服务
            from app.services.data_source_manager import DataSourceManager
            from app.services.real_scheduling_service import RealSchedulingService
            
            data_manager = DataSourceManager()
            scheduling_service = RealSchedulingService()
            
            print("="*80)
            print("🧪 EQP_CLASS匹配要求删除效果测试")
            print("="*80)
            
            # 获取数据
            equipment_result = data_manager.get_table_data('EQP_STATUS')
            all_equipment = equipment_result.get('data', [])
            
            wait_lots_result = data_manager.get_table_data('et_wait_lot')
            wait_lots = wait_lots_result.get('data', [])
            
            # 获取当前已排产数据作为基线
            scheduled_result = data_manager.get_table_data('lotprioritydone')
            current_scheduled = scheduled_result.get('data', [])
            current_used_equipment = set(lot.get('HANDLER_ID', '').strip() for lot in current_scheduled if lot.get('HANDLER_ID'))
            
            print(f"📊 测试基线数据:")
            print(f"   设备总数: {len(all_equipment)} 台")
            print(f"   待排产批次: {len(wait_lots)} 个")
            print(f"   当前已使用设备: {len(current_used_equipment)} 台")
            print(f"   当前设备利用率: {len(current_used_equipment)/len(all_equipment)*100:.1f}%")
            
            # 预加载数据
            print(f"\n🔄 准备测试数据...")
            preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
            if not preloaded_data:
                print("❌ 无法获取预加载数据")
                return False
            
            # ==================== 测试修改后的匹配效果 ====================
            print(f"\n🧪 测试修改后的设备匹配能力...")
            
            # 选择一些原本未被使用的设备进行测试
            unused_equipment = []
            for eqp in all_equipment:
                handler_id = eqp.get('HANDLER_ID', '').strip()
                if handler_id not in current_used_equipment:
                    unused_equipment.append(eqp)
            
            print(f"   选择 {len(unused_equipment)} 台未使用设备进行测试")
            
            # 测试结果统计
            test_results = {
                'equipment_tested': 0,
                'equipment_with_matches': 0,
                'total_new_matches': 0,
                'match_details': [],
                'equipment_utilization_improvement': 0
            }
            
            # 逐台测试未使用的设备
            for i, eqp in enumerate(unused_equipment[:20], 1):  # 测试前20台
                handler_id = eqp.get('HANDLER_ID', '').strip()
                handler_config = eqp.get('HANDLER_CONFIG', '').strip()
                device = eqp.get('DEVICE', '').strip()
                stage = eqp.get('STAGE', '').strip()
                status = eqp.get('STATUS', '').strip()
                
                test_results['equipment_tested'] += 1
                
                print(f"   测试设备 {i}: {handler_id}")
                print(f"     配置: CONFIG={handler_config}, DEVICE={device or '无'}, STAGE={stage or '无'}")
                
                # 测试与待排产批次的匹配
                equipment_matches = []
                
                for lot in wait_lots[:50]:  # 测试前50个批次
                    lot_id = lot.get('LOT_ID', '')
                    
                    try:
                        # 获取批次配置需求
                        lot_requirements = scheduling_service.get_lot_configuration_requirements_optimized(lot, preloaded_data)
                        if not lot_requirements:
                            continue
                        
                        # 测试设备匹配评分
                        match_score, match_type, changeover_time = scheduling_service.calculate_equipment_match_score_optimized(
                            lot_requirements, eqp, preloaded_data)
                        
                        if match_score > 0:
                            equipment_matches.append({
                                'lot_id': lot_id,
                                'device': lot.get('DEVICE', ''),
                                'stage': lot.get('STAGE', ''),
                                'score': match_score,
                                'type': match_type,
                                'changeover_time': changeover_time
                            })
                            
                    except Exception as e:
                        continue
                
                if equipment_matches:
                    test_results['equipment_with_matches'] += 1
                    test_results['total_new_matches'] += len(equipment_matches)
                    
                    # 按评分排序，取前3个最佳匹配
                    best_matches = sorted(equipment_matches, key=lambda x: x['score'], reverse=True)[:3]
                    
                    print(f"     ✅ 找到 {len(equipment_matches)} 个匹配批次，最佳匹配:")
                    for match in best_matches:
                        print(f"       - {match['lot_id']}: {match['device']}-{match['stage']} ({match['type']}, {match['score']}分, {match['changeover_time']}min)")
                    
                    test_results['match_details'].append({
                        'handler_id': handler_id,
                        'matches_count': len(equipment_matches),
                        'best_matches': best_matches,
                        'config': handler_config
                    })
                else:
                    print(f"     ❌ 无匹配批次")
            
            # ==================== 分析测试结果 ====================
            print(f"\n📊 测试结果分析:")
            
            tested_count = test_results['equipment_tested']
            matched_count = test_results['equipment_with_matches']
            total_matches = test_results['total_new_matches']
            
            print(f"   测试设备数量: {tested_count} 台")
            print(f"   找到匹配的设备: {matched_count} 台")
            print(f"   设备激活率: {matched_count/tested_count*100:.1f}%")
            print(f"   新增匹配批次总数: {total_matches} 个")
            print(f"   平均每台设备匹配批次: {total_matches/matched_count:.1f} 个" if matched_count > 0 else "   平均每台设备匹配批次: 0 个")
            
            # 计算理论设备利用率提升
            potential_new_equipment = len(unused_equipment) * (matched_count / tested_count) if tested_count > 0 else 0
            new_utilization = (len(current_used_equipment) + potential_new_equipment) / len(all_equipment) * 100
            improvement = new_utilization - (len(current_used_equipment)/len(all_equipment)*100)
            
            print(f"\n🚀 设备利用率提升预估:")
            print(f"   当前利用率: {len(current_used_equipment)/len(all_equipment)*100:.1f}%")
            print(f"   预估新利用率: {new_utilization:.1f}%")
            print(f"   提升幅度: +{improvement:.1f}%")
            
            # ==================== 匹配质量分析 ====================
            print(f"\n🔍 匹配质量分析:")
            
            match_type_stats = defaultdict(int)
            changeover_time_stats = []
            
            for detail in test_results['match_details']:
                for match in detail['best_matches']:
                    match_type_stats[match['type']] += 1
                    changeover_time_stats.append(match['changeover_time'])
            
            print(f"   匹配类型分布:")
            for match_type, count in match_type_stats.items():
                print(f"     - {match_type}: {count} 次")
            
            if changeover_time_stats:
                avg_changeover = sum(changeover_time_stats) / len(changeover_time_stats)
                print(f"   平均改机时间: {avg_changeover:.1f} 分钟")
                print(f"   改机时间范围: {min(changeover_time_stats)} - {max(changeover_time_stats)} 分钟")
            
            # ==================== 风险评估 ====================
            print(f"\n⚠️ 风险评估:")
            
            # 检查是否有明显的工艺类型错配
            risky_matches = []
            for detail in test_results['match_details']:
                handler_id = detail['handler_id']
                config = detail['config']
                
                # 简单启发式：如果设备配置包含特定关键字，检查匹配的批次类型
                for match in detail['best_matches']:
                    lot_device = match['device']
                    lot_stage = match['stage']
                    
                    # 检查潜在的工艺不匹配（这里只是示例逻辑）
                    if ('PnP' in config or 'C6800' in config) and 'Turret' in lot_stage:
                        risky_matches.append({
                            'equipment': handler_id,
                            'equipment_config': config,
                            'lot_device': lot_device,
                            'lot_stage': lot_stage,
                            'risk_reason': 'PnP设备匹配Turret工艺'
                        })
            
            if risky_matches:
                print(f"   发现 {len(risky_matches)} 个潜在风险匹配:")
                for risk in risky_matches[:5]:  # 只显示前5个
                    print(f"     - {risk['equipment']}({risk['equipment_config']}) → {risk['lot_device']}-{risk['lot_stage']}")
                    print(f"       风险: {risk['risk_reason']}")
            else:
                print(f"   ✅ 未发现明显的工艺类型错配风险")
            
            # ==================== 总结和建议 ====================
            print(f"\n📋 测试总结:")
            
            if improvement > 20:
                print(f"   🎉 删除EQP_CLASS匹配要求效果显著！")
                print(f"   ✅ 设备利用率预计提升 {improvement:.1f}%，大幅改善资源使用效率")
            elif improvement > 10:
                print(f"   👍 删除EQP_CLASS匹配要求有明显效果")
                print(f"   ✅ 设备利用率预计提升 {improvement:.1f}%，改善明显")
            elif improvement > 0:
                print(f"   👌 删除EQP_CLASS匹配要求有一定效果")
                print(f"   ✅ 设备利用率预计提升 {improvement:.1f}%")
            else:
                print(f"   ⚠️ 删除EQP_CLASS匹配要求效果不明显")
            
            if len(risky_matches) > 0:
                print(f"   ⚠️ 需要关注 {len(risky_matches)} 个潜在风险匹配")
                print(f"   💡 建议：在生产环境应用前，建立更精细的设备能力验证机制")
            else:
                print(f"   ✅ 风险评估良好，可以考虑在生产环境应用")
            
            # 保存详细测试结果
            result_file = f'eqp_class_removal_test_result_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(test_results, f, ensure_ascii=False, indent=2)
            
            print(f"\n📄 详细测试结果已保存到: {result_file}")
            print("="*80)
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = test_eqp_class_removal_impact()
    if success:
        print("\n🎉 EQP_CLASS匹配要求删除效果测试完成！")
    else:
        print("\n❌ 测试失败！")

if __name__ == "__main__":
    main()