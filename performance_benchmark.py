#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能基准测试系统
建立系统性能基准线和比较分析
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import time
import json
import logging
import statistics
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self):
        self.logger = logging.getLogger('PerformanceBenchmark')
        self.baseline_results = {}
        
    def benchmark_api_performance(self, iterations=100):
        """API性能基准测试"""
        self.logger.info(f"🚀 开始API性能基准测试 ({iterations}次迭代)")
        
        response_times = []
        success_count = 0
        error_count = 0
        
        try:
            from app import create_app
            app, socketio = create_app()
            
            with app.test_client() as client:
                for i in range(iterations):
                    start_time = time.time()
                    
                    try:
                        response = client.get('/')
                        response_time = (time.time() - start_time) * 1000
                        response_times.append(response_time)
                        
                        if response.status_code == 200:
                            success_count += 1
                        else:
                            error_count += 1
                            
                    except Exception as e:
                        error_count += 1
                        self.logger.debug(f"API请求失败: {e}")
                    
                    if (i + 1) % 20 == 0:
                        self.logger.info(f"  完成 {i + 1}/{iterations} 次请求")
        
        except Exception as e:
            self.logger.error(f"API基准测试失败: {e}")
            return {}
        
        if not response_times:
            return {'error': 'No successful API calls'}
        
        # 计算统计指标
        benchmark_result = {
            'test_type': 'api_performance',
            'iterations': iterations,
            'success_count': success_count,
            'error_count': error_count,
            'success_rate': (success_count / iterations) * 100,
            'response_time_ms': {
                'mean': statistics.mean(response_times),
                'median': statistics.median(response_times),
                'min': min(response_times),
                'max': max(response_times),
                'std_dev': statistics.stdev(response_times) if len(response_times) > 1 else 0,
                'p95': sorted(response_times)[int(len(response_times) * 0.95)] if len(response_times) > 20 else max(response_times),
                'p99': sorted(response_times)[int(len(response_times) * 0.99)] if len(response_times) > 100 else max(response_times)
            }
        }
        
        self.logger.info(f"✅ API基准测试完成")
        self.logger.info(f"  平均响应时间: {benchmark_result['response_time_ms']['mean']:.2f}ms")
        self.logger.info(f"  成功率: {benchmark_result['success_rate']:.1f}%")
        
        return benchmark_result
    
    def benchmark_database_performance(self, iterations=50):
        """数据库性能基准测试"""
        self.logger.info(f"🗄️ 开始数据库性能基准测试 ({iterations}次迭代)")
        
        query_times = []
        success_count = 0
        error_count = 0
        
        try:
            from app import create_app
            app, socketio = create_app()
            
            with app.app_context():
                from app import db
                from app.models import User, EQP_STATUS, ET_WAIT_LOT
                
                for i in range(iterations):
                    start_time = time.time()
                    
                    try:
                        # 执行几个典型的数据库查询
                        user_count = User.query.count()
                        equipment_count = EQP_STATUS.query.count()
                        lot_count = ET_WAIT_LOT.query.limit(10).count()
                        
                        query_time = (time.time() - start_time) * 1000
                        query_times.append(query_time)
                        success_count += 1
                        
                    except Exception as e:
                        error_count += 1
                        self.logger.debug(f"数据库查询失败: {e}")
                    
                    if (i + 1) % 10 == 0:
                        self.logger.info(f"  完成 {i + 1}/{iterations} 次查询")
        
        except Exception as e:
            self.logger.error(f"数据库基准测试失败: {e}")
            return {}
        
        if not query_times:
            return {'error': 'No successful database queries'}
        
        # 计算统计指标
        benchmark_result = {
            'test_type': 'database_performance',
            'iterations': iterations,
            'success_count': success_count,
            'error_count': error_count,
            'success_rate': (success_count / iterations) * 100,
            'query_time_ms': {
                'mean': statistics.mean(query_times),
                'median': statistics.median(query_times),
                'min': min(query_times),
                'max': max(query_times),
                'std_dev': statistics.stdev(query_times) if len(query_times) > 1 else 0,
                'p95': sorted(query_times)[int(len(query_times) * 0.95)] if len(query_times) > 20 else max(query_times)
            }
        }
        
        self.logger.info(f"✅ 数据库基准测试完成")
        self.logger.info(f"  平均查询时间: {benchmark_result['query_time_ms']['mean']:.2f}ms")
        self.logger.info(f"  成功率: {benchmark_result['success_rate']:.1f}%")
        
        return benchmark_result
    
    def benchmark_scheduling_algorithm(self, iterations=10):
        """排产算法性能基准测试"""
        self.logger.info(f"⚡ 开始排产算法基准测试 ({iterations}次迭代)")
        
        execution_times = []
        success_count = 0
        error_count = 0
        
        try:
            from app import create_app
            app, socketio = create_app()
            
            with app.app_context():
                from app.services.real_scheduling_service import RealSchedulingService
                
                for i in range(iterations):
                    start_time = time.time()
                    
                    try:
                        # 创建排产服务并执行数据预加载
                        scheduling_service = RealSchedulingService()
                        preloaded_data = scheduling_service._preload_all_data_with_deterministic_cache()
                        
                        execution_time = (time.time() - start_time) * 1000
                        execution_times.append(execution_time)
                        success_count += 1
                        
                    except Exception as e:
                        error_count += 1
                        self.logger.debug(f"排产算法测试失败: {e}")
                    
                    if (i + 1) % 2 == 0:
                        self.logger.info(f"  完成 {i + 1}/{iterations} 次算法执行")
        
        except Exception as e:
            self.logger.error(f"排产算法基准测试失败: {e}")
            return {}
        
        if not execution_times:
            return {'error': 'No successful scheduling algorithm executions'}
        
        # 计算统计指标
        benchmark_result = {
            'test_type': 'scheduling_algorithm',
            'iterations': iterations,
            'success_count': success_count,
            'error_count': error_count,
            'success_rate': (success_count / iterations) * 100,
            'execution_time_ms': {
                'mean': statistics.mean(execution_times),
                'median': statistics.median(execution_times),
                'min': min(execution_times),
                'max': max(execution_times),
                'std_dev': statistics.stdev(execution_times) if len(execution_times) > 1 else 0
            }
        }
        
        self.logger.info(f"✅ 排产算法基准测试完成")
        self.logger.info(f"  平均执行时间: {benchmark_result['execution_time_ms']['mean']:.2f}ms")
        self.logger.info(f"  成功率: {benchmark_result['success_rate']:.1f}%")
        
        return benchmark_result
    
    def run_comprehensive_benchmark(self):
        """运行综合性能基准测试"""
        self.logger.info("🏃 开始综合性能基准测试")
        
        benchmark_results = {
            'timestamp': datetime.now().isoformat(),
            'test_duration': 0,
            'benchmarks': {}
        }
        
        start_time = time.time()
        
        # 执行各项基准测试
        tests = [
            ('api_performance', lambda: self.benchmark_api_performance(100)),
            ('database_performance', lambda: self.benchmark_database_performance(50)),
            ('scheduling_algorithm', lambda: self.benchmark_scheduling_algorithm(10))
        ]
        
        for test_name, test_func in tests:
            self.logger.info(f"📊 执行 {test_name} 基准测试")
            try:
                result = test_func()
                benchmark_results['benchmarks'][test_name] = result
            except Exception as e:
                self.logger.error(f"{test_name} 基准测试失败: {e}")
                benchmark_results['benchmarks'][test_name] = {'error': str(e)}
        
        # 计算总测试时长
        benchmark_results['test_duration'] = time.time() - start_time
        
        # 生成综合评分
        benchmark_results['performance_score'] = self._calculate_performance_score(benchmark_results['benchmarks'])
        
        self.logger.info(f"✅ 综合基准测试完成，耗时 {benchmark_results['test_duration']:.2f} 秒")
        self.logger.info(f"📊 综合性能评分: {benchmark_results['performance_score']:.1f}/100")
        
        return benchmark_results
    
    def _calculate_performance_score(self, benchmarks):
        """计算综合性能评分 (0-100)"""
        total_score = 0
        weight_sum = 0
        
        # API性能评分 (权重: 40%)
        api_benchmark = benchmarks.get('api_performance', {})
        if 'response_time_ms' in api_benchmark:
            avg_response_time = api_benchmark['response_time_ms'].get('mean', 0)
            api_score = max(0, 100 - (avg_response_time / 20))  # 每20ms扣1分
            total_score += api_score * 0.4
            weight_sum += 0.4
        
        # 数据库性能评分 (权重: 30%)
        db_benchmark = benchmarks.get('database_performance', {})
        if 'query_time_ms' in db_benchmark:
            avg_query_time = db_benchmark['query_time_ms'].get('mean', 0)
            db_score = max(0, 100 - (avg_query_time / 10))  # 每10ms扣1分
            total_score += db_score * 0.3
            weight_sum += 0.3
        
        # 排产算法性能评分 (权重: 30%)
        scheduling_benchmark = benchmarks.get('scheduling_algorithm', {})
        if 'execution_time_ms' in scheduling_benchmark:
            avg_execution_time = scheduling_benchmark['execution_time_ms'].get('mean', 0)
            scheduling_score = max(0, 100 - (avg_execution_time / 100))  # 每100ms扣1分
            total_score += scheduling_score * 0.3
            weight_sum += 0.3
        
        return total_score / weight_sum if weight_sum > 0 else 0

def run_performance_benchmark():
    """运行性能基准测试"""
    benchmark = PerformanceBenchmark()
    return benchmark.run_comprehensive_benchmark()

if __name__ == "__main__":
    print("🏃 启动性能基准测试...")
    
    result = run_performance_benchmark()
    
    print("✅ 基准测试完成")
    
    # 保存基准测试报告
    report_file = f"performance_benchmark_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"📊 基准测试报告已保存: {report_file}")
