#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
run.py应用连接专项监控器 - 专门监控PID 39168的MySQL连接情况
实时分析连接增长趋势和回收模式
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
import time
import json
import pymysql
import psutil
import subprocess
from datetime import datetime
from collections import defaultdict, deque

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('monitor_run_py_connections.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('RunPyConnectionMonitor')

class RunPyConnectionMonitor:
    def __init__(self, target_pid=39168):
        self.target_pid = target_pid
        self.check_interval = 30  # 30秒检查一次
        self.connection_history = deque(maxlen=100)  # 保留最近100次记录
        self._mysql_connection = None
        
    def _get_mysql_connection(self):
        """获取持久MySQL连接"""
        if self._mysql_connection is None:
            try:
                self._mysql_connection = pymysql.connect(
                    host='localhost',
                    port=3306,
                    user='root',
                    password='WWWwww123!',
                    database='aps',
                    charset='utf8mb4'
                )
            except Exception as e:
                logger.error(f"MySQL连接失败: {e}")
                return None
        return self._mysql_connection
    
    def get_run_py_process_info(self):
        """获取run.py进程详细信息"""
        try:
            process = psutil.Process(self.target_pid)
            return {
                'pid': self.target_pid,
                'name': process.name(),
                'memory_mb': process.memory_info().rss / 1024 / 1024,
                'cpu_percent': process.cpu_percent(),
                'create_time': datetime.fromtimestamp(process.create_time()).strftime('%Y-%m-%d %H:%M:%S'),
                'status': process.status(),
                'num_threads': process.num_threads()
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            logger.warning(f"无法获取PID {self.target_pid}信息: {e}")
            return None
    
    def get_mysql_connections_for_process(self):
        """获取指定进程的MySQL连接信息"""
        try:
            conn = self._get_mysql_connection()
            if not conn:
                return {}
                
            with conn.cursor() as cursor:
                # 获取总连接数
                cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                total_connected = int(cursor.fetchone()[1])
                
                # 获取进程列表
                cursor.execute("SHOW PROCESSLIST")
                processes = cursor.fetchall()
                
                # 分析连接
                total_connections = 0
                long_connections = []
                state_distribution = defaultdict(int)
                time_distribution = defaultdict(int)
                
                for proc in processes:
                    proc_id, user, host, db, command, time_sec, state = proc[:7]
                    
                    if user == 'root' and db == 'aps':
                        total_connections += 1
                        state_distribution[state or 'None'] += 1
                        
                        # 时间分布
                        if time_sec < 60:
                            time_distribution['<60s'] += 1
                        elif time_sec < 300:
                            time_distribution['60-300s'] += 1
                        elif time_sec < 900:
                            time_distribution['300-900s'] += 1
                        else:
                            time_distribution['>900s'] += 1
                            
                        # 长连接详情
                        if time_sec > 60:
                            long_connections.append({
                                'id': proc_id,
                                'time_sec': time_sec,
                                'state': state,
                                'command': command
                            })
                
                return {
                    'total_mysql_connections': total_connected,
                    'aps_connections': total_connections,
                    'long_connections': long_connections,
                    'state_distribution': dict(state_distribution),
                    'time_distribution': dict(time_distribution)
                }
                
        except Exception as e:
            logger.error(f"获取MySQL连接信息失败: {e}")
            return {}
    
    def get_netstat_connections(self):
        """通过netstat获取进程网络连接"""
        try:
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            process_connections = 0
            for line in lines:
                if ':3306' in line and 'ESTABLISHED' in line:
                    parts = line.split()
                    if len(parts) >= 5 and parts[4] == str(self.target_pid):
                        process_connections += 1
            
            return process_connections
            
        except Exception as e:
            logger.warning(f"Netstat分析失败: {e}")
            return 0
    
    def analyze_connection_trend(self):
        """分析连接增长趋势"""
        if len(self.connection_history) < 2:
            return {'trend': 'insufficient_data'}
        
        recent_data = list(self.connection_history)[-10:]  # 最近10次记录
        
        connections = [data['mysql_data']['aps_connections'] for data in recent_data]
        memory_usage = [data['process_info']['memory_mb'] for data in recent_data if data['process_info']]
        
        # 连接数趋势
        connection_trend = 'stable'
        if len(connections) >= 3:
            recent_avg = sum(connections[-3:]) / 3
            earlier_avg = sum(connections[:-3]) / max(1, len(connections) - 3)
            
            if recent_avg > earlier_avg * 1.2:
                connection_trend = 'increasing'
            elif recent_avg < earlier_avg * 0.8:
                connection_trend = 'decreasing'
        
        # 内存趋势
        memory_trend = 'stable'
        if len(memory_usage) >= 3:
            recent_mem = sum(memory_usage[-3:]) / 3
            earlier_mem = sum(memory_usage[:-3]) / max(1, len(memory_usage) - 3)
            
            if recent_mem > earlier_mem * 1.1:
                memory_trend = 'increasing'
            elif recent_mem < earlier_mem * 0.9:
                memory_trend = 'decreasing'
        
        return {
            'trend': connection_trend,
            'memory_trend': memory_trend,
            'current_connections': connections[-1] if connections else 0,
            'avg_connections_last_5min': sum(connections[-10:]) / len(connections[-10:]) if connections else 0,
            'connection_variance': max(connections[-5:]) - min(connections[-5:]) if len(connections) >= 5 else 0
        }
    
    def generate_focused_report(self):
        """生成专项监控报告"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 收集数据
        process_info = self.get_run_py_process_info()
        mysql_data = self.get_mysql_connections_for_process()
        netstat_connections = self.get_netstat_connections()
        
        # 记录历史
        current_record = {
            'timestamp': timestamp,
            'process_info': process_info,
            'mysql_data': mysql_data,
            'netstat_connections': netstat_connections
        }
        self.connection_history.append(current_record)
        
        # 趋势分析
        trend_analysis = self.analyze_connection_trend()
        
        report = {
            'timestamp': timestamp,
            'target_pid': self.target_pid,
            'process_status': process_info,
            'mysql_connections': mysql_data,
            'netstat_connections': netstat_connections,
            'trend_analysis': trend_analysis,
            'connection_history_count': len(self.connection_history)
        }
        
        return report
    
    def print_focused_report(self, report):
        """打印专项监控报告"""
        print(f"\n{'='*80}")
        print(f"🎯 run.py应用(PID {report['target_pid']})连接专项监控 - {report['timestamp']}")
        print(f"{'='*80}")
        
        # 进程状态
        if report['process_status']:
            proc = report['process_status']
            print(f"🐍 进程状态: {proc['status']} | 内存: {proc['memory_mb']:.1f}MB | CPU: {proc['cpu_percent']:.1f}%")
            print(f"📅 启动时间: {proc['create_time']} | 线程数: {proc['num_threads']}")
        
        # MySQL连接详情
        mysql = report['mysql_connections']
        if mysql:
            print(f"\n🗄️ MySQL连接分析:")
            print(f"   总连接数: {mysql.get('total_mysql_connections', 0)}")
            print(f"   APS连接数: {mysql.get('aps_connections', 0)}")
            print(f"   长连接数: {len(mysql.get('long_connections', []))}")
            
            print(f"\n📊 连接状态分布:")
            for state, count in mysql.get('state_distribution', {}).items():
                print(f"   {state}: {count}")
            
            print(f"\n⏱️ 连接时间分布:")
            for time_range, count in mysql.get('time_distribution', {}).items():
                print(f"   {time_range}: {count}")
        
        # 网络连接
        print(f"\n🌐 Netstat连接数: {report['netstat_connections']}")
        
        # 趋势分析
        trend = report['trend_analysis']
        if trend['trend'] != 'insufficient_data':
            trend_icon = "📈" if trend['trend'] == 'increasing' else "📉" if trend['trend'] == 'decreasing' else "➡️"
            memory_icon = "📈" if trend['memory_trend'] == 'increasing' else "📉" if trend['memory_trend'] == 'decreasing' else "➡️"
            
            print(f"\n📈 趋势分析:")
            print(f"   连接趋势: {trend_icon} {trend['trend']}")
            print(f"   内存趋势: {memory_icon} {trend['memory_trend']}")
            print(f"   当前连接: {trend['current_connections']}")
            print(f"   5分钟平均: {trend['avg_connections_last_5min']:.1f}")
            print(f"   连接波动: {trend['connection_variance']}")
        
        # 风险评估
        self.assess_risks(report)
        
        print(f"{'='*80}")
    
    def assess_risks(self, report):
        """风险评估"""
        mysql = report['mysql_connections']
        trend = report['trend_analysis']
        
        risks = []
        recommendations = []
        
        # 连接数风险
        aps_connections = mysql.get('aps_connections', 0)
        if aps_connections > 50:
            risks.append(f"🚨 APS连接数过高: {aps_connections}")
            recommendations.append("立即检查连接池配置和连接回收")
        elif aps_connections > 30:
            risks.append(f"⚠️ APS连接数偏高: {aps_connections}")
            recommendations.append("密切监控连接增长趋势")
        
        # 趋势风险
        if trend['trend'] == 'increasing':
            risks.append("📈 连接数呈增长趋势")
            recommendations.append("检查是否有连接泄漏")
        
        # 长连接风险
        long_conn_count = len(mysql.get('long_connections', []))
        if long_conn_count > 20:
            risks.append(f"⏱️ 长连接过多: {long_conn_count}")
            recommendations.append("检查连接回收机制")
        
        # 内存趋势
        if trend.get('memory_trend') == 'increasing':
            risks.append("💾 内存使用呈增长趋势")
            recommendations.append("监控是否存在内存泄漏")
        
        if risks:
            print(f"\n⚠️ 风险警告:")
            for risk in risks:
                print(f"   {risk}")
        
        if recommendations:
            print(f"\n💡 建议措施:")
            for rec in recommendations:
                print(f"   {rec}")
        
        if not risks:
            print(f"\n✅ 状态正常: 无发现风险")
    
    def save_detailed_report(self, report):
        """保存详细报告"""
        filename = f"run_py_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 添加历史数据
        report['connection_history'] = list(self.connection_history)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📋 run.py专项监控报告已保存: {filename}")
        return filename
    
    def start_focused_monitoring(self, duration_minutes=60):
        """启动专项监控"""
        logger.info(f"🎯 启动run.py应用专项监控 - PID {self.target_pid}")
        logger.info(f"⏰ 监控时长: {duration_minutes}分钟，每{self.check_interval}秒检查一次")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        check_count = 0
        
        try:
            while time.time() < end_time:
                check_count += 1
                
                # 检查进程是否还在运行
                try:
                    psutil.Process(self.target_pid)
                except psutil.NoSuchProcess:
                    logger.error(f"❌ 目标进程PID {self.target_pid}已停止")
                    break
                
                # 生成报告
                report = self.generate_focused_report()
                
                # 显示报告(每5次检查显示一次详细报告)
                if check_count % 5 == 1:
                    self.print_focused_report(report)
                else:
                    # 简化显示
                    mysql = report['mysql_connections']
                    proc = report['process_status']
                    trend = report['trend_analysis']
                    
                    conn_count = mysql.get('aps_connections', 0) if mysql else 0
                    memory_mb = proc['memory_mb'] if proc else 0
                    trend_icon = "📈" if trend['trend'] == 'increasing' else "📉" if trend['trend'] == 'decreasing' else "➡️"
                    
                    print(f"[{report['timestamp']}] 🎯 PID {self.target_pid}: {conn_count}连接 | {memory_mb:.1f}MB | {trend_icon}")
                
                # 每10次检查保存一次报告
                if check_count % 10 == 0:
                    filename = self.save_detailed_report(report)
                    logger.info(f"✅ 第{check_count}次检查完成，报告已保存: {filename}")
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
        except KeyboardInterrupt:
            logger.info("🛑 监控被用户中断")
        except Exception as e:
            logger.error(f"❌ 监控过程出错: {e}")
        finally:
            self.cleanup()
            
        logger.info(f"📊 run.py专项监控完成 - 总检查次数: {check_count}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self._mysql_connection:
                self._mysql_connection.close()
                self._mysql_connection = None
                logger.info("✅ 数据库连接已清理")
        except Exception as e:
            logger.error(f"清理失败: {e}")

def main():
    """主函数"""
    # 检查目标进程是否存在
    target_pid = 39168
    try:
        process = psutil.Process(target_pid)
        logger.info(f"✅ 找到目标进程: {process.name()} (PID: {target_pid})")
    except psutil.NoSuchProcess:
        logger.error(f"❌ 目标进程PID {target_pid}不存在")
        return
    
    monitor = RunPyConnectionMonitor(target_pid)
    monitor.start_focused_monitoring(60)  # 60分钟监控

if __name__ == "__main__":
    main()